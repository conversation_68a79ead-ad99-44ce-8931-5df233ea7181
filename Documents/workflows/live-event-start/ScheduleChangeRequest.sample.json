{"ExistingScheduleId": "GMS Schedule Id (match previous RequestorLiveEventScheduleId)", "RequestorId": "GMS Interpreter Id", "RequestorIdentity": "GMS", "RequestorLiveEventId": "GMS Game Id", "RequestorEventType": "game", "RequestorLiveEventScheduleId": "GMS Schedule Id", "WorkflowIntents": [{"LiveEventTime": "2021-05-12T02:02:39.015238-03:00", "WorkflowId": "Start", "ChannelId": "Aquila Channel Id", "ActorSpecificDetails": [{"ActorId": "Aquila Actor Id", "Data": {}}]}, {"LiveEventTime": "2021-05-12T09:22:58.8596176-03:00", "WorkflowId": "Stop", "ChannelId": "Aquila Channel Id", "ActorSpecificDetails": [{"ActorId": "Aquila Actor Id", "Data": {}}]}], "RequestId": "Guid.NewG<PERSON>()", "RequestorActorId": "<Ignored> GMS Interpreter Id", "LongRunningOperationId": "<Ignored>"}
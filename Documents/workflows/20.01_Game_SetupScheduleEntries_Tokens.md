# 20.01 Game - Setup Schedule Entries - Tokens

This describes how the GMS data will be parsed to gather all the input data required for all activities of all workflows needed to schedule a source to participate in a game.

## Data needed for SetupScheduleEntries

In [GmsGames.cs](../../src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/GMS/Entities/GmsGame.cs) we need:

gameId = `GmsGame.Id`
homeTeamAbbr =  `GmsGame.HomeTeam.Abbr`
awayTeamAbbr =  `GmsGame.AwayTeam.Abbr`
mediaId = `GmsGame.Medias[i].id`
encoder = `GmsGame.Medias[i].Schedules[j].Operations.encoders`

For each of the Medias which are relevant to the NextGen OTT platform.

> Dev Note: These are currently extracted in the GameRepository into a single class: IntentMetaData.cs, but it does not have all the data listed above.

## Data needed for Start

## Links

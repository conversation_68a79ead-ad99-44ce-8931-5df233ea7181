# Proposed initial data payloads for events and commands between the components for the live-event scenario

To unblock the team, we need some DTOs that contain our best guess of the data that needs to move between systems.

We need these before they are re-defined in the stories that will design the stores:

- [State Store](https://dev.azure.com/nbadev/DTC/_workitems/edit/6942)
- [Schedule Store](https://dev.azure.com/nbadev/DTC/_workitems/edit/6943)
- [Infrastructure Store](https://dev.azure.com/nbadev/DTC/_workitems/edit/7698)

## Models

The models can be found in: `./src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Models`

## Event Names

The EventGrid EventType constants can be found in: `./src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Common/EventTypes.cs`

## Queue Names

The Queue names can be found in: `./src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Common/QueueNames.cs`

# Seed Data Load Design

## Data Description

Below, “seed data” represents the data to be populated into the following Cosmos DB containers:
1. VideoPlatformChannel and 
1. VideoPlatformWorkflow 

The seed data is “like” country/region data which does not change “often”.

The most probable reasons for the seed data change are:
1. Software feature change, such as adding new features which may require seed data change - schema and/or data;
1. NBA video ingest path change such as multicast IP/encoder change. This could be rare.
1. Channel mapping changes. This could be rare.

Seed data change could be initiated by two different teams:
1. Dev team for adding or changing features;
1. NBA and/or MediaKind teams due to change of infrastructure or channel mapping. This case should be rare.

## Infrastructure or Software

The first question we need to address is whether we should treat this seed data as infrastructure or software.

Proposal: treat it as software instead of infrastructure.

Why?
1. Potential change with code: The seed data may or may not change with code change. The change is more aligned with code/feature change than infrastructure change. The Cosmos DB containers may not need to change for a long time. But the seed data may change when features are added to code.
1. Consistency with code: It is critical that the seed data should be precisely consistent with the version of the code deployed.
1. Any seed data change, even if it is rare, it is prudent to go through full cycle of testing the software against the updated seed data and software/seed data deployment.

## Source Data Format

Regardless how the source data is loaded into Cosmos DB containers, we need to store the source seed data in an easy to manage format. We propose using Excel (CSV) files and use src/Database/Seed folder, one for each container.

The advantages of using Excel (CSV) files are:
1. Easy for data collection and updates;
1. Convenient for source control;

CSV files:
1. [Seed data for VideoPlatformChannel](../src/Database/Seed/video_platform_channel.csv);
1. [Seed data for VideoPlatformWorkflow](../src/Database/Seed/video_platform_workflow.csv);

## Source Data Versions

We may need to handle more than one versions of such seed data (CSV files). For example, there might be more than one instances of Aquila Streaming, one for production and one for dev/test. In different Aquila Streaming environments, there would be separate set of Aquila channels and sources with corresponding IDs.

In this case, we can append file names with _prod or _dev. The CD pipeline can then switch depending on target environment.

## Some Static IDs Involved

In the above CSV files, some GUIDs and values are static. They are defined in the code and should be treated as constants.

| Description | Static ID | Static Name |
| --- | --- | --- |
| Organization ID for NBA | 979e3a53-d290-428c-82f2-5a4ea667bfce |    | 
| VideoPlatformWorkflow ID | 819b9d6d-538a-46d8-8672-************ | Normal Start |
| VideoPlatformWorkflow ID | b0885cd8-40e0-475a-affe-6bfd14440001 | Normal Stop | 
| VideoPlatformActor ID | c983f428-a72f-47fe-a55f-f613d3490000 | GMS |
| VideoPlatformActor ID | 76c8fd64-d1a4-4d49-bb23-65df92680001 | Aquila Channel | 

## Data Loading 

A console application
1. Reading the CSV files;
1. Create corresponding objects defined by domain entities (VideoPlatformChannel, VideoPlatformActor, VideoPlatformWorkflow). This will ensure the schema in code stays consistent with the schema in Cosmos DB;
1. Upsert into Cosmos DB containers;
1. This console app is then invoked in CD pipeline.

## Seed Data Management Workflow

NBA has indicated that there is no need of UX app or infrastructure to allow operations team to change the “seed data”. For now it is sufficient to treat seed data as part of code. No such workflow is needed. Of course this could change in the future.

## Future Considerations

Not being in scope of this sprint, future considerations include

1. A separate pipeline just for deploying seed data since there might be scenarios that we do not need to re-deploy code, only need to deploy a new version of seed data.
1. Seed data versioning, in accordance with software versioning. This versioning is different from versions of seed data for different environments as mentioned above.
1. In the future, NBA might need a separate workflow which allows operations team to manually update seed data and then releases the seed data (only) into production. This workflow would involve UX app, human review and approval, before pushing into Cosmos DB in production.




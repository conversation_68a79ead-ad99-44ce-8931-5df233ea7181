# Working Agreement

A **Working Agreement** is a living document representing the principles and expected behavior of everyone involved in the project; it is not meant to be exhaustive nor complete. The team should be accountable to these standards and revisit, review, and revise as needed. The agreement is signed off by everyone.

## Table Of Contents

- [General](#general)
- [Communication](#communication)
- [Work Life Balance](#work-life-balance)
- [Quality and not Quantity](#quality-and-not-quantity)
- [Ceremonies](#ceremonies)
- [Backlog management](#backlog-management)
- [Code management](#code-management)

## General

- We work as one team towards a common goal and clear scope
- We make sure everyones voice is heard, listened to
- We show all team members equal respect
- We pair-program as often as possible
- We make sure to spread our expertise and skills in the team, so no single person is relied on for one skill
- All times below are listed in CST

## Communication

- We communicate all information relevant to the team through the [Teams channel](https://teams.microsoft.com/l/channel/19%3a41a9d85ed60642df947923cc22a89da6%40thread.skype/Workstream%25201%2520Dev%2520Crew%25202?groupId=df90c237-b6aa-47bf-8de4-19b040b13d46&tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47)
- We add all research results, design documents and other technical documentation to the [project repository](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2FREADME.md) through PRs

## Work Life Balance

- Our office hours, when we can expect to collaborate via Microsoft Teams, phone or face-to-face are Monday to Friday 9AM - 5PM CST
- Considering the global nature of the Dev Crew, some of us will have to be flexible with earlier mornings and evenings. We try to minimize disruption as much as possible.
- We are not expected to answer emails past 6PM our time, on weekends or when we are on holidays or vacation.
- We make a best effort attempt to have no-meeting Fridays. No stand-ups will be scheduled for example.
- We record meetings when possible, so that team members who could not attend live can listen later.

## Quality and not Quantity

- We agree on a [Definition of Done](https://dev.azure.com/nbadev/DTC/_git/DTCWiki?path=%2FProgram-Management%2FDefinition-of-Done-(DoD).md) for our user story's and sprints and live by it.
- We follow engineering best practices like the [CSE Code With Engineering Playbook](https://github.com/microsoft/code-with-engineering-playbook) and the practices outlined in the NBA Project's official [Engineering Core Guidance](https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/133/Engineering-Core-Guidance) wiki.

## Ceremonies

### Project Increments

We entertain two weeks sprints, running from Wednesday to Tuesday. There will be 5 sprints total in Project Increment 3 (PI-3):

| Sprint | Start |
|-|-|
| PI-3.1 | 1/13/2021 |
| PI-3.2 | 1/27/2021 |
| PI-3.3 | 2/10/2021 |
| PI-3.4 | 2/24/2021 |
| PI-3.5 | 3/10/2021 |

### Scrum Rhythm

| Activity | When | Duration | Who | Accountable | Goal |
|-|-|-|-|-|-|
| [Project Standup](https://github.com/microsoft/code-with-engineering-playbook/blob/e6c11d8bccc2ac49567a55fa2297333072605770/stand-ups/readme.md) | Mon-Thu 9.30 AM | 15 min | Everyone | Scrum Master | What has been accomplished, next steps, blockers |
| Sprint Review | Tuesday 9AM | 1 hour | Everyone | Dev Lead | Present work done and sign off on user story completion |
| [Sprint Retro](https://github.com/microsoft/code-with-engineering-playbook/blob/e6c11d8bccc2ac49567a55fa2297333072605770/retrospectives/readme.md) | Thursday 9AM | 1 hour | Everyone | Scrum Master | Dev Teams shares learnings and what can be improved |
| [Sprint Planning](https://github.com/microsoft/code-with-engineering-playbook/blob/e6c11d8bccc2ac49567a55fa2297333072605770/sprint-planning/readme.md) | Wednesday 9AM | 1.5 hour | Everyone | Scrum Master | Size and plan user stories for the sprint |
| Task Creation | After Sprint Planning | - | Dev Team | Dev Lead | Create tasks to clarify and determine velocity. |
| Backlog grooming | Tuesday 10AM | 1 hour | Dev lead, PO | PO | Prepare for next sprint and ensure that stories are ready for next sprint. |

## Backlog Management

- We work together on a [Definition of Ready](https://dev.azure.com/nbadev/DTC/_git/DTCWiki?path=%2FProgram-Management%2FDefinition-of-Ready-(DoR).md&_a=preview) and all user stories assigned to a sprint need to follow this
- We communicate what we are working on through the board
- We assign ourselves a task when we are ready to work on it (not before) and move it to active
- We capture any work we do related to the project in a user story/task
- We close our tasks/user stories only when they are done (as described in the [Definition of Done](https://dev.azure.com/nbadev/DTC/_git/DTCWiki?path=%2FProgram-Management%2FDefinition-of-Done-(DoD).md))
- We work with the Scrum Master or PM if we want to add a new user story to the backlog
- If we add new tasks to the board, we make sure it matches the acceptance criteria of the user story (to avoid scope creep). If it doesn't match the acceptance criteria we should discuss with the Scrum Master to see if we need a new user story for the task or if we should adjust the acceptance criteria.

## Code Management

- We follow the code management guidelines documented in the [NBA's Engineering Core Guidance](https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/133/Engineering-Core-Guidance) wiki
- We follow the git flow branch naming convention for branches and identify the developer's alias and the task number e.g. `feature/dev_alias/123-add-working-agreement`. This is also reflected on the [NBA AzDO](https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/143/Branch-Policies).
- We merge all code into [Main through PRs](https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/106/Pull-Request-Code-Review-(PR)).
- All PRs are ideally reviewed by one person from MCS and one from CSE (for knowledge transfer and to ensure code and security standards are met).
- Comments on PRs need to be resolved by the author of the comment.
- We can make blocking comments in PRs if issues are deemed critical.
- If you pair program, your partner can not be a reviewer.
- We always review existing PRs before starting work on a new task.
- We look through open PRs at the end of stand-up to make sure all PRs have reviewers.
- We treat documentation as code and apply the same [standards to Markdown](https://github.com/microsoft/code-with-engineering-playbook/blob/e6c11d8bccc2ac49567a55fa2297333072605770/code-reviews/recipes/Markdown.md) as code.

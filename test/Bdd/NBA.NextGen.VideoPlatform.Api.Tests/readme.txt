BDD.CORE.API
------------

- A<PERSON><PERSON><PERSON>Ops Sync
  - Use [Spex](https://vamsitp.github.io/spexdocs/)

- Code files
	- `Core\AppTestBase.cs`: Used for "Hooks"

- Configuration
	- `app.config`: Change values under `<spex>` node for Spex (AzDevOps-Sync)
	- Make sure the values of DefaultAssignedTo (in .config) / @owner tag (in .feature) are valid.
		- e.g.: The alias vamsitp(@microsoft.com) is different than vamsi.tp(@microsoft.com) - though both are valid aliases. AzDevOps only honors that one that was added to the account

- Main classes to use
    - `ApiStepDefinitionBase`: To add additional functionality, inherit this class and add/override methods
    - `ApiExecutor`: To add additional functionality, inherit this class and add/override methods

- Scenario specific Packages
    - `Bdd.Core.Web`: For Web Tests
    - `Bdd.Core.Api`: For Api Tests

- Steps to setup automation framework
    - Clone the repo for videoplatform - https://dev.azure.com/nbadev/DTC/_git/VideoPlatform
    - Once cloned, open the repo in visual studio. Navigate to the root folder and find file with name as appsettings.json.
        - In appsettings.json, do find and replace, find with "#{WorkStreamName}#" and replace it with "Videoplatform"
    - For each environment, you should be able to see runsetting file with env name in the file name.
        - To run for a specific environment, open the run settin file that contains the name of the environment.
        - Find variables - keyVault:ClientId and keyVault:ClientSecret, replace them with client id and secret which you can find in the keyvaults mentioned in the variable keyVault:VaultUri.
        - Depending on the network you might need to connect to NBA vpn or whitelist the IPs in the keyvault for access.
        - To select the env runsetting file, go to Test -> configure run settings -> solution wide run settings and select your runsetting file.
        - You should be now all set to execute, pick any test case from the test explorer which you can find under Test - Test explorer and run it.

- Import links
    - Test automation tags - https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/382/Test-Automation-Tags
    - Test pipelines know how - https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/398/WS1-DC3-Test-Pipelines
    - Test Automation keyvault access - https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/605/WS1-Test-Automation-Key-Vault-Access-Workflow
    - WS1 Testing KT links - https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/2064/WS1-Testing-KT-Links
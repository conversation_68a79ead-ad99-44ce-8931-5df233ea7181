// "//-----------------------------------------------------------------------".
// <copyright file="ApiStepDefinitionBase.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;

    using Bogus;

    using Flurl.Http;

    using Microsoft.WindowsAzure.Storage.Blob;

    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities.Models.Prisma;
    using NBA.NextGen.VideoPlatform.Api.Tests.Executors;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    using ApiStepDefinitionBaseClass = global::Bdd.Core.Api.StepDefinitions.ApiStepDefinitionBase;

    /// <summary>
    /// ApiStepDefinitionBase step-definition class.
    /// </summary>
    [Binding]
    public class ApiStepDefinitionBase : ApiStepDefinitionBaseClass
    {
        private readonly CosmosDBDataSource cosmosDBDataSource = new();

        private readonly BlobStorageDataSource blobDataSource = new();

        private readonly EntityData entityData = new();

        private readonly PrismaHelper prismaHelper = new();

        protected string ServiceBusMessageSchemasJsonFilePath => @"TestData\Input\ServiceBusMessageSchemas.json";

        protected string GmsGameJsonFilePath => @"TestData\Input\GmsGame.json";

        protected string GmsEventJsonFilePath => @"TestData\Input\GmsEvent.json";

        protected string AquilaChannelStubBlobName => "Clients/Aquila/Channels/data.json";

        protected string TVPStubBlobName => "Clients/Aquila/Tvp/data.json";

        protected string PrismablobName => "Clients/Prisma/Worker/data.json";

        protected string PlayoutChannelStubBlobName => "Clients/Playout/Playout/data.json";

        protected string GmsTVPStubFilePath => @"TestData\Input\TVPStub.json";

        protected string ApimHeader => $"subscription-key={KeyVaultSecrets.SubscriptionKey}";

        protected string GmsWatchDogLeagueModificationScriptPath => @"Scripts\AddLeagueToGmsWatchDog.ps1";

        protected Dictionary<string, object> ApimHeaders => new() { { Constants.OcpApimSubscriptionKey, KeyVaultSecrets.SubscriptionKey } };

        protected Dictionary<string, object> TVPApimHeaders => new() { { "subscription-key", KeyVaultSecrets.SubscriptionKey } };

        protected Dictionary<string, object> GmsInterpreterEventGridTriggerFunctionsKey => new() { { "x-functions-key", KeyVaultSecrets.GmsInterpreterEventGridTriggerFunctionsKey } };

        protected Dictionary<string, object> AquilaactorFunctionHeader => new() { { "x-functions-key", KeyVaultSecrets.AquilaActorFunctionKey } };

        protected Dictionary<string, object> GmsFunctionHeader => new() { { "x-functions-key", KeyVaultSecrets.GmsFunctionKey } };

        protected Dictionary<string, object> SimulatorApiHeader => new() { { "x-subscription-key", string.Empty } };

        protected Dictionary<string, object> AquilaFunctionHeader => new() { { "x-functions-key", KeyVaultSecrets.AquilaFunctionKey } };

        protected Dictionary<string, object> SchedulerFunctionHeader => new() { { "x-functions-key", KeyVaultSecrets.SchedulerFunctionKey } };

        protected Dictionary<string, object> OrchestrationApimHeader => new() { { "x-subscription-key", KeyVaultSecrets.SubscriptionKey } };

        protected string EventGridFunctionGenericEndpoint => "{0}runtime/webhooks/EventGrid?functionName={1}&code={2}";

        protected string GmsInterpreterScheduleChangeEventGridFunctionEndpoint => string.Format(this.EventGridFunctionGenericEndpoint, Configs.GmsInterpreterFunctionAppBaseUrl, "ScheduleChangeEventGridFunction", KeyVaultSecrets.GmsInterpreterEventGridTriggerFunctionsKey);

        protected string GmsInterpreterTeamZipsChangeEventGridFunctionEndpoint => string.Format(this.EventGridFunctionGenericEndpoint, Configs.GmsInterpreterFunctionAppBaseUrl, "TeamZipsChangeEventGridFunction", KeyVaultSecrets.GmsInterpreterEventGridTriggerFunctionsKey);

        protected string SchedulePath => @"TestData\Input\VideoPlatformSchedule.json";

        protected string SourcePath => @"TestData\Input\VideoPlatformSource.json";

        protected string TemplatePath => @"TestData\Input\VideoPlatformTemplate.json";

        protected string StreamMarkerRequestPath => @"TestData\Input\StreamMarkerRequest.json";

        /// <summary>
        /// Sends a GET request to the specified API endpoint & returns the response.
        /// </summary>
        /// <param name="endpoint">API URL.</param>
        /// <param name="additionalHeaders">Request Headers.</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> SendGetRequestAsync(string endpoint, Dictionary<string, object> additionalHeaders = null)
        {
            dynamic response;
            try
            {
                response = await this.ApiExecutor.GetResponseAsync(endpoint, this.UserDetails, additionalHeaders: additionalHeaders).ConfigureAwait(false);
            }
            catch (FlurlHttpException ex)
            {
                Logger.Log(NLog.LogLevel.Error, ex.Message, ex);
                var responseMessage = await ex.Call.Response.ResponseMessage.Content.ReadAsStringAsync().ConfigureAwait(false);
                Logger.Log(NLog.LogLevel.Error, responseMessage, ex);
                response = ex.Call.Response;
            }

            this.ScenarioContext["LastApiCallResponse"] = response;
            return response;
        }

        /// <summary>
        /// Sends a POST request to the specified API endpoint & returns the response.
        /// </summary>
        /// <param name="endpoint">API URL.</param>
        /// <param name="content">Request Body.</param>
        /// <param name="additionalHeaders">Request Headers.</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> SendPostRequestAsync(string endpoint, object content, Dictionary<string, object> additionalHeaders = null)
        {
            dynamic response;
            try
            {
                response = await this.PostWithResponseAsync(endpoint, content, additionalHeaders).ConfigureAwait(false);
            }
            catch (FlurlHttpException ex)
            {
                response = ex.Call.Response;
            }

            this.ScenarioContext["LastApiCallResponse"] = response;
            return response;
        }

        /// <summary>
        /// Sends a PUT request to the specified API endpoint & returns the response.
        /// </summary>
        /// <param name="endpoint">API URL.</param>
        /// <param name="content">Request Body.</param>
        /// <param name="additionalHeaders">Request Headers.</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> SendPutRequestAsync(string endpoint, object content, Dictionary<string, object> additionalHeaders = null)
        {
            dynamic response;
            try
            {
                response = await this.PutWithResponseAsync(endpoint, content, additionalHeaders).ConfigureAwait(false);
            }
            catch (FlurlHttpException ex)
            {
                response = ex.Call.Response;
            }

            this.ScenarioContext["LastApiCallResponse"] = response;
            return response;
        }

        /// <summary>
        /// Sends a PATCH request to the specified API endpoint & returns the response.
        /// </summary>
        /// <param name="endpoint">API URL.</param>
        /// <param name="content">Request Body.</param>
        /// <param name="additionalHeaders">Request Headers.</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> SendPatchRequestAsync(string endpoint, object content, Dictionary<string, object> additionalHeaders = null)
        {
            dynamic response;
            try
            {
                additionalHeaders.Keys.ToList().ForEach(x =>
                {
                    endpoint.WithHeader(x, additionalHeaders[x]);
                });

                var flurlResponse = await endpoint.PatchJsonAsync(content).ConfigureAwait(false);
                response = flurlResponse.ResponseMessage;
            }
            catch (FlurlHttpException ex)
            {
                response = ex.Call.Response;
            }

            this.ScenarioContext["LastApiCallResponse"] = response;
            return response;
        }

        /// <summary>
        /// Sends a DELETE request to the specified API endpoint & returns the response.
        /// </summary>
        /// <param name="endpoint">API URL.</param>
        /// <param name="additionalHeaders">Request Headers.</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> SendDeleteRequestAsync(string endpoint, Dictionary<string, object> additionalHeaders = null)
        {
            dynamic response;
            try
            {
                response = await this.DeleteWithResponseAsync(endpoint, additionalHeaders).ConfigureAwait(false);
            }
            catch (FlurlHttpException ex)
            {
                response = ex.Call.Response;
            }

            this.ScenarioContext["LastApiCallResponse"] = response;
            return response;
        }

        /// <summary>
        /// Reads the stream content from blobs and returns as a string.
        /// </summary>
        /// <param name="ticks">ticks.</param>
        /// <returns>string.</returns>
        public async Task<string> GetEventsContentFromBlobAsync(object ticks = null)
        {
            dynamic blobs = null;
            var blobContainer = this.blobDataSource.Read(Constants.AutomationTestStubContainerName, keyPrefix: Constants.EventsStorage);
            if (ticks != null)
            {
                blobs = (await this.blobDataSource.ReadAll(blobContainer).ConfigureAwait(false)).Where(x => x.Properties.Created.Value.Ticks >= long.Parse(ticks.ToString()) && x.Name != Constants.AutomationTestStubBlobName);
            }
            else
            {
                blobs = (await this.blobDataSource.ReadAll(blobContainer).ConfigureAwait(false)).Where(x => (DateTime.UtcNow - x.Properties.Created.Value.UtcDateTime).TotalMinutes < 5 && x.Name != Constants.AutomationTestStubBlobName);
            }

            var eventsArray = new JArray();
            foreach (var blob in blobs)
            {
                var blobName = blob.Name;
                var blobContent = await this.GetStringContentFromBlobAsync(Constants.AutomationTestStubContainerName, blobName, Constants.EventsStorage).ConfigureAwait(false);
                eventsArray.Add(JObject.Parse(blobContent));
            }

            return eventsArray.ToString();
        }

        /// <summary>
        /// Reads the stream content from specified blob & returns as a string.
        /// </summary>
        /// <param name="containerName">Blob container name.</param>
        /// <param name="blobName">Blob bame or path.</param>
        /// <param name="keyPrefix">Blob storage key prefix.</param>
        /// <returns>String. Blob content.</returns>
        public async Task<string> GetStringContentFromBlobAsync(string containerName, string blobName, string keyPrefix = "")
        {
            dynamic blob;
            if (string.IsNullOrEmpty(keyPrefix))
            {
                blob = await this.blobDataSource.ReadAsync(containerName, blobName).ConfigureAwait(false);
            }
            else
            {
                blob = await this.blobDataSource.ReadAsync(containerName, blobName, keyPrefix: keyPrefix).ConfigureAwait(false);
            }

            return UtilityMethods.StreamToString(blob);
        }

        /// <summary>
        /// Uploads string content to blob by converting to stream.
        /// </summary>
        /// <param name="containerName">Blob container name.</param>
        /// <param name="blobName">Blob bame or path.</param>
        /// <param name="content">Blob string content to upload.</param>
        /// <returns>Task.</returns>
        public async Task UploadStringContentToBlobAsync(string containerName, string blobName, string content)
        {
            using var blob = UtilityMethods.StringToStream(content);
            await this.blobDataSource.UploadAsync(containerName, blobName, blob).ConfigureAwait(false);
        }

        /// <summary>
        /// Sends a POST request to the trigger event-grid triggered function.
        /// </summary>
        /// <param name="functionEndpoint">Function URL.</param>
        /// <param name="eventData">data property content present in event.</param>
        /// <param name="eventTopicName">Event topic name. Defaults to "".</param>
        /// <param name="eventSubject">Event subject name. Defaults to "".</param>
        /// <returns>API Response.</returns>
        public async Task<dynamic> CallEventGridTriggerFunctionAsync(string functionEndpoint, JToken eventData, string eventTopicName = "", string eventSubject = "")
        {
            var headers = new Dictionary<string, object> { { Constants.ContentType, Constants.JsonBatchHttpRequestMediaType }, { "aeg-event-type", "Notification" } };
            var eventObject = new JObject
            {
                ["id"] = Guid.NewGuid().ToString(),
                ["topic"] = eventTopicName,
                ["subject"] = eventSubject,
                ["data"] = eventData,
                ["eventType"] = eventSubject,
                ["eventTime"] = DateTime.UtcNow.ToString(Constants.DateTimeStringFormat2, CultureInfo.InvariantCulture),
                ["metadataVersion"] = "1",
                ["dataVersion"] = "1.0",
            };

            return await this.SendPostRequestAsync(functionEndpoint, eventObject, headers).ConfigureAwait(false);
        }

        /// <summary>
        /// Calls EventGrid Trigger Function And Check IfSuccess Async.
        /// </summary>
        /// <param name="endpoint">function url.</param>
        /// <param name="id">Id.</param>
        /// <param name="type">Event type.</param>
        /// <param name="contentChangeType">contentChangeType.</param>
        /// <returns>Task.</returns>
        protected async Task CallEventGridTriggerFunctionAndCheckIfSuccessAsync(string endpoint, string id, string type, string contentChangeType = "")
        {
            var contentChangeTypeModified = string.IsNullOrEmpty(contentChangeType) ? "none" : contentChangeType;
            var response = await this.CallEventGridTriggerFunctionAsync(endpoint, JObject.Parse("{ \"Id\" : \"" + id + "\", \"Type\" : \"" + type + "\", \"ContentChangeType\" : \"" + contentChangeTypeModified + "\"}")).ConfigureAwait(false);
            Assert.AreEqual(Constants.Accepted, response.StatusCode.ToString());
            await Task.Delay(2000).ConfigureAwait(false);
        }

        /// <summary>
        /// Upsert New Entity In Stub.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="teamInfo">team info.</param>
        /// <param name="isUpdate">is update.</param>
        /// <param name="property">property.</param>
        /// <returns>required details.</returns>
        protected async Task<(int SeasonIndex, string UpdatedTime, string EntityName, string EntityId)> UpsertNewEntityInStub(string entity, Dictionary<string, string> teamInfo = null, bool isUpdate = false, string property = null)
        {
            Faker faker = new();
            string cityName = faker.Address.City();
            bool isTeamZipEntity = entity.ContainsIgnoreCase("team");
            string idpropertyName = isTeamZipEntity ? "teamId" : "id";
            (int SeasonIndex, string UpdatedTime, string EntityName, string EntityId) requiredData;
            var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
            requiredData.UpdatedTime = updateTime;
            requiredData.EntityName = entityName;
            JObject entityObject = this.entityData.GetEntityData(entity);
            if (!isUpdate)
            {
                if (entity.ContainsIgnoreCase("team"))
                {
                    string codeAndName, city, abbr;
                    if (teamInfo != null)
                    {
                        requiredData.EntityId = teamInfo["Id"];
                        codeAndName = teamInfo["Name"];
                        city = teamInfo["City"];
                        abbr = teamInfo["Abbr"];
                    }
                    else
                    {
                        requiredData.EntityId = UtilityMethods.GetRandomNumber(1, 10000).ToString();
                        codeAndName = requiredData.EntityName;
                        city = cityName;
                        abbr = cityName.ToUpper().Substring(0, 2);
                    }

                    entityObject["teamId"] = int.Parse(requiredData.EntityId);
                    entityObject["code"] = cityName;
                    entityObject["name"] = cityName;
                    entityObject["city"] = cityName;
                    entityObject["abbr"] = cityName.ToUpper().Substring(0, 2);
                    requiredData.EntityName = cityName;
                    this.ScenarioContext["TeamZipsDocumentCreatedInStub"] = entityObject;
                }
                else
                {
                    requiredData.EntityId = this.GetUniqueGameId();
                    entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString(Constants.DateTimeStringFormat2)).ToUnixTimeMilliseconds();
                    entityObject["id"] = requiredData.EntityId;
                    entityObject["location"]["name"] = requiredData.EntityName;
                    entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString(Constants.DateTimeStringFormat2)).ToUnixTimeMilliseconds();
                    switch (property)
                    {
                        case "division":
                            entityObject["awayTeam"]["division"] = null;
                            entityObject["homeTeam"]["division"] = null;
                            break;
                        case "operationsId":
                            entityObject["media"][0]["schedules"][0]["operations"]["id"] = null;
                            break;
                    }
                }

                this.ScenarioContext["entityId"] = requiredData.EntityId;
                entityObject["updated"] = requiredData.UpdatedTime;
                var data = await this.UpsertObjectToListAndUpdateStub(entity, entityObject, idpropertyName).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
                this.ScenarioContext["GmsEntityJson"] = entityObject;
            }
            else
            {
                var data = await this.UpsertObjectToListAndUpdateStub(entity, null, idpropertyName, true).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
                requiredData.UpdatedTime = data.UpdatedTime;
                requiredData.EntityId = data.UpdatedEntityId;
                requiredData.EntityName = data.EntityName;
            }

            return requiredData;
        }

        /// <summary>
        /// Upsert New Entity with dynamic locations In Stub.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="isUpdate">is update.</param>
        /// <returns>required details.</returns>
        protected async Task<(int SeasonIndex, string UpdatedTime, string EntityName, string EntityId)> UpsertNewEntityInStubWithLocations(string entity, bool isUpdate = false)
        {
            Dictionary<string, string> locationsDict = new Dictionary<string, string>();
            bool isTeamZipEntity = entity.ContainsIgnoreCase("team");
            string idpropertyName = isTeamZipEntity ? "teamId" : "id";
            (int SeasonIndex, string UpdatedTime, string EntityName, string EntityId) requiredData;
            var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
            requiredData.UpdatedTime = updateTime;
            requiredData.EntityName = entityName;
            JObject entityObject = this.entityData.GetEntityData(entity);
            if (!isUpdate)
            {
                var medias = entityObject["media"];
                var nssMedia = medias.SingleOrDefault(x => x["mediaType"]["name"].ToString().ContainsIgnoreCase(Constants.NSSIdentifier));
                this.ScenarioContext["mediaId"] = nssMedia["id"].ToString();
                requiredData.EntityId = this.GetUniqueGameId();
                entityObject["id"] = requiredData.EntityId;
                if (entity.EqualsIgnoreCase(nameof(CosmosContainers.GmsEvent)))
                {
                    this.ScenarioContext["eventId"] = entityObject["id"].ToString();
                }

                entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString()).ToUnixTimeMilliseconds();
                entityObject["location"]["LocationId"] = $"{Constants.AutomationIdentifier}loc{UtilityMethods.GetRandomNumber(100, 10000).ToString()}";
                entityObject["location"]["name"] = $"{Constants.AutomationIdentifier}loc{UtilityMethods.GetRandomNumber(100, 10000).ToString()}";
                Faker faker = new();
                entityObject["location"]["city"] = faker.Address.City();
                entityObject["location"]["countryCode"] = faker.Address.CountryCode();
                entityObject["location"]["postal"] = faker.Address.ZipCode();
                entityObject["location"]["stateOrProvince"] = faker.Address.State();
                entityObject["location"]["street"] = faker.Address.StreetAddress();
                entityObject["location"]["timeZone"] = faker.Date.TimeZoneString();
                entityObject["location"]["locationType"] = "Stadium";
                locationsDict.Add("LocationId", entityObject["location"]["LocationId"].ToString());
                locationsDict.Add("Name", entityObject["location"]["name"].ToString());
                locationsDict.Add("City", entityObject["location"]["city"].ToString());
                locationsDict.Add("CountryCode", entityObject["location"]["countryCode"].ToString());
                locationsDict.Add("Postal", entityObject["location"]["postal"].ToString());
                locationsDict.Add("StateOrProvince", entityObject["location"]["stateOrProvince"].ToString());
                locationsDict.Add("Street", entityObject["location"]["street"].ToString());
                locationsDict.Add("TimeZone", entityObject["location"]["timeZone"].ToString());
                locationsDict.Add("LocationType", entityObject["location"]["locationType"].ToString());

                this.ScenarioContext["entityId"] = requiredData.EntityId;
                this.ScenarioContext["locationDetails"] = locationsDict;
                entityObject["updated"] = requiredData.UpdatedTime;
                var data = await this.UpsertObjectToListAndUpdateStub(entity, entityObject, idpropertyName).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
            }
            else
            {
                var stubName = entity.Replace(Constants.Gms, string.Empty, StringComparison.OrdinalIgnoreCase);
                var entityPropertyNameInJson = $"{stubName.ToLower()}s";
                var blobName = $"Clients/Gms/{stubName}s/data.json";
                string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobDataSource, Configs.StubContainer, blobName).ConfigureAwait(false);
                JObject content = JObject.Parse(jsonContent);
                var gamesObj = JArray.Parse(content[entityPropertyNameInJson].ToString()).SingleOrDefault(x => x["id"].ToString().Equals("202200", StringComparison.Ordinal))[entityPropertyNameInJson].ToString();
                entityObject = JObject.Parse(JArray.Parse(gamesObj).SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["entityId"].ToString(), StringComparison.Ordinal)).ToString());
                entityObject["location"]["LocationId"] = ((Dictionary<string, string>)this.ScenarioContext["locationDetails"])["LocationId"].ToString();
                entityObject["location"]["name"] = $"{Constants.AutomationIdentifier}loc{UtilityMethods.GetRandomNumber(100, 10000).ToString()}";
                Faker faker = new();
                entityObject["location"]["city"] = faker.Address.City();
                entityObject["location"]["countryCode"] = faker.Address.CountryCode();
                entityObject["location"]["postal"] = faker.Address.ZipCode();
                entityObject["location"]["stateOrProvince"] = faker.Address.State();
                entityObject["location"]["street"] = faker.Address.StreetAddress();
                entityObject["location"]["timeZone"] = faker.Date.TimeZoneString();
                entityObject["location"]["locationType"] = "Stadium";
                locationsDict.Add("LocationId", entityObject["location"]["LocationId"].ToString());
                locationsDict.Add("Name", entityObject["location"]["name"].ToString());
                locationsDict.Add("City", entityObject["location"]["city"].ToString());
                locationsDict.Add("CountryCode", entityObject["location"]["countryCode"].ToString());
                locationsDict.Add("Postal", entityObject["location"]["postal"].ToString());
                locationsDict.Add("StateOrProvince", entityObject["location"]["stateOrProvince"].ToString());
                locationsDict.Add("Street", entityObject["location"]["street"].ToString());
                locationsDict.Add("TimeZone", entityObject["location"]["timeZone"].ToString());
                locationsDict.Add("LocationType", entityObject["location"]["locationType"].ToString());

                this.ScenarioContext["locationDetails"] = locationsDict;
                entityObject["updated"] = requiredData.UpdatedTime;
                var data = await this.UpsertObjectToListAndUpdateStub(entity, entityObject, idpropertyName).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
                requiredData.SeasonIndex = data.SeasonIndex;
                requiredData.UpdatedTime = data.UpdatedTime;
                requiredData.EntityId = data.UpdatedEntityId;
                requiredData.EntityName = data.EntityName;
            }

            return requiredData;
        }

        /// <summary>
        /// Upsert New Entity In Stub.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="fields">is fields.</param>
        /// <param name="isUpdate">is update.</param>
        /// <returns>required details.</returns>
        protected async Task<(int SeasonIndex, string UpdatedTime, string EntityName, string EntityId)> UpsertNewEntityInStubWithRequiredFieldValues(string entity, Dictionary<string, object> fields, bool isUpdate = false)
        {
            bool isTeamZipEntity = entity.ContainsIgnoreCase("team");
            string idpropertyName = isTeamZipEntity ? "teamId" : "id";
            (int SeasonIndex, string UpdatedTime, string EntityName, string EntityId) requiredData;
            var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
            requiredData.UpdatedTime = updateTime;
            requiredData.EntityName = entityName;
            JObject entityObject = this.entityData.GetEntityData(entity);
            if (!isUpdate)
            {
                var medias = entityObject["media"];
                var nssMedia = medias.SingleOrDefault(x => x["mediaType"]["name"].ToString().ContainsIgnoreCase(Constants.NSSIdentifier));
                this.ScenarioContext["mediaId"] = nssMedia["id"].ToString();
                requiredData.EntityId = this.GetUniqueGameId();
                entityObject["id"] = requiredData.EntityId;
                entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString()).ToUnixTimeMilliseconds();
                fields.ToList().ForEach(x =>
                {
                    entityObject.Remove(x.Key);
                    var field = JObject.Parse(@"{" + x.Key + ":" + x.Value + "}");
                    entityObject.Add(x.Key, JToken.Parse(x.Value.ToString()));
                });

                this.ScenarioContext["entityId"] = requiredData.EntityId;
                this.ScenarioContext["locationDetails"] = this.GetLocationDetails(entityObject);
                entityObject["updated"] = requiredData.UpdatedTime;
                var data = await this.UpsertObjectToListAndUpdateStub(entity, entityObject, idpropertyName).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
            }
            else
            {
                var stubName = entity.Replace(Constants.Gms, string.Empty, StringComparison.OrdinalIgnoreCase);
                var entityPropertyNameInJson = $"{stubName.ToLower()}s";
                var blobName = $"Clients/Gms/{stubName}s/data.json";
                string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobDataSource, Configs.StubContainer, blobName).ConfigureAwait(false);
                JObject content = JObject.Parse(jsonContent);
                var gamesObj = JArray.Parse(content[entityPropertyNameInJson].ToString()).SingleOrDefault(x => x["id"].ToString().Equals("202200", StringComparison.Ordinal))[entityPropertyNameInJson].ToString();
                entityObject = JObject.Parse(JArray.Parse(gamesObj).SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["entityId"].ToString(), StringComparison.Ordinal)).ToString());
                entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString()).ToUnixTimeMilliseconds();
                fields.ToList().ForEach(x =>
                {
                    entityObject.Remove(x.Key);
                    var field = JObject.Parse(@"{" + x.Key + ":" + x.Value + "}");
                    entityObject.Add(x.Key, JToken.Parse(x.Value.ToString()));
                });
                entityObject["updated"] = requiredData.UpdatedTime;
                var data = await this.UpsertObjectToListAndUpdateStub(entity, entityObject, idpropertyName).ConfigureAwait(false);
                requiredData.SeasonIndex = data.SeasonIndex;
                requiredData.SeasonIndex = data.SeasonIndex;
                requiredData.UpdatedTime = data.UpdatedTime;
                requiredData.EntityId = data.UpdatedEntityId;
                requiredData.EntityName = data.EntityName;
            }

            return requiredData;
        }

        /// <summary>
        /// Upsert gms entity in specified leagues.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="leagueId">leagueId.</param>
        /// <returns>required details.</returns>
        protected async Task UpsertNewEntityInStubInRequiredLeague(string entity, string leagueId)
        {
            bool isTeamZipEntity = entity.ContainsIgnoreCase("team");
            string idpropertyName = isTeamZipEntity ? "teamId" : "id";
            (int SeasonIndex, string UpdatedTime, string EntityName, string EntityId) requiredData;
            var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
            requiredData.UpdatedTime = updateTime;
            requiredData.EntityName = entityName;
            JObject entityObject = this.entityData.GetEntityData(entity);
            {
                var stubName = entity.ReplaceIgnoreCase(Constants.Gms, string.Empty);
                var entityPropertyNameInJson = $"{stubName.ToLower()}s";
                var blobName = $"Clients/Gms/{stubName}s/data.json";
                string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobDataSource, Configs.StubContainer, blobName).ConfigureAwait(false);
                JToken league = null;
                int seasonIndex = 0;
                JObject content = JObject.Parse(jsonContent);
                JArray seasons = (JArray)content[entityPropertyNameInJson];
                if (!isTeamZipEntity)
                {
                    league = JArray.Parse(content[entityPropertyNameInJson].ToString()).SingleOrDefault(x => x["id"].ToString().Equals($"2022{leagueId}", StringComparison.Ordinal));
                    seasonIndex = seasons.ToList().FindIndex(x => x["id"].ToString().Equals($"2022{leagueId}", StringComparison.Ordinal));
                }
                else
                {
                    league = JArray.Parse(content[entityPropertyNameInJson].ToString()).SingleOrDefault(x => x["id"].ToString().Equals(leagueId, StringComparison.Ordinal));
                    seasonIndex = seasons.ToList().FindIndex(x => x["id"].ToString().Equals(leagueId, StringComparison.Ordinal));
                }

                entityObject["id"] = this.GetUniqueGameId();
                this.ScenarioContext["entityId"] = entityObject["id"].ToString();
                entityObject["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString()).ToUnixTimeMilliseconds();
                entityObject["updated"] = requiredData.UpdatedTime;
                var entities = (JArray)league[entityPropertyNameInJson];
                entities.Add(entityObject);

                content[entityPropertyNameInJson][seasonIndex] = league;
                jsonContent = content.ToString();
                await this.UploadStringContentToBlobAsync(Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
                await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Upsert Object To List And Update Stub.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="objectToBeAdded">objectToBeAdded.</param>
        /// <param name="idpropertyName">idPropertyName.</param>
        /// <param name="isUpdate">is update.</param>
        /// <param name="param1">param1.</param>
        /// <returns>required details.</returns>
        protected async Task<(int SeasonIndex, string UpdatedTime, string UpdatedEntityId, string EntityName)> UpsertObjectToListAndUpdateStub(string entity, JObject objectToBeAdded = null, string idpropertyName = "id", bool isUpdate = false, string param1 = null)
        {
            (int SeasonIndex, string UpdatedTime, string UpdatedEntityId, string EntityName) updatedRecorddata;
            string entityPropertyNameInJson = string.Empty;
            string entityNameInside = string.Empty;
            var stubName = entity.Replace(Constants.Gms, string.Empty, StringComparison.OrdinalIgnoreCase);
            if (stubName.ContainsIgnoreCase("schedule"))
            {
                entityPropertyNameInJson = entity.ContainsIgnoreCase(nameof(CosmosContainers.GmsGame)) ? "gameSchedules" : "eventSchedules";
                entityNameInside = entity.ContainsIgnoreCase(nameof(CosmosContainers.GmsGame)) ? "games" : "events";
            }
            else
            {
                entityPropertyNameInJson = $"{stubName.ToLower()}s";
                entityNameInside = entityPropertyNameInJson;
            }

            var blobName = $"Clients/Gms/{stubName}s/data.json";
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, blobName).ConfigureAwait(false);
            this.ScenarioContext["service"] = Constants.Gms;
            JObject content = JObject.Parse(jsonContent);
            JArray seasons = (JArray)content[entityPropertyNameInJson];
            if (objectToBeAdded != null && isUpdate)
            {
                var seasonIndex = seasons.ToList().FindIndex(x => x[entityNameInside].Any(y => y[idpropertyName].ToString().EqualsIgnoreCase(objectToBeAdded[idpropertyName].ToString())));
                JArray entities;
                if (seasonIndex == -1)
                {
                    updatedRecorddata.SeasonIndex = 0;
                    entities = (JArray)seasons[0][entityNameInside];
                }
                else
                {
                    updatedRecorddata.SeasonIndex = seasonIndex;
                    entities = (JArray)seasons[seasonIndex][entityNameInside];
                    entities.SingleOrDefault(x => x[idpropertyName].ToString().EqualsIgnoreCase(objectToBeAdded[idpropertyName].ToString())).Remove();
                }

                var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
                updatedRecorddata.UpdatedTime = updateTime;
                updatedRecorddata.UpdatedEntityId = objectToBeAdded[idpropertyName].ToString();
                if (entity.ContainsIgnoreCase("team"))
                {
                    updatedRecorddata.EntityName = objectToBeAdded["name"].ToString();
                }
                else
                {
                    updatedRecorddata.EntityName = objectToBeAdded["location"]["name"].ToString();
                }

                if (param1 != null)
                {
                    if (param1.ContainsIgnoreCase("decreasing"))
                    {
                        var est = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
                        var updatedTime = TimeZoneInfo.ConvertTime(DateTime.UtcNow.AddMinutes(-3), est).ToString("yyyy-MM-dd HH:mm:ss.999");
                        objectToBeAdded["updated"] = updatedTime;
                    }
                    else if (param1.ContainsIgnoreCase("no change"))
                    {
                    }
                    else
                    {
                        objectToBeAdded["updated"] = updatedRecorddata.UpdatedTime;
                    }
                }
                else
                {
                    objectToBeAdded["updated"] = updatedRecorddata.UpdatedTime;
                }

                this.ScenarioContext["entityId"] = updatedRecorddata.UpdatedEntityId;
                entities.Add(objectToBeAdded);
            }
            else
            {
                ////var seasonIndex = seasons.ToList().FindIndex(x => x[entityPropertyNameInJson] != null);
                ////if (seasonIndex == -1)
                ////{
                ////    Assert.Fail("No " + entityPropertyNameInJson + " are present in " + stubName + " stub");
                ////}

                var seasonIndex = 0;
                updatedRecorddata.SeasonIndex = seasonIndex;
                JArray entities = (JArray)seasons[seasonIndex][entityNameInside];

                if (!isUpdate)
                {
                    if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
                    {
                        this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                        this.ScenarioContext["entityCreated"] = this.GetGmsContainerNameOrPartitionKeyValue(entity);
                        this.ScenarioContext["entityPartitionKeyValue"] = this.GetGmsContainerNameOrPartitionKeyValue(entity);
                    }

                    var idvalue = ((JValue)objectToBeAdded[idpropertyName]).Value.ToString();
                    bool isDuplicateRecord = entities.Any(x => ((JValue)x[idpropertyName]).Value.ToString().Equals(idvalue, StringComparison.OrdinalIgnoreCase));
                    if (isDuplicateRecord)
                    {
                        entities.Remove(entities.FirstOrDefault(x => ((JValue)x[idpropertyName]).Value.ToString().Equals(idvalue, StringComparison.OrdinalIgnoreCase)));
                    }

                    entities.Add(objectToBeAdded);
                    updatedRecorddata.UpdatedTime = string.Empty;
                    updatedRecorddata.UpdatedEntityId = string.Empty;
                    updatedRecorddata.EntityName = string.Empty;
                }
                else
                {
                    if (!this.ScenarioContext.ContainsKey("blobBeforeUpdate"))
                    {
                        this.ScenarioContext["blobBeforeUpdate"] = jsonContent;
                        this.ScenarioContext["entityUpdated"] = this.GetGmsContainerNameOrPartitionKeyValue(entity);
                        this.ScenarioContext["entityPartitionKeyValue"] = this.GetGmsContainerNameOrPartitionKeyValue(entity);
                    }

                    JToken entityToBeUpdated = entities.FirstOrDefault(x => entities.Count(y => ((JValue)y[idpropertyName]).Value.ToString().EqualsIgnoreCase(((JValue)x[idpropertyName]).Value.ToString())) == 1);
                    if (entityToBeUpdated == null)
                    {
                        Assert.Fail($"TestData in {entity} stub has duplicate records");
                    }

                    var index = entities.IndexOf(entityToBeUpdated);
                    JObject entityToUpdate = (JObject)entities[index];
                    var (updateTime, entityName) = this.SetUpdatedTimeAndEntityName();
                    updatedRecorddata.UpdatedTime = updateTime;
                    updatedRecorddata.EntityName = entityName;
                    if (entity.ContainsIgnoreCase("team"))
                    {
                        updatedRecorddata.UpdatedEntityId = entityToUpdate[idpropertyName].ToString();
                        entityToUpdate["name"] = updatedRecorddata.EntityName;
                    }
                    else
                    {
                        updatedRecorddata.UpdatedEntityId = entityToUpdate[idpropertyName].ToString();
                        entityToUpdate["location"]["name"] = updatedRecorddata.EntityName;
                        entityToUpdate["dateTimeUtcMillis"] = DateTimeOffset.Parse(DateTime.UtcNow.AddDays(1).ToString(Constants.DateTimeStringFormat2)).ToUnixTimeMilliseconds();
                    }

                    entityToUpdate["updated"] = updatedRecorddata.UpdatedTime;
                    this.ScenarioContext["entityId"] = updatedRecorddata.UpdatedEntityId;
                }
            }

            jsonContent = content.ToString();
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            return updatedRecorddata;
        }

        /// <summary>
        /// Gets the modified GMS polling function name as per entity.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>string.</returns>
        protected string GetGmsPollingFunctionName(string entity)
        {
            var lowerCaseEntity = entity.ToLower();
            var modifiedName = lowerCaseEntity.ContainsIgnoreCase("team") ? $"{lowerCaseEntity}s" : lowerCaseEntity;
            return $"gms{modifiedName}pollingfunction";
        }

        /// <summary>
        /// Gets the container name or partition key value for an entity.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>string.</returns>
        protected string GetGmsContainerNameOrPartitionKeyValue(string entity)
        {
            var modifiedName = entity.ContainsIgnoreCase("team") ? $"{entity}s" : entity;
            return modifiedName;
        }

        /// <summary>
        /// Sets UpdatedTime And EntityName.
        /// </summary>
        /// <returns>Tuple.</returns>
        protected (string UpdateTime, string EntityName) SetUpdatedTimeAndEntityName()
        {
            var est = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            var updatedTime = TimeZoneInfo.ConvertTime(DateTime.UtcNow, est).ToString("yyyy-MM-dd HH:mm:ss.999");
            var entityName = this.GetUniqueGameId();
            return (updatedTime, entityName);
        }

        /// <summary>
        /// Build Comma-Separated Values String for Cosmos Query.
        /// </summary>
        /// <param name="values">list of values.</param>
        /// <param name="withSingleQuotes">withSingleQuotes.</param>
        /// <returns>string.</returns>
        protected string BuildCommaSeparatedValuesStringForCosmosQuery(IEnumerable<string> values, bool withSingleQuotes = true)
        {
            if (withSingleQuotes)
            {
                return string.Join(", ", values.Select(x => $"'{x}'"));
            }
            else
            {
                return string.Join(", ", values.Select(x => $"\"{x}\""));
            }
        }

        /// <summary>
        /// Builds Live and AdjustedLiveEventsTimes for all workflows.
        /// </summary>
        /// <param name="referenceTime">Reference time.</param>
        /// <param name="nssDelay">nssDelay.</param>
        /// <returns>Dictionary. Live time and Adjusted time for all workflows.</returns>
        protected Dictionary<string, (string LiveTime, string AdjustedTime)> BuildLiveAndAdjustedLiveEventsTimes(DateTime referenceTime, string nssDelay = null)
        {
            var workFlowIntentTimes = new Dictionary<string, (string LiveTime, string AdjustedTime)>();
            if (string.IsNullOrEmpty(nssDelay))
            {
                nssDelay = "0";
            }

            var eventMetadataSetupWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Setup_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataSetupWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Setup_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataStartWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Start_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataStartWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Start_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataEndWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_End_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataEndWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_End_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataCleanupWorkflowBusinessDefaultOffset = DateTime.ParseExact(this.FeatureContext.Get<string>("Event_Metadata_Cleanup_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal), "dd:HH:mm:ss", CultureInfo.InvariantCulture);
            var eventMetadataCleanupWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Cleanup_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureSetupWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Setup_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureSetupWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Setup_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureStartWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Start_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureStartWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Start_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureEndWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_End_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureEndWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_End_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureCleanupWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Cleanup_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventInfrastructureCleanupWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Infrastructure_Cleanup_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var audienceSetUpWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Audience_Setup_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var audienceSetUpWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Audience_Setup_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataDeleteWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Delete_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventMetadataDeleteWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Metadata_Delete_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventReachedTipOffTimeWorkflowExecutionDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Reached_Tipoff_Time_ExecutionDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));
            var eventReachedTipOffTimeWorkflowBusinessDefaultOffset = DateTime.Parse(this.FeatureContext.Get<string>("Event_Reached_Tipoff_Time_BusinessDefaultOffset").Replace("-", string.Empty, StringComparison.Ordinal));

            workFlowIntentTimes.Add(nameof(Workflows.EventInfrastructureSetup), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Subtract(eventInfrastructureSetupWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventInfrastructureSetupWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventInfrastructureCleanup), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Add(eventInfrastructureCleanupWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventInfrastructureCleanupWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventInfrastructureStart), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Subtract(eventInfrastructureStartWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventInfrastructureStartWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventInfrastructureEnd), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Add(eventInfrastructureEndWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventInfrastructureEndWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));

            workFlowIntentTimes.Add(nameof(Workflows.EventMetadataSetup), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), DateTime.UtcNow.Add(eventMetadataSetupWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventMetadataSetupWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventMetadataCleanup), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).AddHours(TimeSpan.Parse(eventMetadataCleanupWorkflowBusinessDefaultOffset.ToString("dd:HH:mm:ss")).TotalHours).Add(eventMetadataCleanupWorkflowExecutionDefaultOffset.TimeOfDay).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));

            workFlowIntentTimes.Add(nameof(Workflows.EventMetadataStart), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Subtract(eventMetadataStartWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventMetadataStartWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventMetadataEnd), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Add(eventMetadataEndWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventMetadataEndWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));

            workFlowIntentTimes.Add(nameof(Workflows.AudienceSetup), (referenceTime.ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), DateTime.UtcNow.Add(audienceSetUpWorkflowBusinessDefaultOffset.TimeOfDay.Add(audienceSetUpWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            workFlowIntentTimes.Add(nameof(Workflows.EventMetadataDelete), (referenceTime.ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.Add(eventMetadataDeleteWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventMetadataDeleteWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));

            workFlowIntentTimes.Add(nameof(Workflows.EventReachedTipoffTime), (referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture), referenceTime.AddMinutes(Convert.ToDouble(nssDelay)).Add(eventReachedTipOffTimeWorkflowBusinessDefaultOffset.TimeOfDay.Add(eventReachedTipOffTimeWorkflowExecutionDefaultOffset.TimeOfDay)).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture)));
            return workFlowIntentTimes;
        }

        /// <summary>
        /// Gets the Id of specified Actor.
        /// </summary>
        /// <param name="actor">Actor name.</param>
        /// <returns>Actor Id. string.</returns>
        protected string GetActorIdForActor(string actor)
        {
            string actorId = string.Empty;
            switch (actor)
            {
                case nameof(Actors.Aquila):
                    {
                        actorId = Constants.AquilaActorId;
                        break;
                    }

                case nameof(Actors.Prisma):
                    {
                        actorId = Constants.PrismaActorId;
                        break;
                    }

                case nameof(Actors.Tvp):
                    {
                        actorId = Constants.TvpActorId;
                        break;
                    }

                case nameof(Actors.Playout):
                    {
                        actorId = Constants.PlayoutActorId;
                        break;
                    }
            }

            return actorId;
        }

        /// <summary>
        /// Gets the Id of specified Workflow.
        /// </summary>
        /// <param name="workflow">Workflow name.</param>
        /// <returns>WorkflowId. string.</returns>
        protected string GetWorkflowIdForWorkflow(string workflow)
        {
            string workflowId = string.Empty;
            switch (workflow)
            {
                case nameof(Workflows.EventMetadataSetup):
                    workflowId = nameof(Workflows.EventMetadataSetup);
                    break;
                case nameof(Workflows.EventMetadataDelete):
                    workflowId = nameof(Workflows.EventMetadataDelete);
                    break;
                case nameof(Workflows.EventMetadataStart):
                    workflowId = nameof(Workflows.EventMetadataStart);
                    break;
                case nameof(Workflows.EventMetadataEnd):
                    workflowId = nameof(Workflows.EventMetadataEnd);
                    break;
                case nameof(Workflows.EventMetadataCleanup):
                    workflowId = nameof(Workflows.EventMetadataCleanup);
                    break;
                case nameof(Workflows.AudienceSetup):
                    workflowId = nameof(Workflows.AudienceSetup);
                    break;
                case nameof(Workflows.EventInfrastructureSetup):
                    workflowId = nameof(Workflows.EventInfrastructureSetup);
                    break;
                case nameof(Workflows.EventInfrastructureStart):
                    workflowId = nameof(Workflows.EventInfrastructureStart);
                    break;
                case nameof(Workflows.EventInfrastructureEnd):
                    workflowId = nameof(Workflows.EventInfrastructureEnd);
                    break;
                case nameof(Workflows.EventInfrastructureCleanup):
                    workflowId = nameof(Workflows.EventInfrastructureCleanup);
                    break;
                case nameof(Workflows.EventReachedTipoffTime):
                    workflowId = nameof(Workflows.EventReachedTipoffTime);
                    break;
                default:
                    {
                        // No code is needed
                        break;
                    }
            }

            return workflowId;
        }

        /// <summary>
        /// clears the blobs from test-automation container.
        /// </summary>
        /// <returns>Task.</returns>
        protected async Task ClearEventsData()
        {
            var container = this.blobDataSource.Read(Constants.AutomationTestStubContainerName, keyPrefix: Constants.EventsStorage);
            BlobContinuationToken fileToken = null;
            do
            {
                var result = await container.ListBlobsSegmentedAsync(fileToken).ConfigureAwait(false);
                fileToken = result.ContinuationToken;
                Parallel.ForEach(result.Results.Where(r => r.GetType() == typeof(CloudBlockBlob)).ToList(), item =>
                {
                    ((CloudBlockBlob)item).DeleteIfExistsAsync().ConfigureAwait(false);
                });
            }
            while (fileToken != null);
        }

        /// <summary>
        /// Store location details.
        /// </summary>
        /// <param name="entityObject">game/event object.</param>
        /// <returns>locationDict.dictionary.</returns>
        protected Dictionary<string, string> GetLocationDetails(JObject entityObject)
        {
            Dictionary<string, string> locationsDict = new();
            locationsDict.Add("LocationId", entityObject["location"]["LocationId"].ToString());
            locationsDict.Add("Name", entityObject["location"]["name"].ToString());
            locationsDict.Add("City", entityObject["location"]["city"].ToString());
            locationsDict.Add("CountryCode", entityObject["location"]["countryCode"].ToString());
            locationsDict.Add("Postal", entityObject["location"]["postal"].ToString());
            locationsDict.Add("StateOrProvince", entityObject["location"]["stateOrProvince"].ToString());
            locationsDict.Add("Street", entityObject["location"]["street"].ToString());
            locationsDict.Add("TimeZone", entityObject["location"]["timeZone"].ToString());
            locationsDict.Add("LocationType", entityObject["location"]["locationType"].ToString());

            return locationsDict;
        }

        /// <summary>
        /// Gets prisma esni medias from game/event level schedule.
        /// </summary>
        /// <param name="gmsEntityId">game id/event id.</param>
        /// <param name="isFromDeleteWorkflow">If true. get esni resources ids from only delete workflow. else, gets from other workflows.</param>
        /// <returns>List of prisma esni medias in different workflows.</returns>
        protected async Task<(List<PrismaEsniMedia> EsniMediasInSetupWorkflow, List<string> EsniIdsInDeleteListInSetup, List<string> EsniIdsInCleanup, List<string> EsniIdsInDelete)> GetPrismaEsniMediasOfAGmsEntityInDifferentWorkflows(string gmsEntityId, bool isFromDeleteWorkflow = false)
        {
            (List<PrismaEsniMedia> EsniMediasInSetupWorkflow, List<string> EsniIdsInDeleteListInSetup, List<string> EsniIdsInCleanup, List<string> EsniIdsInDelete) esniResourcesDetailsInWorkflows = (new(), new(), new(), new());
            var gmsEntityLevelSchedule = (await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetGameLevelScheduleByGameId, gmsEntityId), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false)).SingleOrDefault();
            if (gmsEntityLevelSchedule != null)
            {
                if (!isFromDeleteWorkflow)
                {
                    var prismaActorDataPresentInSetupWorkflow = gmsEntityLevelSchedule["WorkflowIntents"].SingleOrDefault(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventMetadataSetup)))["VideoPlatformActorSpecificDetails"].SingleOrDefault(x => x["ActorId"].ToString().EqualsIgnoreCase(this.GetActorIdForActor(nameof(Actors.Prisma))));
                    if (gmsEntityLevelSchedule["WorkflowIntents"].Any(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventMetadataCleanup))))
                    {
                        var prismaActorDataPresentInCleanupWorkflow = gmsEntityLevelSchedule["WorkflowIntents"].SingleOrDefault(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventMetadataCleanup)))["VideoPlatformActorSpecificDetails"].SingleOrDefault(x => x["ActorId"].ToString().EqualsIgnoreCase(this.GetActorIdForActor(nameof(Actors.Prisma))));
                        if (prismaActorDataPresentInSetupWorkflow != null && prismaActorDataPresentInCleanupWorkflow != null)
                        {
                            esniResourcesDetailsInWorkflows.EsniMediasInSetupWorkflow.AddRange(prismaActorDataPresentInSetupWorkflow["Data"]["MediasToUpsert"].ToObject<List<PrismaEsniMedia>>());
                            if (prismaActorDataPresentInSetupWorkflow["Data"]["EsniResourceIdsToDelete"].HasValues)
                            {
                                esniResourcesDetailsInWorkflows.EsniIdsInDeleteListInSetup.AddRange(prismaActorDataPresentInSetupWorkflow["Data"]["EsniResourceIdsToDelete"].Values<string>());
                            }

                            esniResourcesDetailsInWorkflows.EsniIdsInCleanup.AddRange(prismaActorDataPresentInCleanupWorkflow["Data"]["EsniResourceIds"].Values<string>());
                        }
                    }
                    else if (prismaActorDataPresentInSetupWorkflow != null)
                    {
                        esniResourcesDetailsInWorkflows.EsniMediasInSetupWorkflow.AddRange(prismaActorDataPresentInSetupWorkflow["Data"]["MediasToUpsert"].ToObject<List<PrismaEsniMedia>>());
                    }
                }
                else
                {
                    var prismaActorDataPresentInDeleteWorkflow = gmsEntityLevelSchedule["WorkflowIntents"].SingleOrDefault(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventMetadataDelete)))["VideoPlatformActorSpecificDetails"].SingleOrDefault(x => x["ActorId"].ToString().EqualsIgnoreCase(this.GetActorIdForActor(nameof(Actors.Prisma))));
                    if (prismaActorDataPresentInDeleteWorkflow != null)
                    {
                        esniResourcesDetailsInWorkflows.EsniIdsInDelete.AddRange(prismaActorDataPresentInDeleteWorkflow["Data"]["EsniResourceIds"].Values<string>());
                    }
                }
            }

            return esniResourcesDetailsInWorkflows;
        }

        /// <summary>
        /// Uploads Esni data to prisma stub.
        /// </summary>
        /// <param name="isEsniMedia">Boolean. Upload Esni Media Data.</param>
        /// <param name="isEsniAudience">Boolean. Upload Esni Audience Data.</param>
        /// <param name="dataToBeAdded">Data to be added explicity.</param>
        /// <returns>Task.</returns>
        protected async Task UploadEsniDataToPrismaStub(bool isEsniMedia, bool isEsniAudience, IEnumerable<JObject> dataToBeAdded = null)
        {
            // Backup original blob.
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.PrismablobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("PrismaOriginalBlob"))
            {
                this.ScenarioContext["PrismaOriginalBlob"] = jsonContent;
                this.ScenarioContext["blobPath"] = this.PrismablobName;
            }

            JObject stubData = this.entityData.GetEntityData("PrismaStubPayload");
            JArray stubItems = JArray.Parse(stubData["esnimedia"].ToString());
            if (isEsniMedia)
            {
                var esniMedias = this.ScenarioContext.ContainsKey("EsniMedias") ? this.ScenarioContext.Get<List<PrismaEsniMedia>>("EsniMedias") : new();
                var prismaIdsToBeAddedToStubs = this.prismaHelper.GetPrismaIdsFromEsniMedias(esniMedias);
                prismaIdsToBeAddedToStubs.ForEach(x =>
                {
                    stubItems.Add(JObject.Parse($"{{id: '{x.ReplaceIgnoreCase("/", "%2F")}'}}"));
                });
            }

            if (isEsniAudience)
            {
                var esniAudiences = this.ScenarioContext.ContainsKey("EsniAudiences") ? this.ScenarioContext.Get<List<EsniAudienceInfo>>("EsniAudiences") : new();
                esniAudiences.ForEach(x =>
                {
                    stubItems.Add(JObject.Parse($"{{id: '{x.AudienceId.ReplaceIgnoreCase("/", "%2F")}'}}"));
                });
            }

            if (dataToBeAdded != null)
            {
                dataToBeAdded.ToList().ForEach(x => stubItems.Add(x));
            }

            stubData["esnimedia"] = stubItems.GroupBy(x => x["id"].ToString()).Select(y => y.First()).ToObject<JArray>();
            var stringcontent = stubData.ToString();
            using var dataStream = UtilityMethods.StringToStream(stringcontent);
            await this.blobDataSource.UploadAsync(Configs.StubContainer, this.ScenarioContext["blobPath"].ToString(), dataStream).ConfigureAwait(false);

            // Restart json server
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        protected string GetUniqueGameId()
        {
            return $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomNumber(10000, 99999)}";
        }

        /// <summary>
        /// Add Aquila channel to start/stop in stub.
        /// </summary
        /// <param name="arrayName">arrayName(start/stop).</param>
        /// <returns>Task.</returns>
        protected async Task AddChannelToStartOrStopInAquilaStub(string arrayName)
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            JObject json = JObject.Parse(jsonContent);
            JArray startArray = (JArray)json["start"];
            JArray stopArray = (JArray)json["stop"];
            startArray.Clear();
            stopArray.Clear();
            JArray expectedArray = arrayName.EqualsIgnoreCase("start") ? startArray : stopArray;
            string channel = @"{id:'" + this.ScenarioContext["channelId"].ToString() + "'}";
            expectedArray.Add(JObject.Parse(channel));
            jsonContent = json.ToString();

            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// Add TVP stub.
        /// </summary
        /// <param name="gmsEntityJson">game/event json document.</param>
        /// <returns>Task.</returns>
        protected async Task PrepareTVPEntities(JObject gmsEntityJson)
        {
            int counter = 1;
            GmsHelper gmsHelper = new();
            if (gmsEntityJson["Type"].ToString().EndsWith(Constants.Game))
            {
                foreach (var tempMedia in gmsEntityJson["media"])
                {
                    if (tempMedia["mediaType"]["name"].ToString().EqualsIgnoreCase(Constants.NSSIdentifier))
                    {
                        var temp = gmsHelper.BuildGmsMediaIdOrChannelIdOrProductionId(this.ScenarioContext["gameId"].ToString(), gmsEntityJson["awayTeam"]["abbr"].ToString(), tempMedia["id"].ToString(), gmsEntityJson["homeTeam"]["abbr"].ToString(), true).ToLower();
                        this.ScenarioContext[$"productionId{counter}"] = temp;
                        this.ScenarioContext["channelId"] = temp;
                        counter++;
                    }
                }

                this.ScenarioContext["teamEntitlements"] = JArray.Parse($"['NBATP-{gmsEntityJson["awayTeam"]["abbr"]}', 'NBATP-{gmsEntityJson["homeTeam"]["abbr"]}']");
            }
            else
            {
                foreach (var tempMedia in gmsEntityJson["media"])
                {
                    if (tempMedia["mediaType"]["name"].ToString().EqualsIgnoreCase(Constants.NSSIdentifier))
                    {
                        var temp = gmsHelper.BuildGmsMediaIdOrChannelIdOrProductionId(this.ScenarioContext["eventId"].ToString(), null, tempMedia["id"].ToString(), null, false).ToLower();
                        this.ScenarioContext[$"productionId{counter}"] = temp;
                        this.ScenarioContext["channelId"] = temp;
                        counter++;
                    }
                }
            }

            if (Configs.RunWithMocking)
            {
                // Hold original blob.
                string tvpJsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.TVPStubBlobName).ConfigureAwait(false);
                this.ScenarioContext["tvpOriginalBlob"] = tvpJsonContent;
                this.ScenarioContext["tvpBlobPath"] = this.TVPStubBlobName;
                string entityId = gmsEntityJson["Type"].ToString().EndsWith(Constants.Game) ? this.ScenarioContext["gameId"].ToString() : this.ScenarioContext["eventId"].ToString();

                // Update stub
                string stubData;
                if (JToken.Parse(gmsEntityJson["media"].ToString()).Where(x => x["mediaType"]["name"].ToString().ContainsIgnoreCase(Constants.NSSIdentifier) && (x["active"].ToString().EqualsIgnoreCase("true") || x["schedules"][0]["active"].ToString().EqualsIgnoreCase("true"))).ToList().Count == 1)
                {
                    if (gmsEntityJson["Type"].ToString().EndsWith(Constants.Game))
                    {
                        string homeTeamAbbr = gmsEntityJson["homeTeam"]["abbr"].ToString().ToLower();
                        string awayTeamAbbr = gmsEntityJson["awayTeam"]["abbr"].ToString().ToLower();
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubGame")).ReplaceIgnoreCase("<GameId>", entityId).ReplaceIgnoreCase("<MediaId>", gmsEntityJson["media"][0]["id"].ToString()).ReplaceIgnoreCase("tor", awayTeamAbbr).ReplaceIgnoreCase("bos", homeTeamAbbr);
                    }
                    else
                    {
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubEvent")).ReplaceIgnoreCase("<EventId>", entityId).ReplaceIgnoreCase("<MediaId>", gmsEntityJson["media"][0]["id"].ToString());
                    }
                }
                else if (JToken.Parse(gmsEntityJson["media"].ToString()).Where(x => x["mediaType"]["name"].ToString().ContainsIgnoreCase(Constants.NSSIdentifier) && (x["active"].ToString().EqualsIgnoreCase("true") || x["schedules"][0]["active"].ToString().EqualsIgnoreCase("true"))).ToList().Count == 0)
                {
                    if (gmsEntityJson["Type"].ToString().EndsWith(Constants.Game))
                    {
                        this.ScenarioContext["productionId1"] = $"g{entityId}placeholder";
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubGame")).ReplaceIgnoreCase("<GameId>", entityId).ReplaceIgnoreCase("tor<MediaId>bos", "placeholder");
                    }
                    else
                    {
                        this.ScenarioContext["productionId1"] = $"e{entityId}placeholder";
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubEvent")).ReplaceIgnoreCase("<EventId>", entityId).ReplaceIgnoreCase("tor<MediaId>bos", "placeholder");
                    }
                }
                else
                {
                    if (gmsEntityJson["Type"].ToString().EndsWith(Constants.Game))
                    {
                        string homeTeamAbbr = gmsEntityJson["homeTeam"]["abbr"].ToString().ToLower();
                        string awayTeamAbbr = gmsEntityJson["awayTeam"]["abbr"].ToString().ToLower();
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubGame")).ReplaceIgnoreCase("<GameId>", entityId).ReplaceIgnoreCase("<MediaId>", gmsEntityJson["media"][0]["id"].ToString()).ReplaceIgnoreCase("<MediaId2>", gmsEntityJson["media"][1]["id"].ToString()).ReplaceIgnoreCase("tor", awayTeamAbbr).ReplaceIgnoreCase("bos", homeTeamAbbr);
                    }
                    else
                    {
                        stubData = JsonConvert.SerializeObject(this.entityData.GetEntityData("TVPStubEvent")).ReplaceIgnoreCase("<EventId>", entityId).ReplaceIgnoreCase("<MediaId>", gmsEntityJson["media"][0]["id"].ToString()).ReplaceIgnoreCase("<MediaId2>", gmsEntityJson["media"][1]["id"].ToString());
                    }
                }

                using var stubDataStream = UtilityMethods.StringToStream(stubData);
                await this.blobDataSource.UploadAsync(Configs.StubContainer, this.ScenarioContext["tvpBlobPath"].ToString(), stubDataStream).ConfigureAwait(false);

                // Restart json server
                await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            }
        }
    }
}
// "//-----------------------------------------------------------------------".
// <copyright file="AquilaWatchdogSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Bdd.Core.DataSources;

    using Flurl;

    using global::Bdd.Core.Utils;

    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    /// <summary>
    /// AquilaWatchdogSteps step-definition class.
    /// </summary>
    [Binding]
    public class AquilaWatchdogSteps : ApiStepDefinitionBase
    {
        private readonly EntityData entityData;
        private readonly CosmosDBDataSource cosmosDBDataSource;
        private readonly BlobStorageDataSource blobStorage;
        private readonly JsonDataSource jsonDataSource;
        private dynamic response;
        private string entityId;
        private string entityName;
        private string updatedEntityId;
        private string oldEntityName;
        private int entityCount = 0;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaWatchdogSteps"/> class.
        /// </summary>
        /// <param name="cosmosDBDataSource">cosmosDBDataSource.</param>
        /// <param name="entityData">entityData.</param>
        /// <param name="blobStorage">Blob storage data source.</param>
        /// <param name="jsonDataSource">jsonDataSource.</param>
        public AquilaWatchdogSteps(CosmosDBDataSource cosmosDBDataSource, EntityData entityData, BlobStorageDataSource blobStorage, JsonDataSource jsonDataSource)
        {
            this.cosmosDBDataSource = cosmosDBDataSource;
            this.entityData = entityData;
            this.blobStorage = blobStorage;
            this.jsonDataSource = jsonDataSource;
        }

        /// <summary>
        /// triggers the aquila polling function by sending a http request with invalid function key.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [When(@"I trigger the Aquila ""(.*)"" polling function with invalid key")]
        public async Task WhenITriggerThePollingFunctionWithInvalidKey(string entity)
        {
            string function = $"aquila{entity.ToLower()}pollingfunction";
            object invalidKey = UtilityMethods.GetRandomString();
            var header = new Dictionary<string, object>() { { "x-functions-key", invalidKey } };
            this.response = await this.SendPostRequestAsync(Url.Combine(Configs.AquilaFunctionAppUrl + function), null, header).ConfigureAwait(false);
        }

        /// <summary>
        /// triggers the aquila polling function by sending a http request.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [Given(@"I trigger the Aquila ""(.*)"" polling function")]
        [When(@"I trigger the Aquila ""(.*)"" polling function")]
        public async Task WhenITriggerTheAquilaPollingFunction(string entity)
        {
            this.ScenarioContext["service"] = "Aquila";
            string function = $"aquila{entity.ToLower()}pollingfunction";
            JObject body = JObject.Parse("{ \"input\" : \"\"}");
            this.response = await this.SendPostRequestAsync(Url.Combine(Configs.AquilaFunctionAppUrl + function), body, this.AquilaFunctionHeader).ConfigureAwait(false);
            Assert.AreEqual(Constants.Accepted, this.response?.StatusCode.ToString(), $"Aquila {entity} polling function failed");
            await Task.Delay(1000).ConfigureAwait(false);
        }

        /// <summary>
        /// validates 401 status code in api response.
        /// </summary>
        [Then(@"I should get unauthorized message for Aquila polling function")]
        public void ThenIShouldGetUnauthorizedMessageForAquilaPollingFunction()
        {
            this.VerifyThat(() => Assert.AreEqual("401", this.response.StatusCode.ToString()));
        }

        /// <summary>
        /// Creates an entity in aquila.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [Given(@"I create a ""(.*)"" in Aquila")]
        [When(@"I create a ""(.*)"" in Aquila")]
        public async Task WhenICreateAInAquila(string entity)
        {
            var blobName = $"Clients/Aquila/{entity}s/data.json";
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                this.ScenarioContext["entityCreated"] = entity;
                this.ScenarioContext["entityPartitionKeyValue"] = entity;
            }

            JObject content = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(content[$"{entity.ToLower()}s"].ToString());
            JObject entityObject = this.entityData.GetEntityData(entity);

            this.entityId = Guid.NewGuid().ToString();
            this.ScenarioContext["entityId"] = this.entityId;
            this.entityName = $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomString(4)}";

            entityObject["id"] = this.entityId;
            entityObject["name"] = this.entityName;

            entities.Add(entityObject);
            content[$"{entity.ToLower()}s"] = entities;
            jsonContent = content.ToString();

            await this.UploadStringContentToBlobAsync(Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// Updates the entity in aquila.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [When(@"I update a ""(.*)"" in Aquila")]
        [When(@"I again update the same ""(.*)"" in Aquila")]
        public async Task WhenIUpdateAInAquila(string entity)
        {
            var blobName = $"Clients/Aquila/{entity}s/data.json";
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeUpdate"))
            {
                this.ScenarioContext["blobBeforeUpdate"] = jsonContent;
                this.ScenarioContext["entityUpdated"] = entity;
                this.ScenarioContext["entityPartitionKeyValue"] = entity;
            }

            this.entityName = $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomString(4)}";
            JObject json = JObject.Parse(jsonContent);
            JArray entities = (JArray)json[$"{entity.ToLower()}s"];

            var entityToBeUpdated = entities.FirstOrDefault(x => entities.Count(y => ((JValue)y["id"]).Value.ToString().EqualsIgnoreCase(((JValue)x["id"]).Value.ToString())) == 1);
            if (entityToBeUpdated == null)
            {
                Assert.Fail($"{entity} is not present in stub");
            }

            var index = entities.IndexOf(entityToBeUpdated);

            this.updatedEntityId = entities[index]["id"].ToString();
            this.oldEntityName = entities[index]["name"].ToString();
            entities[index]["name"] = this.entityName;

            jsonContent = json.ToString();
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if details of aquila entity got updates in store.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify the updated details of the Aquila ""(.*)"" in ""(.*)"" container in database")]
        public async Task ThenIVerifyTheUpdatedDetailsOfTheAquilaInContainerInAquilaStoreCosmosDB(string entity, string container)
        {
            Logger.Info(container);
            int timeout = 20000;
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, "Name", this.updatedEntityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            while (timeout > 0 && !itemData.ToList()[0].ToDictionary()["Name"].Equals(this.entityName))
            {
                itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, "Name", this.updatedEntityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
                timeout -= 5000;
            }

            Assert.AreEqual(this.entityName, itemData.ToList()[0].ToDictionary()["Name"], $"Updated name of {entity} is not matching");
        }

        /// <summary>
        /// validates if details of new created aquila entity is present in store.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify the newly created ""(.*)"" in Aquila is present in ""(.*)"" container in database")]
        public async Task ThenIVerifyTheNewlyCreatedInAquilaIsPresentInContainerInAquilaStoreCosmosDB(string entity, string container)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.entityId), null, Configs.CosmosDbName, container).ConfigureAwait(false);
            int timeout = 20000;
            while (timeout > 0 && itemData.ToList().Count < 1)
            {
                itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.entityId), null, Configs.CosmosDbName, container).ConfigureAwait(false);
                timeout -= 5000;
            }

            Assert.AreEqual(1, itemData.ToList().Count, $"Newly created {entity} in {container} count not matched");
        }

        /// <summary>
        /// validates if specified field value is set correctly in aquila entity.
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="entity">entity.</param>
        /// <param name="value">value.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify ""(.*)"" of the Aquila ""(.*)"" is set to ""(.*)""")]
        public async Task ThenIVerifyOfTheAquilaIsSetTo(string field, string entity, int value)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, this.entityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            if (itemData.ToList().Count == 0)
            {
                itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, this.updatedEntityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            }

            Assert.AreEqual(itemData.ToList()[0].ToDictionary()[field], value);
        }

        /// <summary>
        /// Set NotificationState to value.
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="entity">entity.</param>
        /// <param name="value">value.</param>
        /// <returns>Task.</returns>
        [When(@"I set ""(.*)"" of ""(.*)"" to ""(.*)""")]
        public async Task WhenISetOfTo(string field, string entity, int value)
        {
            var containerName = this.GetGmsContainerNameOrPartitionKeyValue(entity);
            string entityId = entity.ToLower().StartsWith("gms") ? this.ScenarioContext["entityId"].ToString() : this.entityId;
            entity = entity.EqualsIgnoreCase("GmsTeamZip") ? $"{entity}s" : entity;

            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, entityId), null, Configs.CosmosDbName, containerName).ConfigureAwait(false);
            var updatedEntity = itemData.ToList()[0].ToDictionary();
            updatedEntity[field] = value;

            await this.cosmosDBDataSource.UpdateAsync(Configs.CosmosDbName, entity, "id", entityId, updatedEntity, containerName).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if specified field value is updated correctly in aquila entity.
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="entity">entity.</param>
        /// <param name="value">value.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify ""(.*)"" of the updated Aquila ""(.*)"" is set to ""(.*)""")]
        public async Task ThenIVerifyOfTheUpdatedAquilaIsSetTo(string field, string entity, int value)
        {
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, this.updatedEntityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            Assert.AreEqual(itemData.ToList()[0].ToDictionary()[field], value);
        }

        /// <summary>
        /// validates if entity details matches in aquila & database.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify the ""(.*)"" details match in Aquila and database")]
        public async Task ThenIVerifyTheDetailsMatchInAquilaAndCosmosDB(string entity)
        {
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, "Name", this.entityId), null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            Assert.AreEqual(itemData.ToList()[0].ToDictionary()["Name"], this.entityName);
        }

        /// <summary>
        /// deletes the item from store.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [When(@"I delete a ""(.*)"" from ""(.*)"" container in database")]
        public async Task WhenIDeleteAFromContainerInAquilaStoreCosmosDB(string entity, string container)
        {
            Logger.Info(entity);
            await Task.Delay(5000).ConfigureAwait(false);
            await this.cosmosDBDataSource.DeleteItemsAsync(Configs.CosmosDbName, container, string.Format(CosmosQueries.GetDocumentById, this.entityId), false, container).ConfigureAwait(false);
        }

        /// <summary>
        /// verify if deleted item is present in store.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify the deleted ""(.*)"" is present in ""(.*)"" container in database")]
        public async Task ThenIVerifyTheDeletedIsPresentInContainerInOfCosmosDB(string entity, string container)
        {
            Logger.Info(entity);
            await Task.Delay(5000).ConfigureAwait(false);
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.entityId), null, Configs.CosmosDbName, container).ConfigureAwait(false);
            Assert.AreEqual(itemData.ToList().Count, 1);
        }

        /// <summary>
        /// Gets the count of entities in aquila.
        /// </summary>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [When(@"I get count of ""(.*)"" in Aquila")]
        public async Task WhenIGetCountOfInAquila(string container)
        {
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(CosmosQueries.GetAllDocuments, null, Configs.CosmosDbName, container).ConfigureAwait(false);
            this.entityCount = itemData.ToList().Count;
        }

        /// <summary>
        /// validates if specified field value is set correctly in aquila entity in store..
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="value">value.</param>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [When(@"I set ""(.*)"" to ""(.*)"" of a ""(.*)"" in ""(.*)"" container in database")]
        public async Task WhenISetToOfAInContainerInAquilaStoreCosmosDB(string field, int value, string entity, string container)
        {
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(CosmosQueries.GetTop1Document, null, Configs.CosmosDbName, entity).ConfigureAwait(false);
            this.entityId = itemData.ToList()[0].ToDictionary()["id"].ToString();
            var updatedEntity = itemData.ToList()[0].ToDictionary();
            updatedEntity[field] = value;

            await this.cosmosDBDataSource.UpdateAsync(Configs.CosmosDbName, container, "id", this.entityId, updatedEntity, container).ConfigureAwait(false);
            await Task.Delay(1000).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if updated aquila entity event is published.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify updated Aquila ""(.*)"" event is published")]
        public async Task ThenIVerifyUpdatedAquilaIsPublishedToEventGrid(string entity)
        {
            await this.ReadEventsAndCheckIfPublished(entity, this.updatedEntityId).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if created aquila entity event is published.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify the Aquila ""(.*)"" event is published")]
        [Then(@"I verify created Aquila ""(.*)"" event is published")]
        public async Task ThenIVerifyCreatedAquilaIsPublishedToEventGrid(string entity)
        {
            await this.ReadEventsAndCheckIfPublished(entity, this.entityId).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if no new entity is created in store.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="container">container.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify that no new ""(.*)"" is created in ""(.*)"" container in database")]
        public async Task ThenIVerifyThatNoNewIsCreatedInContainerinAquilaStoreCosmosDB(string entity, string container)
        {
            Logger.Info(entity);
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(CosmosQueries.GetAllDocuments, null, Configs.CosmosDbName, container).ConfigureAwait(false);
            Assert.AreEqual(itemData.ToList().Count, this.entityCount);
        }

        [Then(@"I validate if channels created above gets ""(.*)""")]
        [Then(@"I validate if dynamic channels of the game are ""(.*)""")]
        [Then(@"I validate if channels started above gets ""(.*)""")]
        [Then(@"I validate if channels created above are ""(.*)""")]
        public async Task ThenIValidateIfChannelsCreatedAboveGets(string channelState)
        {
            Logger.Info(channelState);
            //// write retry logic to verify if channels in this.channelsToBeChecked field are updated with state channelState param by calling aquila channel state apis.
            var blobName = $"Clients/Aquila/Channels/data.json";
            string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobStorage, Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                this.ScenarioContext["entityCreated"] = nameof(CosmosContainers.Channel);
                this.ScenarioContext["entityPartitionKeyValue"] = nameof(CosmosContainers.Channel);
            }

            // var sourceInfo = this.ScenarioContext.Get<Dictionary<string, string>>("VideoPlatformSourceInfo");
            JObject content = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(content["channels"].ToString());
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.Channel));
            this.ScenarioContext["entityId"] = this.ScenarioContext["channelId"];
            entityObject["id"] = this.ScenarioContext["channelId"].ToString();
            entityObject["state"] = "stopped";
            entityObject["name"] = $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomString(4)}";
            entities.Add(entityObject);
            content["channels"] = entities;
            jsonContent = content.ToString();

            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        [Then(@"I validate the ""([^""]*)"" workflow start up time is as per the configured value")]
        public async Task ThenIValidateTheWorkflowStartUpTimeIsAsPerTheConfiguredValue(string workflowType)
        {
            var documents = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetScheduleDocumentsWithAGameId, this.ScenarioContext["gameId"]), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false);
            foreach (var schedules in documents)
                {
                    foreach (var workflowIntents in schedules["WorkflowIntents"])
                    {
                        if (workflowIntents["WorkflowId"].ToString().EqualsIgnoreCase("EventMetadataStart"))
                        {
                            var liveEventTime = workflowIntents["LiveEventTime"];
                            var adjustedWorkflowRequestTime = workflowIntents["AdjustedWorkflowRequestTime"];
                            Assert.AreEqual(adjustedWorkflowRequestTime.ToString(), liveEventTime.ToDateTime().AddHours(-Constants.EventMetadataStartBusinessDefaultOffset).ToString(), $"The startup time for {workflowType} is not as expected");
                        }

                        if (workflowIntents["WorkflowId"].ToString().EqualsIgnoreCase("EventInfrastructureStart"))
                        {
                            var liveEventTime = workflowIntents["LiveEventTime"];
                            var adjustedWorkflowRequestTime = workflowIntents["AdjustedWorkflowRequestTime"];
                            Assert.AreEqual(adjustedWorkflowRequestTime.ToString(), liveEventTime.ToDateTime().AddHours(-Constants.EventInfrastructureStartBusinessDefaultOffset).ToString(), $"The startup time for {workflowType} is not as expected");
                        }

                        if (workflowIntents["WorkflowId"].ToString().EqualsIgnoreCase("EventInfrastructureSetup"))
                        {
                            var liveEventTime = workflowIntents["LiveEventTime"];
                            var adjustedWorkflowRequestTime = workflowIntents["AdjustedWorkflowRequestTime"];
                            Assert.AreEqual(adjustedWorkflowRequestTime.ToString(), liveEventTime.ToDateTime().AddHours(-Constants.EventInfrastructureSetupBusinessDefaultOffset).ToString(), $"The startup time for {workflowType} is not as expected");
                        }
                    }
                }
        }

        /// <summary>
        /// Reads the events and check if published.
        /// </summary>
        /// <param name="entity">entity.</param>
        /// <param name="entityId">entity id.</param>
        /// <returns>Task.</returns>
        private async Task ReadEventsAndCheckIfPublished(string entity, string entityId)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            string jsonContent = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
            JArray events = JArray.Parse(jsonContent);

            List<JToken> published = events.Where(json => json["subject"].ToString() == $"aquila.{entity}Updated" && json["data"]["Id"].ToString() == entityId && json["data"]["Type"].ToString().ToLower() == entity.ToLower()).ToList();
            if (published.Count < 1)
            {
                await Task.Delay(5000).ConfigureAwait(false);
                jsonContent = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
                events = JArray.Parse(jsonContent);
                published = events.Where(json => json["subject"].ToString() == $"aquila.{entity}Updated" && json["data"]["Id"].ToString() == entityId && json["data"]["Type"].ToString().ToLower() == entity.ToLower()).ToList();
            }

            Assert.GreaterOrEqual(published.Count, 1, $"Published event not found for {entity}");
        }
    }
}

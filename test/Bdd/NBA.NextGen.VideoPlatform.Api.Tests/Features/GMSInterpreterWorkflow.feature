@owner=v-avas<PERSON><PERSON> @gmsinterpreter @videoplatform @testplan=7081 @testsuite=28250 @before=GetRequiredOffsetsAsync() @parallel=false
Feature: GMSInterpreterWorkflow
It is a software component to convert GMS-Change events, into Schedule.Change.Request commands.
A GMS-Specific Event notifying us of a change and the change type
Reading from CosmosDB
Place that DTO onto : ServiceBus: Schedule.Change.Request Queue.

@testcase=11169 @priority=1 @version=6 @bvt @before=ClearEventsData() @before=CreateGameDocumentInCosmosDB() @after=DeleteGameDocumentFromCosmosDB() @after=ClearEventsData() @priority1
Scenario: Validate the GMS interpreter workflow when there is a scheduleId meeting all the requirements
When I trigger Schedule change function with its payload
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I verify whether the scheduleId is same as VideoPlatformScheduleId in the container "VideoPlatformSchedule" in Schedule Store database

@testcase=16514 @priority=1 @version=8 @bvt @before=ClearEventsData() @before=GetRandomVideoPlatformSourceFromCosmosAsync() @after=ClearEventsData() @testplan=7081 @testsuite=16355 @priority1
Scenario: Validate if GMS interpreter is creating required workflow intents with valid actor details when a game is created with NSS medias
Given I create a GMS "Game" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "Game" information
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate if valid esni medias with blackouts are created for NSS medias of above "Game"
And I validate if valid gms entitlements are created
And I validate if following actor details in respective workflow intents are correct
| WorkFlowIntent           | Actor  | CanBePresent |
| EventMetadataSetup       | Tvp    | true         |
| EventMetadataSetup       | Prisma | true         |
| EventInfrastructureSetup | Aquila | true         |
| EventInfrastructureSetup | Tvp    | true         |
| EventMetadataStart       | Tvp    | true         |
| EventMetadataEnd         | Tvp    | true         |
| EventMetadataCleanup     | Tvp    | true         |
| EventMetadataCleanup     | Prisma | true         |
And I validate if EventInfrastructureStart, EventInfrastructureStop, EventReachedTipoffTime and EventInfrastructureCleanup workflow intents of each schedule per NSS media of game are correct
And I delete the "GmsGame" document and related documents from database

@testcase=24633 @priority=2 @version=4 @before=ClearEventsData() @before=GetRandomVideoPlatformSourceFromCosmosAsync() @after=ClearEventsData() @testplan=7081 @testsuite=16355 @priority2
Scenario: Validate if GMS interpreter is updating required workflow intents with valid actor details when an existing gms game is updated
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "GmsGame" polling function
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate if valid esni medias with blackouts are created for NSS medias of above "Game"
And I validate if valid gms entitlements are created
And I validate if following actor details in respective workflow intents are correct
| WorkFlowIntent           | Actor  | CanBePresent |
| EventMetadataSetup       | Tvp    | true         |
| EventMetadataSetup       | Prisma | true         |
| EventInfrastructureSetup | Aquila | true         |
| EventMetadataStart       | Tvp    | true         |
| EventMetadataEnd         | Tvp    | true         |
| EventMetadataCleanup     | Tvp    | true         |
| EventMetadataCleanup     | Prisma | true         |
And I validate if EventInfrastructureStart, EventInfrastructureStop, EventReachedTipoffTime and EventInfrastructureCleanup workflow intents of each schedule per NSS media of game are correct
When I update above GMS "Game" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | False                 |
| TV            | United States   | RSN                   | Home        | True          | False                 |
| TV            | United States   | RSN                   | Away        | True          | True                  |
And I trigger the GMS "GmsGame" polling function
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate if esni medias are upserted with valid blackouts for NSS medias of above "Game"
And I validate if valid gms entitlements are created
And I validate if following actor details in respective workflow intents are correct
| WorkFlowIntent           | Actor  | CanBePresent |
| EventMetadataSetup       | Tvp    | true         |
| EventMetadataSetup       | Prisma | true         |
| EventInfrastructureSetup | Aquila | true         |
| EventMetadataStart       | Tvp    | true         |
| EventMetadataEnd         | Tvp    | true         |
| EventMetadataCleanup     | Tvp    | true         |
| EventMetadataCleanup     | Prisma | true         |
And I validate if EventInfrastructureStart, EventInfrastructureStop, EventReachedTipoffTime and EventInfrastructureCleanup workflow intents of each schedule per NSS media of game are correct
Then I delete the "GmsGame" document and related documents from database

@testcase=17306 @priority=1 @version=4 @bvt @before=ClearEventsData() @after=ClearEventsData() @testplan=7081 @testsuite=17303 @priority1
Scenario: Validate if GMS interpreter is creating required workflow intents with valid actor details when a teamzip is created
Given I create a team zips document in database
When I trigger Audience function by passing above team information
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate if a schedule got created with audience ids in setup workflow as part of prisma actor details
And I delete the team zips and audience documents created from database

@testcase=13508 @priority=1 @version=4 @audience @bvt @testplan=7081 @testsuite=13114 @priority1
Scenario: Validate if valid audiences are created when a new team zips information is added to GMS
Given I create a team zips document in database
When I trigger Audience function by passing above team information
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
And I delete the team zips and audience documents created from database

@testcase=13509 @audience @priority=2 @testplan=7081 @testsuite=13114 @version=3 @priority2
Scenario: Validate if audiences are updated correctly when an existing team zips information is updated in the container
Given I create a team zips document in database and wait for audiences to be created
When I update above team zips information in database
When I trigger Audience function by passing above team information
Then I validate if audiences are updated correctly for RSN and OTA media distributions of the team
And I delete the team zips and audience documents created from database

@testcase=13634 @priority=1 @version=3 @teamzips @audience @bvt @testplan=7081 @testsuite=13114 @after=DeleteTestDataFromBlob() @priority1
Scenario: Validate if valid audiences are created when a new team zips information is found in GMS
When I create a "GmsTeamZip" in GMS
And I trigger the GMS "GmsTeamZip" polling function
Then I verify the newly created "GmsTeamZip" is present in "GmsTeamZip" container in database
And I verify the "GmsTeamZip" details match in GMS and database
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
And I delete the team zips and audience documents created from database

@testcase=14681 @priority=1 @version=7 @esnimedia @blackout @bvt @testplan=7081 @testsuite=14332 @priority1
Scenario Outline: Validate if valid esni medias with blackouts are created for NSS medias when a new gms game/event with medias is created in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| TV            | United States   | RSN                   | Away        | True          | True                  |
| TV            | United States   | OTA                   | Home        | True          | True                  |
| TV            | United States   | OTA                   | Away        | True          | True                  |
| TV            | Canada          | RSN                   | Home        | True          | True                  |
| TV            | Canada          | RSN                   | Away        | True          | True                  |
| TV            | Canada          | OTA                   | Home        | True          | True                  |
| TV            | Canada          | OTA                   | Away        | True          | True                  |
| TV            | India           | OTA                   | Home        | True          | True                  |
| SomeTV        | United States   | OTA                   | Home        | True          | True                  |
| SomeTV        | United States   | Regional              | Home        | True          | True                  |
| SomeTV        | United States   | RSN                   | Home        | True          | True                  |
| SomeTV        | Canada          | OTA                   | Home        | True          | True                  |
| SomeTV        | Canada          | Regional              | Home        | True          | True                  |
| SomeTV        | Canada          | RSN                   | Home        | True          | True                  |
| Something     | Some Country    | Some Name             | Any         | True          | True                  |
| TV            | United States   | Some Name             | Home        | True          | True                  |
| TV            | Canada          | Some Name             | Home        | True          | True                  |
| TV            | United States   | RSN                   | Something   | True          | True                  |
| TV            | Canada          | OTA                   | Something   | True          | True                  |
| TV            | United States   | Regional              | Any         | False         | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14682 @version=6 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=14332 @priority3
Scenario Outline: Validate if no blackouts are created for NSS esni medias when a new GMS game/event without NSS medias is created in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias and blackouts are not created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14683 @version=6 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=14332 @priority3
Scenario Outline: Validate if no blackouts are created for NSS esni medias when a new GMS game/event with only NSS medias is created in container
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias and blackouts are not created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14684 @version=6 @esnimedia @blackout @priority=2 @testplan=7081 @testsuite=14332 @priority2
Scenario Outline: Validate if esni medias are upserted with valid blackouts for NSS esni medias correctly when existing medias of gms game/event are updated in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| TV            | Canada          | OTA                   | Away        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | False                 |
| TV            | Canada          | OTA                   | Away        | True          | False                 |
| TV            | United States   | RSN                   | Away        | True          | True                  |
| TV            | Canada          | OTA                   | Home        | True          | True                  |
| TV            | United States   | OTA                   | Away        | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are upserted with valid blackouts for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14685 @version=6 @esnimedia @blackout @priority=2 @testplan=7081 @testsuite=14332 @priority2
Scenario Outline: Validate if esni medias are updated with valid blackouts for NSS esni medias correctly when existing medias of gms game/event are made inactive in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| TV            | Canada          | OTA                   | Away        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | False                 |
| TV            | Canada          | OTA                   | Away        | True          | False                 |
| TV            | Canada          | RSN                   | Away        | True          | True                  |
| TV            | United States   | OTA                   | Away        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are updated with valid blackouts for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14686 @version=6 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=14332 @priority3
Scenario Outline: Validate if NSS esni media gets deleted when there is 1 NSS media and Non-NSS media for gms game/event and later Non-NSS is made inactive in next game/event update
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | False                 |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if NSS esni media gets deleted as there are no blackouts to apply for "<EntityType>" and to_be_deleted list is updated
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28215 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of inactive NSS medias are added to to_be_deleted list when existing NSS medias of gms game/event are made inactive in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | Canada          | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | Canada          | RSN                   | Home        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are updated with valid blackouts for NSS medias of above "<EntityType>" and to_be_deleted list is updated
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28216 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of inactive TV medias are added to to_be_deleted list when existing TV medias of gms game/event are made inactive in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | False                 |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are updated with valid blackouts for NSS medias of above "<EntityType>" and to_be_deleted list is updated
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28217 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of inactive NSS/TV medias are added to to_be_deleted list when existing NSS/TV medias of gms game/event are made inactive in database
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | False                 |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are updated with valid blackouts for NSS medias of above "<EntityType>" and to_be_deleted list is updated
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28218 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of active NSS/TV medias are added back to setup list when existing NSS/TV medias of gms game/event are made active from inactive
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | False                 |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if esni medias are updated with valid blackouts for NSS medias of above "<EntityType>" and to_be_deleted list is updated
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if existing esni entities of above "<EntityType>" which are made active from inactive are removed from to_be_deleted list and added back to setup list
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28219 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if all existing esni entities of valid medias are added to delete workflow when existing gms entity becomes irrelevant for orchestration by making all NSS medias inactive
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if there is only "<EntityType>" level schedule with only "EventMetadataDelete" workflow
And I validate if all existing esni entities of above "<EntityType>" are present in delete workflow prisma actor data
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28220 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of valid medias are categorized correctly when existing gms entity is made relevant from irrelevant by making atleast 1 NSS media active
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | False                 |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | False                 |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if there is only "<EntityType>" level schedule with only "EventMetadataDelete" workflow
And I validate if all existing esni entities of above "<EntityType>" are present in delete workflow prisma actor data
When I update above GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | False         | True                  |
| TV            | United States   | Regional              | Any         | True          | False                 |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28221 @version=1 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if all existing esni entities of valid medias are added to delete workflow when existing gms entity becomes irrelevant for orchestration by making inactive
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I make above Gms "<EntityName>" "Inactive"
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if there is only "<EntityType>" level schedule with only "EventMetadataDelete" workflow
And I validate if all existing esni entities of above "<EntityType>" are present in delete workflow prisma actor data
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28222 @version=2 @esnimedia @blackout @priority=3 @testplan=7081 @testsuite=28212 @priority3
Scenario Outline: Validate if esni entities of valid medias are categorized correctly when existing gms entity is made relevant from irrelevant by making game active
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I make above Gms "<EntityName>" "Inactive"
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if there is only "<EntityType>" level schedule with only "EventMetadataDelete" workflow
And I validate if all existing esni entities of above "<EntityType>" are present in delete workflow prisma actor data
When I make above Gms "<EntityName>" "Active"
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created again for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14687 @priority=1 @version=6 @esnimedia @blackout @gmsgame @gmsevent @viewingpolicy @bvt @testplan=7081 @testsuite=14332 @after=DeleteTestDataFromBlob() @priority1
Scenario Outline: Validate if valid esni medias are created when a new GMS game/event with NSS medias and other medias is created in GMS
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| TV            | United States   | RSN                   | Away        | True          | True                  |
| TV            | United States   | OTA                   | Home        | True          | True                  |
| TV            | United States   | OTA                   | Away        | True          | True                  |
| TV            | Canada          | RSN                   | Home        | True          | True                  |
| TV            | Canada          | RSN                   | Away        | True          | True                  |
| TV            | Canada          | OTA                   | Home        | True          | True                  |
| TV            | Canada          | OTA                   | Away        | True          | True                  |
| TV            | India           | OTA                   | Home        | True          | True                  |
| SomeTV        | United States   | OTA                   | Home        | True          | True                  |
| SomeTV        | United States   | Regional              | Home        | True          | True                  |
| SomeTV        | United States   | RSN                   | Home        | True          | True                  |
| SomeTV        | Canada          | OTA                   | Home        | True          | True                  |
| SomeTV        | Canada          | Regional              | Home        | True          | True                  |
| SomeTV        | Canada          | RSN                   | Home        | True          | True                  |
| Something     | Some Country    | Some Name             | Any         | True          | True                  |
| TV            | United States   | Some Name             | Home        | True          | True                  |
| TV            | Canada          | Some Name             | Home        | True          | True                  |
| TV            | United States   | RSN                   | Something   | True          | True                  |
| TV            | Canada          | OTA                   | Something   | True          | True                  |
| TV            | United States   | Regional              | Any         | False         | True                  |
When I trigger the GMS "<EntityName>" polling function
And I trigger the GMS "GmsTeamZip" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=27273 @videoplatformtemplate @priority=1 @version=2 @bvt @25671ac01 @testplan=7081 @testsuite=27177 @priority1 @36058ac02
Scenario Outline: Validate if GMS interpreter is adding correct Aquila Channel Resolution detail in EventInfrastructureSetup workflow intent for GMS Game/ Event when valid resolutions are present in NSS media and NSS media schedule
Given I have a "<Entity>" in GMS with a NSS media with a valid resolution in "NSS media"
And I update the "<Entity>" with a "valid" resolution for "NSS media schedule"
When I trigger the GMS "<Entity>" polling function
Then I validate if the "Resolution" value from "NSS media schedule" is added to Aquila actor details in workflow intents data of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=37177 @videoplatformtemplate @priority=1 @version=1 @bvt @25671ac01 @testplan=7081 @priority1 @36058ac02 @testsuite=37174 @priority1
Scenario Outline: Validate if GMS interpreter is adding correct Aquila Channel Resolution detail in EventInfrastructureSetup workflow intent for GMS Game/ Event when valid resolution is present in NSS media and NSS media schedule does not have resolution value
Given I have a "<Entity>" in GMS with a NSS media with a valid resolution in "NSS media"
And I update the "<Entity>" with a "invalid" resolution for "NSS media schedule"
When I trigger the GMS "<Entity>" polling function
Then I validate if the "Resolution" value from "NSS media" is added to Aquila actor details in workflow intents data of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |


@testcase=27274 @videoplatformtemplate @priority=1 @version=2 @25671ac01 @testplan=7081 @testsuite=27177 @priority1 @36058ac02
Scenario Outline: Validate if GMS interpreter is adding correct Aquila Channel Resolution detail when an existing Gms Game/Event is updated
Given I have a "<Entity>" in GMS with a NSS media with a valid resolution in "NSS media"
And I update the "<Entity>" with a "<ResolutionValidity1>" resolution for "NSS media schedule"
And I trigger the GMS "<Entity>" polling function
And I validate if the "Resolution" value from "<Resolution1>" is added to Aquila actor details in workflow intents data of "<Entity>"
When I update the "<Entity>" with a "<ResolutionValidity2>" resolution for "NSS media schedule"
And I trigger the GMS "<Entity>" polling function
Then I validate if the "Resolution" value from "<Resolution2>" is added to Aquila actor details in workflow intents data of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity  | ResolutionValidity1 | ResolutionValidity2 | Resolution1        | Resolution2        |
| GmsGame | invalid             | valid               | NSS media          | NSS media schedule |
| GmsGame | valid               | invalid             | NSS media schedule | NSS media          |
| GmsEvent | invalid             | valid               | NSS media          | NSS media schedule |
| GmsEvent | valid               | invalid             | NSS media schedule | NSS media          |

@testcase=27275 @videoplatformtemplate @priority=1 @version=2 @bvt @25671ac01 @before=ClearEventsData() @after=DeleteMKAquilaChannel() @testplan=7081 @testsuite=27177 @priority1
Scenario Outline: Validate if Aquila channel with correct Template is created when EventInfrastructureSetup workflow is executed by scheduler
Given I "create" a "<Entity>" in GMS with a NSS media with a valid resolution
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Resolution" value is added to Aquila actor details in workflow intents data of "<Entity>"
When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I validate whether the created Aquila channel has correct template value in Aquila
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=27276 @videoplatformsource @priority=1 @version=1 @bvt @25671ac01 @testplan=7081 @testsuite=27177 @priority1
Scenario Outline: Validate if GMS interpreter is adding correct Aquila Channel Encoder value in EventInfrastructureSetup workflow intent for GMS Game/ Event
Given I "create" a "<Entity>" in GMS with a NSS media with a valid Encoder value
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Encoder" value is added to Aquila actor details in workflow intents data of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=27277 @videoplatformsource @priority=1 @version=1 @25671ac01 @testplan=7081 @testsuite=27177 @priority1
Scenario Outline: Validate if GMS interpreter is adding correct Aquila Channel Encoder value when an existing Gms Game/Event is updated
Given I "create" a "<Entity>" in GMS with a NSS media with a valid Encoder value
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Encoder" value is added to Aquila actor details in workflow intents data of "<Entity>"
And I "update" a "<Entity>" in GMS with new channel Encoder value for NSS media
When I trigger the GMS "<Entity>" polling function
Then I validate if the "Encoder" value in Aquila actor details in workflow intents data of "<Entity>"is updated
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=27278 @videoplatformsource @priority=1 @version=2 @bvt @25671ac01 @before=ClearEventsData() @after=DeleteMKAquilaChannel() @testplan=7081 @testsuite=27177 @priority1
Scenario Outline: Validate if Aquila channel with correct Sources is created when EventInfrastructureSetup workflow is executed by scheduler
Given I "create" a "<Entity>" in GMS with a NSS media with a valid Encoder value
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Encoder" value is added to Aquila actor details in workflow intents data of "<Entity>"
When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I validate whether the created Aquila channel has correct source values in Aquila
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=27287 @videoplatformtemplate @priority=3 @version=1 @bvt @25671ac01 @before=ClearEventsData() @after=DeleteMKAquilaChannel() @testplan=7081 @testsuite=27177 @priority3
Scenario Outline: Validate if Aquila channel is not created when EventInfrastructureSetup workflow is executed with invaid channel resolution by scheduler
Given I create a "<Entity>" in GMS with a NSS media with a invalid "resolution" value
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Resolution" value is added to Aquila actor details in workflow intents data of "<Entity>"
When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "failed" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I should get "WorkflowStateChanged" event with "failed" status from the Orchestrator
And I validate whether the created Aquila channel is not created in Aquila
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@testcase=27288 @videoplatformtemplate @priority=3 @version=1 @bvt @25671ac01 @before=ClearEventsData() @after=DeleteMKAquilaChannel() @testplan=7081 @testsuite=27177 @priority3
Scenario Outline: Validate if Aquila channel is not created when EventInfrastructureSetup workflow is executed with invalid source values by scheduler
Given I create a "<Entity>" in GMS with a NSS media with a invalid "Encoder" value
When I trigger the GMS "<Entity>" polling function
Then I validate if the correct "Encoder" value is added to Aquila actor details in workflow intents data of "<Entity>"
When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "failed" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I should get "WorkflowStateChanged" event with "failed" status from the Orchestrator
And I validate whether the created Aquila channel is not created in Aquila
And I delete "<Entity>" from database
Examples:
| Entity         |
| GmsGame        |
| GmsEvent      |

@23733ac01 @priority=2 @version=4 @testplan=7081 @testsuite=24757 @testcase=24945 @gmsupdateignore @priority2
Scenario Outline: Validate whether the Gms update is ignored when Gms Update is happening on or after the point of time after which gms updates are ignored
Given I create a "<Entity>" in GMS with valid time
And I trigger the GMS "<Entity>" polling function
And I verify that the "<Event1>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether schedules are created in database
When I update "<Entity>" with Game/Event time in GMS with "<GameOrEventTime>" the point of time after which gms updates are ignored
And I trigger the GMS "<Entity>" polling function
Then I verify that the "<Event1>" event is published
Then I verify that the "<Event2>" event is published
And I validate whether the schedules are not updated
And I delete "<Entity>" from database
Examples:
| Entity   | GameOrEventTime | Event1           | Event2                           |
| GmsGame  | equals to       | gms.GameUpdated  | GmsInterpreter.GameUpdateIgnored |
| GmsGame  | after           | gms.GameUpdated  | GmsInterpreter.GameUpdateIgnored |
| GmsEvent | equals to       | gms.EventUpdated | GmsInterpreter.EventUpdateIgnored|
| GmsEvent | after           | gms.EventUpdated |GmsInterpreter.EventUpdateIgnored |

@23733ac01 @priority=2 @version=4 @before=GetRandomVideoPlatformSourceFromCosmosAsync() @after=ClearEventsData() @testplan=7081 @testsuite=24757 @gmsupdateignore @testcase=24946 @priority2
Scenario Outline: Validate whether the Gms update is accepted when Gms Update is happening before the point of time after which gms updates are ignored
Given I create a "<Entity>" in GMS with valid time
And I trigger the GMS "<Entity>" polling function
And I verify that the "<Event>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether schedules are created in database
When I update "<Entity>" with Game/Event time in GMS with "before" the point of time after which gms updates are ignored
And I trigger the GMS "<Entity>" polling function
Then I verify that the "<Event>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether the schedules are updated
And I delete "<Entity>" from database
Examples:
| Entity  | Event           |
| GmsGame | gms.GameUpdated |
|GmsEvent | gms.EventUpdated |

@23733ac01 @priority=3 @version=4 @before=ClearEventsData() @before=GetRandomVideoPlatformSourceFromCosmosAsync() @gmsupdateignore @testplan=7081 @testsuite=24757  @testcase=24947 @priority3
Scenario: Validate whether EventmetadataSetup workflow is reexecuting when Gms Update is happening  before the point of time after which gms updates are ignored
Given I create a "GmsGame" in GMS with valid time
And I trigger the GMS "GmsGame" polling function
And I validate whether schedules are created in database
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
When I update "GmsGame" with Game/Event time in GMS with "before" the point of time after which gms updates are ignored
And I trigger the GMS "GmsGame" polling function
Then I validate whether the schedules are updated
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I delete "GmsGame" from database

@testcase=28232 @testsuite=28231 @23734ac01 @priority=3 @testplan=7081 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate TVP actor details are updated when a gms game/event NSS media is updated as Inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataSetup" workflow of above "<EntityName>"
And I validate tvp actor details in "EventMetadataCleanup" workflow of above "<EntityName>"
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | False         | True                  |
And I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataSetup" workflow of above "<EntityName>"
And I validate tvp actor details in "EventMetadataCleanup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28233 @testsuite=28231 @23734ac01 @priority=3 @testplan=7081 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate TVP actor details are updated when a gms game/event is updated with no active NSS medias
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataSetup" workflow of above "<EntityName>"
And I validate tvp actor details in "EventMetadataCleanup" workflow of above "<EntityName>"
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | False         | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
And I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataDelete" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28234 @testsuite=28231 @23734ac01 @priority=3 @testplan=7081 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate TVP actor EventMetadataDelete workflow when a gms game/event is marked as inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataSetup" workflow of above "<EntityName>"
And I validate tvp actor details in "EventMetadataCleanup" workflow of above "<EntityName>"
When I make above Gms "<EntityName>" "Inactive" in Gms
And I trigger the GMS "<EntityName>" polling function
Then I validate tvp actor details in "EventMetadataDelete" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33544 @testsuite=33543 @32025ac03 @32025ac04 @priority=2 @version=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate TVP actor EventMetadataSetup workflow when a gms game/event has GeoRestrictionPolicies at Schedules level with multiple region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" GeoRestrictionPoliciy at "Schedules" level
| region | value |
| CA,US  | Allow |
| DE,FR  | Block |
And I trigger the GMS "<EntityName>" polling function
Then I validate GeoRestrictionPolicies details in "EventMetadataSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33546 @testsuite=33543 @32025ac03 @32025ac04 @priority=2 @version=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate TVP actor EventMetadataSetup workflow when a gms game/event has GeoRestrictionPolicies at media level with multiple region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" GeoRestrictionPoliciy at "Media" level
| region | value |
| CA,US  | Allow |
| DE,FR  | Block |
And I trigger the GMS "<EntityName>" polling function
Then I validate GeoRestrictionPolicies details in "EventMetadataSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33547 @testsuite=33543 @32025ac03 @32025ac04 @priority=3 @version=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate TVP actor EventMetadataSetup workflow when a gms game/event has GeoRestrictionPolicies at Schedules level with single region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" GeoRestrictionPoliciy at "Schedules" level
| region | value |
| CA     | Allow |
| DE     | Block |
And I trigger the GMS "<EntityName>" polling function
Then I validate GeoRestrictionPolicies details in "EventMetadataSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33548 @testsuite=33543 @32025ac03 @32025ac04 @priority=3 @version=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate TVP actor EventMetadataSetup workflow when a gms game/event has GeoRestrictionPolicies at media level with single region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" GeoRestrictionPoliciy at "Media" level
| region | value |
| CA     | Allow |
| DE     | Block |
And I trigger the GMS "<EntityName>" polling function
Then I validate GeoRestrictionPolicies details in "EventMetadataSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33549 @testsuite=33543 @32025ac03 @32025ac04 @priority=2 @version=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate TVP actor EventMetadataSetup workflow when a gms game/event has GeoRestrictionPolicies at media and Schedules level then Schedules policy gets applied
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" GeoRestrictionPoliciy at "Media" level
| region | value |
| CA     | Allow |
| DE     | Block |
And I add "<EntityName>" GeoRestrictionPoliciy at "Schedules" level
| region | value |
| PL,CZ  | Allow |
| FR,US  | Block |
And I trigger the GMS "<EntityName>" polling function
Then I validate GeoRestrictionPolicies details in "EventMetadataSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=44975 @testsuite=44972 @43642ac01 @43642ac01 @priority=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate channel is deployed to NSSGeoredundantRegions at "Schedules" level regions when a gms game/event has NSS-Georedundant-Regions at media and Schedules level
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" NSSGeoredundantRegions at "Media" level
| key                      | value           |
| NSS-Georedundant-Regions | eastus2,westus2 |
And I add "<EntityName>" NSSGeoredundantRegions at "Schedules" level
| key                      | value             |
| NSS-Georedundant-Regions | eastus2,centralus |
And I trigger the GMS "<EntityName>" polling function
Then I validate Regions details in "EventInfrastructureSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=44976 @testsuite=44972 @43642ac01 @43642ac01 @priority=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate channel is deployed to NSSGeoredundantRegions at "Media" level regions when a gms game/event has NSS-Georedundant-Regions are not present at Schedules level
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" NSSGeoredundantRegions at "Media" level
| key                      | value           |
| NSS-Georedundant-Regions | eastus2,westus2 |
And I trigger the GMS "<EntityName>" polling function
Then I validate Regions details in "EventInfrastructureSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=44977 @testsuite=44972 @43642ac01 @43642ac01 @priority=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate channel is deployed to NSSGeoredundantRegions at "Media" level regions when a gms game/event has NSS-Georedundant-Regions are not present at Schedules level using single region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" NSSGeoredundantRegions at "Media" level
| key                      | value   |
| NSS-Georedundant-Regions | eastus2 |
And I trigger the GMS "<EntityName>" polling function
Then I validate Regions details in "EventInfrastructureSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=44978 @testsuite=44972 @43642ac01 @43642ac01 @priority=2 @testplan=7081 @after=DeleteTestDataFromBlob() @priority2
Scenario Outline: Validate channel is deployed to NSSGeoredundantRegions at "Schedules" level regions when a gms game/event has NSS-Georedundant-Regions at media and Schedules level using single region
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add "<EntityName>" NSSGeoredundantRegions at "Media" level
| key                      | value   |
| NSS-Georedundant-Regions | eastus2 |
And I add "<EntityName>" NSSGeoredundantRegions at "Schedules" level
| key                      | value   |
| NSS-Georedundant-Regions | eastus2 |
And I trigger the GMS "<EntityName>" polling function
Then I validate Regions details in "EventInfrastructureSetup" workflow of above "<EntityName>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=46215 @priority=2 @version=1 @priority2 @45627ac01 @testsuite=46212 @testplan=7081
Scenario Outline:Validate AdjustedWorkflowRequestTime of EventMetadataCleanup in schedules is executing 75 hours after GameTime
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
And I trigger the GMS "<EntityName>" polling function
Then I verify whether "AdjustedWorkflowRequestTime" of "EventMetadataCleanup" in schedules is 75 hours after GameTime of "<EntityName>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |
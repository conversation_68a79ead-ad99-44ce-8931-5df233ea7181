@owner=v-avas<PERSON><PERSON> @testplan=7081 @videoplatform @multicastsources @testsuite=53679
Feature: IncludeMulticastSources
Asset is associated with encoder (greater than 10000) in schedules

@testcase=53899 @version=2 @priority2 @52347ac05 @priority=2 @testsuite=53679 @56483ac01 @testplan=7081 @manual
Scenario Outline: Validate correct assetId mapped to encoderId is used for streaming by playout
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I add an NSS media encoder value for "<EntityName>"
And I trigger the GMS "<EntityName>" polling function
And I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Playout
And I should get "PlayoutContainerStateChanged" event with state as "Started" from "Playout" actor
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate whether playout containers with Id in "AssetName + "_" + ChannelID" format is created
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=58099 @version=1 @priority2 @56483ac02 @priority=2 @testsuite=57741 @testplan=7081 @manual
Scenario Outline: Validate only one playout container is created if a game contains multiple nss medias with same encoderID
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set NSS media encoder value as below for "<EntityName>"
| media | EncoderId |
| 0     | 10004     |
| 1     | 10004     |
And I trigger the GMS "<EntityName>" polling function
And I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Playout
And I should get "PlayoutContainerStateChanged" event with state as "Started" from "Playout" actor
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate whether only one playout container with Id in "AssetName + "_" + ChannelID" format is created
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

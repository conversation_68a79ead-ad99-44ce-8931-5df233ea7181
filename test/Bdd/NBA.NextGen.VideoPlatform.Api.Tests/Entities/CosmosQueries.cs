// "//-----------------------------------------------------------------------".
// <copyright file="CosmosQueries.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
    public static class CosmosQueries
    {
        public static string GetOffSetsForSetUpCleanUpStartAndStopWorkflows => $"SELECT * FROM c WHERE c.id = '{Constants.StartWorkFlowId}' OR c.id = '{Constants.StopWorkFlowId}' OR c.id = '{Constants.SetUpWorkFlowId}' OR c.id = '{Constants.CleanUpWorkFlowId}' OR c.id = '{Constants.AudienceSetUpWorkFlowId}'";

        public static string GetDocumentsWithIds => "SELECT * FROM c WHERE c.id IN ({0})";

        public static string GetDocumentById => "SELECT * FROM c WHERE c.id = '{0}'";

        public static string GetFieldValueInADocumentById => "SELECT c.{0} FROM c where c.id = '{1}'";

        public static string GetFieldValueInDocuments => "SELECT c.{0} FROM c";

        public static string GetAllDocuments => "SELECT * FROM c";

        public static string GetTop1Document => "SELECT top 1 * FROM c";

        public static string GetDocumentsByActorInfrastructureId => "SELECT * FROM c where c.VideoPlatformActors[{0}].ActorInfrastructureId = '{1}'";

        public static string GetDocumentsByGmsEncoderIds => "SELECT * FROM c where c.VideoPlatformActors[0].ActorInfrastructureId IN ({0})";

        public static string GetScheduleDocumentByLiveEventIdAndEventScheduleIdAndChannelId => "SELECT * FROM c where c.RequestorLiveEventId ='{0}' and c.RequestorLiveEventScheduleId='{1}'and c.WorkflowIntents[0].ChannelId='{2}'";

        public static string GetScheduleDocumentByLiveEventId => "SELECT * FROM c where c.RequestorLiveEventId ='{0}'";

        public static string GetChannelByLiveEventId => "SELECT * FROM c where c.LiveEventId ='{0}'";

        public static string GetGameLevelScheduleByGameId => "SELECT * FROM c where c.RequestorLiveEventId ='{0}' and c.RequestorLiveEventScheduleId = null";

        public static string GetScheduleLevelScheduleByGameId => "SELECT * FROM c where c.RequestorLiveEventId ='{0}' and c.RequestorLiveEventScheduleId = {1}";

        public static string GetGmsEntitlementsByGameId => "SELECT * FROM c where c.MediaEntitlements[0].MediaId LIKE '%{0}%'";

        public static string GetNumberOfEntries => "SELECT VALUE COUNT(1) FROM c";

        public static string GetLastDocument => "SELECT top 1 * FROM c ORDER BY c._ts desc";

        public static string GetScheduleDocumentsWithAGameId => "SELECT * FROM c where c.RequestorLiveEventId LIKE '%{0}%'";

        public static string GetDocumentsWithASpecifiedTermInId => "SELECT * FROM c where c.id LIKE '%{0}%'";

        public static string GetDocumentsWithASpecifiedTermInProperty => "SELECT * FROM c where c.{0} LIKE '%{1}%'";

        public static string GetDocumentsWithASpecifiedPrefixInProperty => "SELECT * FROM c where c.{0} LIKE '{1}%'";

        public static string GetWatchDogContextDocumentsByLeagueId => "SELECT * FROM c WHERE c.League = {0}";

        public static string GetWatchDogContextDocumentsByEntityAndLeagueId => "SELECT * FROM c WHERE c.League = {0} AND c.ServiceType={1}";

        public static string GetVideoPlatformChannelDocumentsByLiveEventId => "SELECT * FROM c where c.LiveEventId ='{0}'";

        public static string GetChannelStartingTodayInEST => "SELECT * FROM c WHERE c.WorkflowIntents[0].WorkflowId='EventInfrastructureStart' and c.WorkflowIntents[0].LiveEventTime>'{0}' and c.WorkflowIntents[0].LiveEventTime<'{1}'";

        public static string GetDocumentByEntitlement => "SELECT * FROM c WHERE c.Entitlement = '{0}'";

        public static string GetDocumentByValue => "SELECT * FROM c WHERE c.Value = '{0}'";

        public static string GetAllChannels => "SELECT * FROM c WHERE c.LiveEventId != null";

        public static string GetDocumentByLeagueId => "SELECT * FROM c where c.League={0}";
    }
}
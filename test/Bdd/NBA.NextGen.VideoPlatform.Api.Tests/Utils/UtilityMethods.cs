// "//-----------------------------------------------------------------------".
// <copyright file="UtilityMethods.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Utils
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Text;
    using System.Threading.Tasks;
    using System.Xml;

    using Azure.Storage.Files.Shares;
    using Azure.Storage.Files.Shares.Models;

    using Bdd.Core.DataSources;

    using Bogus;

    using Flurl;
    using Flurl.Http;

    using Microsoft.Azure.Cosmos;

    using NBA.NextGen.VideoPlatform.Api.Tests.Executors;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    public static class UtilityMethods
    {
        private static readonly Faker FakerSource = new Faker();

        public static async Task RestartJsonServerAsync(string serverEndpoint)
        {
            dynamic response;

            try
            {
                response = Url.Combine(serverEndpoint).GetAsync();
                await Task.Delay(2000).ConfigureAwait(false);
                response = Url.Combine(serverEndpoint).GetAsync();
            }
            catch (FlurlHttpException ex)
            {
                response = ex.Call.Response;
                Assert.Fail($"failed to restart server - {response}");
            }

            await Task.Delay(3000).ConfigureAwait(false);
        }

        public static string StreamToString(Stream stream)
        {
            string content;
            using (var reader = new StreamReader(stream, Encoding.UTF8))
            {
                content = reader.ReadToEnd();
            }

            return content;
        }

        public static Stream StringToStream(string content)
        {
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));
            return stream;
        }

        public static string GetRandomString(int length = 10)
        {
            return FakerSource.Random.String2(length);
        }

        public static string ReplaceFirstOccurrence(string original, string oldValue, string newValue)
        {
            int loc = original.IndexOf(oldValue);
            if (loc == -1)
            {
                return original;
            }

            return original.Remove(loc, oldValue.Length).Insert(loc, newValue);
        }

        public static string GetKeyValueFromString(string content, string key, int charactersToSkipFromKey = 4, bool firstIndex = false)
        {
            int startIndex;
            if (firstIndex == true)
            {
                startIndex = content.IndexOf(key) + key.Length + charactersToSkipFromKey;
            }
            else
            {
                startIndex = content.LastIndexOf(key) + key.Length + charactersToSkipFromKey;
            }

            int i = startIndex;
            while (content[i] != '"')
            {
                i++;
            }

            int length = i - startIndex;
            return content.Substring(startIndex, length);
        }

        public static int GetRandomNumber(int min, int max)
        {
            return FakerSource.Random.Number(min, max);
        }

        /// <summary>
        /// Reads the stream content from specified blob & returns as a string.
        /// </summary>
        /// <param name="blobDataSource">BlobDataSource Object.</param>
        /// <param name="containerName">Blob container name.</param>
        /// <param name="blobName">Blob bame or path.</param>
        /// <returns>String. Blob content.</returns>
        public static async Task<string> GetStringContentFromBlobAsync(BlobStorageDataSource blobDataSource, string containerName, string blobName)
        {
            var blob = await blobDataSource.ReadAsync(containerName, blobName).ConfigureAwait(false);
            return UtilityMethods.StreamToString(blob);
        }

        /// <summary>
        /// Uploads string content to blob by converting to stream.
        /// </summary>
        /// <param name="blobDataSource">BlobDataSource Object.</param>
        /// <param name="containerName">Blob container name.</param>
        /// <param name="blobName">Blob bame or path.</param>
        /// <param name="content">Blob string content to upload.</param>
        /// <returns>Task.</returns>
        public static async Task UploadStringContentToBlobAsync(BlobStorageDataSource blobDataSource, string containerName, string blobName, string content)
        {
            using var blob = UtilityMethods.StringToStream(content);
            await blobDataSource.UploadAsync(containerName, blobName, blob).ConfigureAwait(false);
        }

        /// <summary>
        /// Get All Files From File Share.
        /// </summary>
        /// <param name="connectionString">connectionString.</param>
        /// <param name="shareName">shareName.</param>
        /// <returns>List.<string></returns>
        public static List<string> GetAllFilesFromFileShare(string connectionString, string shareName)
        {
            List<string> fileNames = new List<string>();
            ShareClient share = new ShareClient(connectionString, shareName);

            // Track the remaining directories to walk, starting from the root
            var remaining = new Queue<ShareDirectoryClient>();
            remaining.Enqueue(share.GetRootDirectoryClient());
            while (remaining.Count > 0)
            {
                // Get all of the next directory's files and subdirectories
                ShareDirectoryClient dir = remaining.Dequeue();
                foreach (ShareFileItem item in dir.GetFilesAndDirectories())
                {
                    fileNames.Add(item.Name);
                }
            }

            return fileNames;
        }

        public static JToken DeserializeWithCamelCasePropertyNames(string json)
        {
            using (TextReader textReader = new StringReader(json))
            using (JsonReader jsonReader = new CamelCasePropertyNameJsonReader(textReader))
            {
                JsonSerializer ser = new JsonSerializer();
                return ser.Deserialize<JToken>(jsonReader);
            }
        }

        /// <summary>
        /// Convert XML document to JSON document.
        /// </summary>
        /// <param name="xmlDoc">xmlDoc.</param>
        /// <returns>List.<string></returns>
        public static JObject ConvertXMLToJSON(string xmlDoc)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xmlDoc);
            var json = JsonConvert.SerializeXmlNode(doc.FirstChild.NextSibling, Newtonsoft.Json.Formatting.Indented, true);
            var ac = JsonConvert.DeserializeObject(json);
            return (JObject)JsonConvert.DeserializeObject(json);
        }
    }
}

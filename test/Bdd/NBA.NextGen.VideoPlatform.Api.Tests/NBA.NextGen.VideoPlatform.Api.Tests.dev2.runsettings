<?xml version="1.0" encoding="utf-8"?>
<!--https://docs.microsoft.com/en-us/visualstudio/test/configure-unit-tests-by-using-a-dot-runsettings-file-->
<RunSettings>
  <!-- Configurations that affect the Test Framework -->
  <RunConfiguration>

    <!-- 0 = Use all Cores -->
    <MaxCpuCount>0</MaxCpuCount>

    <!-- Path relative to solution directory -->
    <ResultsDirectory>.\TestResults</ResultsDirectory>

    <!-- x86 or x64 - You can also change it from menu Test, Test Settings, Default Processor Architecture -->
    <!--<TargetPlatform>x64</TargetPlatform>-->

    <!-- Framework35 | [Framework40] | Framework45 -->
    <!--<TargetFrameworkVersion>Framework45</TargetFrameworkVersion>-->

    <!-- Path to Test Adapters -->
    <!--<TestAdaptersPaths>.\</TestAdaptersPaths>-->

    <!--TestSessionTimeout is only available with Visual Studio 2017 version 15.5 and higher -->
    <!-- Specify timeout in milliseconds. A valid value should be greater than 0 -->
    <TestSessionTimeout>30000000</TestSessionTimeout>
  </RunConfiguration>

  <!-- Configurations for data collectors -->
  <!--<DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage" uri="datacollector://Microsoft/CodeCoverage/2.0" assemblyQualifiedName="Microsoft.VisualStudio.Coverage.DynamicCoverageDataCollector, Microsoft.VisualStudio.TraceCollector, Version=11.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
        <Configuration>
          <CodeCoverage>
            <ModulePaths>
              <Exclude>
                <ModulePath>.*CPPUnitTestFramework.*</ModulePath>
              </Exclude>
            </ModulePaths>-->


  <!-- We recommend you do not change the following values: -->

  <!--<UseVerifiableInstrumentation>True</UseVerifiableInstrumentation>
            <AllowLowIntegrityProcesses>True</AllowLowIntegrityProcesses>
            <CollectFromChildProcesses>True</CollectFromChildProcesses>
            <CollectAspDotNet>False</CollectAspDotNet>

          </CodeCoverage>
        </Configuration>
      </DataCollector>-->


  <!--Video data collector is only available with Visual Studio 2017 version 15.5 and higher -->

  <!--<DataCollector uri="datacollector://microsoft/VideoRecorder/1.0" assemblyQualifiedName="Microsoft.VisualStudio.TestTools.DataCollection.VideoRecorder.VideoRecorderDataCollector, Microsoft.VisualStudio.TestTools.DataCollection.VideoRecorder, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" friendlyName="Screen and Voice Recorder">
      </DataCollector>

    </DataCollectors>
  </DataCollectionRunSettings>-->

  <!-- Parameters used by tests at runtime -->
  <TestRunParameters>

    <!-- Common -->
    <Parameter name="appSettings:EnablePerfTag" value="false" />
    <Parameter name="appSettings:BuildNumber" value="" />

    <Parameter name="database:DbUser" value="" />
    <Parameter name="database:DbPwd" value="" />
    <Parameter name="chaosMonkey:Enabled" value="false" />

	  <Parameter name="appSettings:GmsUsername" value="" />
    <Parameter name="appSettings:GmsPassword" value="" />
    <Parameter name="appSettings:GmsClientId" value="" />
    <Parameter name="appSettings:GmsClientSecret" value="" />
    <Parameter name="appSettings:MKHubClientId" value="" />
	  <Parameter name="appSettings:MKHubPassword" value="" />
	  <Parameter name="appSettings:MKHubUsername" value="" />   
    <Parameter name="appSettings:GmsBaseUrl" value="https://gms-prev.nba.com/gms/" />
    <Parameter name="appSettings:MKHubBaseUrl" value="https://hub-preprod.aas.mediakind.com/api/v1.0/" />
    <Parameter name="appSettings:PrismaViewingPolicyManagerId" value="NBA-Game-224" />

    <!-- DevInt -->
    <Parameter name="appSettings:Env" value="dev2" />

    <!--Tests-->
    <Parameter name="appSettings:RunWithMocking" value="True" />

    <!--KeyVault-->
    <Parameter name="keyVault:ClientId" value="" />
    <Parameter name="keyVault:ClientSecret" value="" />
    <Parameter name="keyVault:VaultUri" value="https://ott-dvue2-atest-kv001.vault.azure.net/secrets/" />

    <!--AppService-->
    <Parameter name="appSettings:RestartJsonServerUrl" value="https://appstub-vpdev.azurewebsites.net/restart" />

    <!--Functions-->
    <Parameter name="appSettings:OrchestratorHttpFunctionUrl" value="https://ott-dvue2-vdorche-func001.azurewebsites.net/api/" />
    <Parameter name="appSettings:GMSFunctionAppUrl" value="https://ott-dvue2-vdgmswd-func001.azurewebsites.net/admin/functions/" />
    <Parameter name="appSettings:GmsInterpreterFunctionAppBaseUrl" value="https://ott-dvue2-vdgmsin-func001.azurewebsites.net/" />
    <Parameter name="appSettings:AquilaFunctionAppUrl" value="https://ott-dvue2-vdaquwd-func001.azurewebsites.net/admin/functions/" />
    <Parameter name="appSettings:AquilaActorFunctionAppUrl" value="https://ott-dvue2-vdaquac-func001.azurewebsites.net/api/ChannelWorkflow" />
    <Parameter name="appSettings:SchedulerFunctionAppUrl" value="https://ott-dvue2-vdsched-func001.azurewebsites.net/admin/functions/timetriggerscheduler" />
    <Parameter name="appSettings:AquilaActorHealthCheckFunctionAppUrl" value="https://ott-dvue2-vdaquac-func001.azurewebsites.net/admin/functions/" />
    <Parameter name="appSettings:PrismaActorFunctionAppUrl" value="https://ott-dvue2-vdpriac-func001.azurewebsites.net/admin/functions/" />
    <Parameter name="appSettings:TVPActorFunctionAppUrl" value="https://ott-dvue2-vdtvpac-func001.azurewebsites.net/admin/functions/" />
    <Parameter name="appSettings:ResourceGroupName" value="ott-dvue2-vidplat-rg001" />
    <Parameter name="appSettings:GmsWatchDogFunctionAppName" value="ott-dvue2-vdgmswd-func001" />

    <!--APIM,APIs-->
	  <Parameter name="appSettings:ApimBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net" />
    <Parameter name="appSettings:TVPApiBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/mktvp" />
    <Parameter name="appSettings:MKHubApimBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/mkhub/api/v1.0" />
    <Parameter name="appSettings:GmsApimBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/nbagms/gms/api/v1/custom" />
    <Parameter name="appSettings:OrchestrationApiBaseUrl" value="https://nbaapim-core.azure-api.net/orchestration/api/" />
    <Parameter name="appSettings:AquilaApimBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/mkaquila/api/v1.0/accounts/" />
    <Parameter name="appSettings:SimulatorApiBaseUrl" value="https://app-simulator-devint-uxpv.azurewebsites.net/api/Simulation/" />
	  <Parameter name="appSettings:SimulationApiBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/simulator/api/Simulation/" />
    <Parameter name="appSettings:PlayoutServiceApimBaseURL" value="https://ott-dvue2-intcall-apim001.azure-api.net/playoutservice" />
    <Parameter name="appSettings:PrismaWorkerApiBaseUrl" value= "https://ott-dvue2-intcall-apim001.azure-api.net/mkprismaworker/api/esni/viewingPolicyManagers/" />
    <Parameter name="appSettings:PrismaManagerApiBaseUrl" value= "https://ott-dvue2-intcall-apim001.azure-api.net/mkprismamanager/api/services/viewingPolicyManagers/" />
    <Parameter name="appSettings:GetAllSubscriptionOffersUrl" value= "https://ott-dvue2-intcall-apim001.azure-api.net/mktvp/v3/subscriptions/" />
    <Parameter name="appSettings.GetAllMediaUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/nbagms/gms/api/v1/custom/game/all/media/operation"/>
    <Parameter name="appSettings:UpdateProductionStateUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/UpdateProductionState/production" />
    <Parameter name="appSettings:ManualInterventionSchedulerAPIBaseUrl" value="http://ott-dvue2-intcall-apim001.azure-api.net/SchedulerOrchestratorAPI/orchestrator/" />
    <Parameter name="appSettings:StreamMarkerAPIUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/StreamMarkersUpdate/accounts/channels" />
    <Parameter name="appSettings:ManualInterventionReingestAPIBaseUrl" value="https://ott-dvue2-intcall-apim001.azure-api.net/GmsWatchdogOrchestratorAPI/orchestrator" />
	  <Parameter name="appSettings:HealthCheckUrl" value="https://ott-dvue2-#replace#-func001.azurewebsites.net/api" />
    <Parameter name="appSettings:MCDApiUrl" value="https://ott-dvue2-vdsched-func001.azurewebsites.net/api/orchestrator/channelstartime/" />
    <Parameter name="appSettings:GamesSeedingEndpointUrl" value="https://ott-dvue2-vdtvpac-func001.azurewebsites.net/api/orchestrator/gamesSeedingFromFile" />
	  
    <!--Service bus queue names-->
    <Parameter name="appSettings:WorkflowRequestQueue" value="vp-dv-wr" />
    <Parameter name="appSettings:ScheduleChangeRequestQueue" value="vp-dv-scr" />
    <Parameter name="appSettings:PrismaInfrastructureStateChangeRequestQueue" value="vp-dv-iscr-prisma-medias" />
    <Parameter name="appSettings:TvpInfrastructureStateChangeRequestQueue" value="vp-dv-iscr-tvp" />
    <Parameter name="appSettings:PlayoutInfrastructureStateChangeRequestQueue" value="vp-dv-iscr-playouts" />
    <Parameter name="appSettings:AquilaInfrastructureStateChangeRequestQueue" value="vp-dv-iscr-aquila-channels" />
    <Parameter name="appSettings:PlayoutStartRequestCompletedQueue" value="vp-dv-psrc" />

    

    <!--Blob-->
    <Parameter name="appSettings:StubContainer" value="vpdev" />

    <!--Cosmos-->
    <Parameter name="docDb:DatabaseName" value="ott-dvue2-vidplat-cdb001" />
    <Parameter name="appSettings:CosmosDbName" value="ott-dvue2-vidplat-cdb001" />
    <Parameter name="docDb:EndPointUrl" value="https://ott-dvue2-vidplat-cdb001.documents.azure.com:443/" />
    
  </TestRunParameters>

  <!-- Adapter Specific sections -->

  <!-- MSTest adapter -->
  <!--<MSTest>
    <Parallelize>
      <Workers>8</Workers>
      <Scope>ClassLevel</Scope>
    </Parallelize>
    <MapInconclusiveToFailed>True</MapInconclusiveToFailed>
    <CaptureTraceOutput>false</CaptureTraceOutput>
    <DeleteDeploymentDirectoryAfterTestRunIsComplete>False</DeleteDeploymentDirectoryAfterTestRunIsComplete>
    <DeploymentEnabled>False</DeploymentEnabled>
    <AssemblyResolution>
      <Directory Path="D:\myfolder\bin\" includeSubDirectories="false"/>
    </AssemblyResolution>
  </MSTest>-->

</RunSettings>
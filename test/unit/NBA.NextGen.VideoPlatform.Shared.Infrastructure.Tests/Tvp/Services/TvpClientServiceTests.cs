using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NBA.NextGen.Shared.Domain.Enums;
using NBA.NextGen.Shared.Unit;
using NBA.NextGen.Vendor.Api.MKTvp;
using NBA.NextGen.Vendor.Api.PlayOptions;
using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Constants;
using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Tvp.Services;
using Xunit;

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Tests.Tvp.Services;

public class TvpClientServiceTests
{
    private readonly Mock<ILogger<TvpClientService>> _logger;

    private readonly Mock<IPlayOptionsClient> _playOptionsClient;

    private readonly Mock<IMKTvpClient> _tvpClient;

    private readonly TvpClientService _sut;

    private readonly string _serviceCollectionId;

    private readonly string _subscriptionId;

    private readonly string _productionId;

    public TvpClientServiceTests()
    {
        _logger = new Mock<ILogger<TvpClientService>>();
        _tvpClient = new Mock<IMKTvpClient>();
        var clientFactory = new Mock<IClientFactory>();
        _playOptionsClient = new Mock<IPlayOptionsClient>();
        var apimOptions = new Mock<IOptionsMonitor<ApiManagementOptions>>();

        apimOptions.Setup(x => x.CurrentValue).Returns(new ApiManagementOptions
            { EnableMocking = true, SubscriptionKey = "f503e223-a1be-489b-a005-d2b2583d3de2" });

        clientFactory.Setup(x => x.CreateClient(false)).Returns(_tvpClient.Object);

        var profile = new TvpProfile();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        _sut = new TvpClientService(clientFactory.Object, _playOptionsClient.Object,
            apimOptions.Object, mapper, _logger.Object);
        
        _serviceCollectionId = Guid.NewGuid().ToString();
        _subscriptionId = Guid.NewGuid().ToString();
        _productionId = Guid.NewGuid().ToString();
    }

    [Fact]
    public async Task AddServiceCollectionsToSubscriptionAsync_WithNormalParameter_ShouldPassAsync()
    {
        _tvpClient
            .Setup(x => x.AddServiceCollectionsToSubscriptionsAsync(_subscriptionId, _serviceCollectionId,
                CancellationToken.None)).Returns(Task.CompletedTask);

        await _sut.AddServiceCollectionsToSubscriptionAsync(_serviceCollectionId, _subscriptionId);

        _tvpClient.Verify(
            x => x.AddServiceCollectionsToSubscriptionsAsync(
                It.Is<string>(x => x.Equals(_subscriptionId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(_serviceCollectionId, StringComparison.Ordinal)),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task RemoveServiceCollectionFromSubscriptionAsync_WithNormalParameter_ShouldPassAsync()
    {
        _tvpClient
            .Setup(x => x.RemoveServiceCollectionFromSubscriptionAsync(_subscriptionId, _serviceCollectionId,
                CancellationToken.None)).Returns(Task.CompletedTask);

        await _sut.RemoveServiceCollectionFromSubscriptionAsync(_serviceCollectionId, _subscriptionId);

        _tvpClient.Verify(
            x => x.RemoveServiceCollectionFromSubscriptionAsync(
                It.Is<string>(x => x.Equals(_subscriptionId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(_serviceCollectionId, StringComparison.Ordinal)),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task DeleteProductionByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        _tvpClient.Setup(x => x.DeleteProductionAsync(_productionId, CancellationToken.None))
            .Returns(Task.CompletedTask);

        await _sut.DeleteProductionByIdAsync(_productionId);

        _tvpClient.Verify(
            x => x.DeleteProductionAsync(
                It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task UpdateEventSchedulesAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventScheduleRequest = TestData.GetEventScheduleRequest();

        var tvpEventScheduleUpdateInfo = new TvpEventScheduleUpdateInfo
        {
            Upid = "de2c027b-f109-4a51-b997-f9806736fd87"
        };

        var eventExternalId = "93a293a4-3a12-4e9c-afec-f449396cfcb2";
        var eventScheduleId = "123293a4-3a12-4e9c-afec-f449396cfed3";

        _tvpClient
            .Setup(x => x.UpdateEventScheduleAsync(eventExternalId, eventScheduleId, eventScheduleRequest,
                CancellationToken.None)).Returns(Task.CompletedTask);

        await _sut.UpdateEventSchedulesAsync(eventExternalId, eventScheduleId, tvpEventScheduleUpdateInfo);

        _tvpClient.Verify(
            x => x.UpdateEventScheduleAsync(
                It.Is<string>(x => x.Equals(eventExternalId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(eventScheduleId, StringComparison.Ordinal)),
                It.IsAny<EventScheduleRequest>(),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task PostEventAsync_WithProperParameter_ShouldPassAsync()
    {
        var tvpEventCreationInfo = TestData.GetTvpEventCreationInfo();

        await _sut.PostEventAsync(tvpEventCreationInfo);

        _tvpClient.Verify(
            x => x.PostEventAsync(
                It.IsAny<EventRequest>(), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateEventAsync_WithProperParameter_ShouldPassAsync()
    {
        var eventId = "fc472817-ecca-4dd5-ae24-f501104e4a1a";

        var tvpEventUpdateInfo = TestData.GetTvpEventUpdateInfo();

        _tvpClient.Setup(x => x.PutEventByIdAsync(eventId, It.IsAny<EventRequest>(), CancellationToken.None))
            .Returns(Task.CompletedTask);

        await _sut.UpdateEventAsync(eventId, tvpEventUpdateInfo);

        _tvpClient.Verify(
            x => x.PutEventByIdAsync(
                eventId, It.IsAny<EventRequest>(), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task PostEventSchedulesAsync_WithProperParameter_ShouldPassAsync()
    {
        var eventExternalId = "4f05cedd-2576-41b5-9f66-9bf62e278f62";

        var tvpEventCreationInfo = TestData.GetTvpEventCreationInfo();
        var eventScheduleRequest = TestData.GetEventScheduleRequest();
        var eventScheduleResponse = TestData.GetEventScheduleResponse();

        _tvpClient
            .Setup(x => x.CreateEventScheduleAsync(eventExternalId, eventScheduleRequest, CancellationToken.None))
            .Returns(Task.FromResult(eventScheduleResponse));

        await _sut.PostEventSchedulesAsync(eventExternalId, tvpEventCreationInfo);

        _tvpClient.Verify(
            x => x.CreateEventScheduleAsync(
                It.Is<string>(x => x.Equals(eventExternalId, StringComparison.Ordinal)),
                It.IsAny<EventScheduleRequest>(),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task CreateProductionAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventScheduleExternalId = "51f7a91f-123c-4b78-b1cd-a36b5954cb31";

        _tvpClient.Setup(x => x.CreateProductionAsync(TestData.GetProductionRequest(), CancellationToken.None))
            .Returns(Task.FromResult(TestData.GetProductionResponse()));

        await _sut.CreateProductionAsync(eventScheduleExternalId,
            TestData.GetTvpTvpProductionCreationInfo(), new Collection<string>());

        _tvpClient.Verify(
            x => x.CreateProductionAsync(
                It.IsAny<ProductionRequest>(), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateProductionAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventScheduleExternalId = "51f7a91f-123c-4b78-b1cd-a36b5954cb31";

        var tvpProductionUpdateInfo = TestData.GetTvpProductionUpdateInfo();

        var productionRequest = TestData.GetProductionRequest();

        _tvpClient
            .Setup(x => x.UpdateProductionAsync(tvpProductionUpdateInfo.ProductionExternalId, productionRequest,
                CancellationToken.None)).Returns(Task.CompletedTask);

        await _sut.UpdateProductionAsync(eventScheduleExternalId, tvpProductionUpdateInfo, new Collection<string>());

        _tvpClient.Verify(
            x => x.UpdateProductionAsync(
                It.Is<string>(x => x.Equals(tvpProductionUpdateInfo.ProductionExternalId, StringComparison.Ordinal)),
                It.IsAny<ProductionRequest>(),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetProuctionByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var productionResponse = TestData.GetProductionResponse();
        _tvpClient.Setup(x => x.GetProductionByIdAsync(_productionId, CancellationToken.None))
            .Returns(Task.FromResult(productionResponse));

        await _sut.GetProductionByIdAsync(_productionId);

        _tvpClient.Verify(
            x => x.GetProductionByIdAsync(It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task CreateSubscriptionAsync_WithNormalParameter_ShouldPassAsync()
    {
        var subscription = TestData.GetSubscription();
        var tvpSubscription = TestData.GetTvpSubscription();

        _tvpClient.Setup(x => x.CreateSubscriptionAsync(_subscriptionId, subscription, CancellationToken.None))
            .Returns(Task.FromResult(Task.CompletedTask));
        await _sut.CreateSubscriptionAsync(_subscriptionId, tvpSubscription);
        _tvpClient.Verify(
            x => x.CreateSubscriptionAsync(
                It.Is<string>(x => x.Equals(_subscriptionId, StringComparison.Ordinal)),
                It.IsAny<Subscription>(),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task GetEventByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        var includeSchedules = true;
        var eventResponse = TestData.GetEventResponse();
        _tvpClient.Setup(x => x.GetEventByIdAsync(eventId, includeSchedules, CancellationToken.None))
            .Returns(Task.FromResult(eventResponse));

        await _sut.GetEventByIdAsync(eventId, includeSchedules);

        _tvpClient.Verify(
            x => x.GetEventByIdAsync(
                It.Is<string>(x => x.Equals(eventId, StringComparison.Ordinal)),
                It.Is<bool>(x => x.Equals(includeSchedules)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task DeleteEventByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        _tvpClient.Setup(x => x.DeleteEventByIdAsync(eventId, CancellationToken.None)).Returns(Task.CompletedTask);

        await _sut.DeleteEventByIdAsync(eventId, CancellationToken.None);

        _tvpClient.Verify(
            x => x.DeleteEventByIdAsync(
                It.Is<string>(x => x.Equals(eventId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task DeleteEventScheduleByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        var scheduleId = "f57b5aa1-e33a-4843-b790-e82f47422d5f";

        _tvpClient.Setup(x => x.DeleteEventScheduleAsync(eventId, scheduleId, CancellationToken.None))
            .Returns(Task.CompletedTask);

        await _sut.DeleteEventScheduleByIdAsync(eventId, scheduleId, CancellationToken.None);

        _tvpClient.Verify(
            x => x.DeleteEventScheduleAsync(
                It.Is<string>(x => x.Equals(eventId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(scheduleId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetAllTeamsAsync__WithNormalParameter_ShouldPassAsync()
    {
        var top = 2;
        var teamsPaginatedResponse = TestData.GetTeamsPaginatedResponse();
        _tvpClient.Setup(x => x.GetAllTeamAsync(top, null, CancellationToken.None))
            .Returns(Task.FromResult(teamsPaginatedResponse));

        await _sut.GetAllTeamsAsync(top);

        _tvpClient.Verify(
            x => x.GetAllTeamAsync(
                It.Is<int>(x => x.Equals(top)),
                null,
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task PostLocationAsync_WithProperParameter_ShouldPassAsync()
    {
        var location = TestData.GetEventLocation();
        var locationRequest = TestData.GetLocationRequest();
        var locationResponse = TestData.GetLocationResponse();

        _tvpClient.Setup(x => x.PostLocationAsync(locationRequest, CancellationToken.None))
            .Returns(Task.FromResult(locationResponse));
        await _sut.PostLocationAsync(location);

        _tvpClient.Verify(
            x => x.PostLocationAsync(
                It.Is<LocationRequest>(x => x.ExternalId == location.LocationId), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetLocationByIdAsync_WithProperParameter_ShouldPassAsync()
    {
        var externalLocationId = "123";
        var locationResponse = TestData.GetLocationResponse();

        _tvpClient.Setup(x => x.GetLocationByIdAsync(externalLocationId, CancellationToken.None))
            .Returns(Task.FromResult(locationResponse));
        await _sut.TryGetLocationByIdAsync(externalLocationId);

        _tvpClient.Verify(
            x => x.GetLocationByIdAsync(
                It.Is<string>(x => x.Equals(externalLocationId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateLocationAsync_WithProperParameter_ShouldPassAsync()
    {
        var location = TestData.GetEventLocation();
        var locationRequest = TestData.GetLocationRequest();
        var externalLocationId = "123";

        _tvpClient.Setup(x => x.PutLocationByIdAsync(externalLocationId, locationRequest, CancellationToken.None))
            .Returns(Task.CompletedTask);
        await _sut.UpdateLocationAsync(externalLocationId, location);

        _tvpClient.Verify(
            x => x.PutLocationByIdAsync(
                It.Is<string>(x => x.Equals(externalLocationId, StringComparison.Ordinal)),
                It.Is<LocationRequest>(x => x.ExternalId == location.LocationId),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetSubscriptionByIdAsync_withProperParameterAsync()
    {
        var id = "test";

        var subscriptionResponse = TestData.GetSubscription();
        _tvpClient.Setup(x => x.GetSubscriptionAsync(id, CancellationToken.None))
            .Returns(Task.FromResult(subscriptionResponse));
        await _sut.GetSubscriptionByIdAsync(id);
        _tvpClient.Verify(
            x => x.GetSubscriptionAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateProductionStateAsync_WithProperParameter_ShouldPassAsync()
    {
        var status = "Verified";

        _tvpClient.Setup(x => x.UpdateOperationalStateAsync(_productionId, status, CancellationToken.None))
            .Returns(Task.CompletedTask);
        await _sut.UpdateProductionStateAsync(_productionId, status);

        _tvpClient.Verify(
            x => x.UpdateOperationalStateAsync(
                It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(status, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetGeoPolicyByExternalIdAsync_WithProperParameter_ShouldPassAsync()
    {
        var externalLocationId = "123";
        var response = TestData.GetGeoRestrictionRequest();

        _tvpClient.Setup(x => x.GetGeoPolicyByExternalIdAsync(externalLocationId, CancellationToken.None))
            .Returns(Task.FromResult(response));
        await _sut.TryGetGeoPolicyByExternalIdAsync(externalLocationId);

        _tvpClient.Verify(
            x => x.GetGeoPolicyByExternalIdAsync(
                It.Is<string>(x => x.Equals(externalLocationId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task PostGeoRestrictionAsync_WithProperParameter_ShouldPassAsync()
    {
        var policy = TestData.GetGeoRestrictionPolicy();
        var request = TestData.GetGeoRestrictionRequest();
        var response = TestData.GetGeoRestrictionRequest();

        _tvpClient.Setup(x => x.PostGeoPoliciesAsync(request, CancellationToken.None))
            .Returns(Task.FromResult(response));
        await _sut.CreateGeoPolicyAsync(policy);

        _tvpClient.Verify(
            x => x.PostGeoPoliciesAsync(
                It.Is<GeoPolicy>(x => x.ExternalId == policy.ExternalId), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateGeoRestrictionAsync_WithProperParameter_ShouldPassAsync()
    {
        var policy = TestData.GetGeoRestrictionPolicy();
        var request = TestData.GetGeoRestrictionRequest();
        var response = TestData.GetGeoRestrictionRequest();

        _tvpClient.Setup(x => x.PutGeoPolicyByExternalIdAsync(request.ExternalId, request, CancellationToken.None))
            .Returns(Task.FromResult(response));
        await _sut.UpdateGeoPolicyAsync(policy);

        _tvpClient.Verify(
            x => x.PutGeoPolicyByExternalIdAsync(
                policy.ExternalId, It.Is<GeoPolicy>(x => x.ExternalId == policy.ExternalId), CancellationToken.None),
            Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdateOfferAsync_WithProperParameter_ShouldPassAsync()
    {
        var offer = TestData.GetTvpOffer();
        var request = TestData.GetTvpOfferRequest();
        var response = TestData.GetTvpOfferRequest();

        _tvpClient
            .Setup(x => x.CreateUpdateSubscriptionOfferAsync(It.IsAny<string>(), It.IsAny<string>(), request,
                CancellationToken.None)).Returns(Task.FromResult(response));
        await _sut.CreateOrUpdateOfferAsync("12", "123", offer);

        _tvpClient.Verify(
            x => x.CreateUpdateSubscriptionOfferAsync(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Offer>(), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetEventScheduleByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var eventExternalId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        var eventResponse = TestData.GetEventScheduleResponse();
        _tvpClient
            .Setup(x => x.GetEventScheduleAsync(eventExternalId, eventExternalId, null, CancellationToken.None))
            .Returns(Task.FromResult(eventResponse));

        await _sut.GetEventScheduleByIdAsync(eventExternalId);

        _tvpClient.Verify(
            x => x.GetEventScheduleAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                null,
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetOfferByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var id = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        var offerResponse = new ResourceOffer
        {
            Offer = TestData.GetTvpOfferRequest()
        };

        _tvpClient.Setup(x => x.GetOfferByIdAsync(id, CancellationToken.None))
            .Returns(Task.FromResult(offerResponse));

        await _sut.GetOfferByIdAsync(id);

        _tvpClient.Verify(
            x => x.GetOfferByIdAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetTeamByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var teamResponse = TestData.GetTeamResponse();
        var id = teamResponse.ExternalId;

        _tvpClient.Setup(x => x.GetTeamAsync(id, CancellationToken.None)).Returns(Task.FromResult(teamResponse));

        await _sut.TryGetTeamByIdAsync(id);

        _tvpClient.Verify(
            x => x.GetTeamAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task CreateTeamByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var team = TestData.GetTvpEventTeamCreationInfo();

        var teamRequest = TestData.GetTeamRequest();

        var teamResponse = TestData.GetTeamResponse();

        _tvpClient.Setup(x => x.CreateTeamAsync(teamRequest, CancellationToken.None))
            .Returns(Task.FromResult(teamResponse));

        await _sut.CreateTeamByIdAsync(team);

        _tvpClient.Verify(
            x => x.CreateTeamAsync(
                It.IsAny<TeamRequest>(),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateTeamByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var team = TestData.GetTvpEventTeamCreationInfo();

        var teamRequest = TestData.GetTeamRequest();

        var teamResponse = TestData.GetTeamResponse();

        var id = team.ExternalId;

        _tvpClient.Setup(x => x.UpdateTeamAsync(id, teamRequest, CancellationToken.None))
            .Returns(Task.FromResult(teamResponse));

        await _sut.UpdateTeamByIdAsync(id, team);

        _tvpClient.Verify(
            x => x.UpdateTeamAsync(
                It.IsAny<string>(),
                It.IsAny<TeamRequest>(),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetTeamByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var teamResponse = TestData.GetTeamResponse();
        var id = teamResponse.ExternalId;

        _tvpClient.Setup(x => x.GetTeamAsync(id, CancellationToken.None)).Returns(Task.FromResult(teamResponse));

        await _sut.GetTeamByIdAsync(id);

        _tvpClient.Verify(
            x => x.GetTeamAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetSubscriptionByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var id = "test";

        var subscriptionResponse = TestData.GetSubscription();
        _tvpClient.Setup(x => x.GetSubscriptionAsync(id, CancellationToken.None))
            .Returns(Task.FromResult(subscriptionResponse));
        await _sut.TryGetSubscriptionByIdAsync(id);
        _tvpClient.Verify(
            x => x.GetSubscriptionAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetIfSubscriptionExistAsync_WithExistingSubscription_ReturnsTrueAsync()
    {
        // Arrange
        var subscriptionResponse = TestData.GetSubscription();

        _tvpClient.Setup(x => x.GetSubscriptionAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(subscriptionResponse);

        // Act
        var result = await _sut.GetIfSubscriptionExistAsync(_subscriptionId);

        // Assert
        _tvpClient.Verify(x => x.GetSubscriptionAsync(_subscriptionId, CancellationToken.None), Times.Once);
        Assert.True(result);
    }

    [Fact]
    public async Task GetIfSubscriptionExistAsync_WithNonExistingSubscription_ReturnsFalseAsync()
    {
        // Act
        var result = await _sut.GetIfSubscriptionExistAsync(_subscriptionId);

        // Assert
        _tvpClient.Verify(x => x.GetSubscriptionAsync(_subscriptionId, CancellationToken.None), Times.Once);
        Assert.False(result);
    }

    [Fact]
    public async Task GetServiceCollectionIdAsync_WithProductionId_ReturnsServiceCollectionIdAsync()
    {
        // Arrange
        var getServiceCollectionsResponse = new GetServiceCollectionsResponse
            { ServiceCollections = new Collection<ServiceCollection> { new() { Id = _serviceCollectionId } } };
        _tvpClient.Setup(x => x.GetServiceCollectionsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(getServiceCollectionsResponse);

        // Assert
        var result = await _sut.GetServiceCollectionIdByServiceCollectionNameAsync(_productionId);

        // Verify
        _tvpClient.Verify(x => x.GetServiceCollectionsAsync(_productionId, It.IsAny<CancellationToken>()),
            Times.Once);
        Assert.Equal(_serviceCollectionId, result);
    }

    [Fact]
    public async Task GetServiceCollectionIdAsync_WithProductionIdButNoServiceCollection_ReturnsNullAsync()
    {
        // Arrange
        var getServiceCollectionsResponse = new GetServiceCollectionsResponse
            { ServiceCollections = new Collection<ServiceCollection>() };
        _tvpClient.Setup(x => x.GetServiceCollectionsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(getServiceCollectionsResponse);

        // Assert
        var result = await _sut.GetServiceCollectionIdByServiceCollectionNameAsync(_productionId);

        // Verify
        _tvpClient.Verify(x => x.GetServiceCollectionsAsync(_productionId, It.IsAny<CancellationToken>()),
            Times.Once);
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSubscriptionIdsForServiceCollectionAsync_WithServiceCollectionId_ReturnsSubscriptionIdsAsync()
    {
        // Arrange
        var getAllSubscriptionsResponse = new GetSubscriptionsResponse
            { Subscriptions = new Collection<Subscription> { new() { Id = _subscriptionId } } };
        _tvpClient
            .Setup(x => x.GetAllSubscriptionsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(getAllSubscriptionsResponse);

        // Assert
        var result = await _sut.GetSubscriptionIdsForServiceCollectionAsync(_serviceCollectionId);

        // Verify
        _tvpClient.Verify(
            x => x.GetAllSubscriptionsAsync(TvpResourceType.ServiceCollection, _serviceCollectionId, It.IsAny<int?>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        Assert.Equal(_subscriptionId, result.Single());
    }

    [Fact]
    public async Task DeleteSubscriptionAsync_WithSubscriptionId_CallsDeleteSubscriptionAsync()
    {
        // Assert
        await _sut.DeleteSubscriptionAsync(_subscriptionId);

        // Verify
        _tvpClient.Verify(x => x.DeleteSubscriptionAsync(_subscriptionId, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task TryGetSubscriptionByIdAsync_WithException_ShouldPassAsync()
    {
        var id = "test";

        _tvpClient.Setup(x => x.GetSubscriptionAsync(id, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.TryGetSubscriptionByIdAsync(id);

        // Assert
        Assert.Null(result);

        _tvpClient.Verify(
            x => x.GetSubscriptionAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetLocationByIdAsync_WithException_ShouldPassAsync()
    {
        var externalLocationId = "123";

        _tvpClient.Setup(x => x.GetLocationByIdAsync(externalLocationId, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));
        var result = await _sut.TryGetLocationByIdAsync(externalLocationId);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetLocationByIdAsync(
                It.Is<string>(x => x.Equals(externalLocationId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetGeoPolicyByExternalIdAsync_WithException_ShouldPassAsync()
    {
        var externalLocationId = "123";

        _tvpClient.Setup(x => x.GetGeoPolicyByExternalIdAsync(externalLocationId, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));
        var result = await _sut.TryGetGeoPolicyByExternalIdAsync(externalLocationId);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetGeoPolicyByExternalIdAsync(
                It.Is<string>(x => x.Equals(externalLocationId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task TryGetTeamByIdAsync_WithException_ShouldPassAsync()
    {
        var teamResponse = TestData.GetTeamResponse();
        var id = teamResponse.ExternalId;

        _tvpClient.Setup(x => x.GetTeamAsync(id, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.TryGetTeamByIdAsync(id);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetTeamAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetOfferByIdAsync_WithException_ShouldPassAsync()
    {
        var id = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";

        _tvpClient.Setup(x => x.GetOfferByIdAsync(id, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.GetOfferByIdAsync(id);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetOfferByIdAsync(
                It.Is<string>(x => x.Equals(id, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetEventScheduleByIdAsync_WithException_ShouldPassAsync()
    {
        var eventExternalId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        _tvpClient
            .Setup(x => x.GetEventScheduleAsync(eventExternalId, eventExternalId, null, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.GetEventScheduleByIdAsync(eventExternalId);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetEventScheduleAsync(
                It.Is<string>(x => x.Equals(eventExternalId, StringComparison.Ordinal)),
                It.Is<string>(x => x.Equals(eventExternalId, StringComparison.Ordinal)),
                null,
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetEventByIdAsync_WithException_ShouldPassAsync()
    {
        var eventId = "d81bfa6c-d5e5-4d8c-baad-68ea64a6e617";
        var includeSchedules = true;
        _tvpClient.Setup(x => x.GetEventByIdAsync(eventId, includeSchedules, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.GetEventByIdAsync(eventId, includeSchedules);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetEventByIdAsync(
                It.Is<string>(x => x.Equals(eventId, StringComparison.Ordinal)),
                It.Is<bool>(x => x.Equals(includeSchedules)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetProductionByIdAsync_WithException_ShouldPassAsync()
    {
        _tvpClient.Setup(x => x.GetProductionByIdAsync(_productionId, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.GetProductionByIdAsync(_productionId);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetProductionByIdAsync(It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetOperationalStateByIdAsync_WithNormalParameter_ShouldPassAsync()
    {
        var productionOperationalStateResponse = TestData.GetProductionOperationalStateResponse();
        _tvpClient.Setup(x => x.GetOperationalStateAsync(_productionId, CancellationToken.None))
            .Returns(Task.FromResult(productionOperationalStateResponse));

        await _sut.GetOperationalStateByIdAsync(_productionId);

        _tvpClient.Verify(
            x => x.GetOperationalStateAsync(It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GetOperationalStateByIdAsync_WithException_ShouldPassAsync()
    {
        _tvpClient.Setup(x => x.GetOperationalStateAsync(_productionId, CancellationToken.None))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        var result = await _sut.GetOperationalStateByIdAsync(_productionId);

        // Assert
        Assert.Null(result);
        _tvpClient.Verify(
            x => x.GetOperationalStateAsync(It.Is<string>(x => x.Equals(_productionId, StringComparison.Ordinal)),
                CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task
        AddSubscriptionToParentSubscriptionAsync_WithParameters_CallsAddSubscriptionToParentSubscriptionAsync()
    {
        // Arrange
        var parentSubscriptionId = "parentSubscriptionId";

        // Act
        await _sut.AddSubscriptionToParentSubscriptionAsync(parentSubscriptionId, _subscriptionId);

        // Assert
        _tvpClient.Verify(
            x => x.AddSubscriptionToParentSubscriptionAsync(parentSubscriptionId, _subscriptionId,
                It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AddSubscriptionToParentSubscriptionAsync_WithException_LogsWarningAsync()
    {
        // Arrange
        var parentSubscriptionId = "parentSubscriptionId";
        _tvpClient.Setup(x =>
                x.AddSubscriptionToParentSubscriptionAsync(It.IsAny<string>(), It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .Throws(new MKTvpClientException(string.Empty, 404, null, null, null));

        // Act
        await _sut.AddSubscriptionToParentSubscriptionAsync(parentSubscriptionId, _subscriptionId);

        // Assert
        _logger.VerifyLogging(
            $"Parent subscription {parentSubscriptionId} or child subscription {_subscriptionId} not found",
            LogLevel.Warning, Times.Once());
    }

    [Fact]
    public async Task GetEventFromPlayOptionsAsync_WhenEventIsFound_ReturnsEventAsync()
    {
        // Arrange
        var eventId = "**********";
        var tvpPlayoptionsResponse = new PlayOptionsEventResponse
        {
            EventId = eventId
        };
        _playOptionsClient
            .Setup(x => x.GetliveeventAsync(It.Is<string>(x => x == "**********"), It.Is<bool>(x => x),
                It.IsAny<CancellationToken>())).ReturnsAsync(tvpPlayoptionsResponse);

        // Act
        var result = await _sut.GetEventFromPlayOptionsAsync(eventId);

        // Assert
        Assert.Equal(result, tvpPlayoptionsResponse);
    }

    [Fact]
    public async Task GetEventFromPlayOptionsAsync_WhenEventIsNotFound_ReturnsNullAsync()
    {
        // Arrange
        var eventId = "**********";
        var headers =
            new ReadOnlyDictionary<string, IEnumerable<string>>(new Dictionary<string, IEnumerable<string>>());
        var notFoundException =
            new PlayOptionsClientException("Not found!", (int)HttpStatusCode.NotFound, "Not found!!!", headers, null);
        _playOptionsClient
            .Setup(x => x.GetliveeventAsync(It.Is<string>(x => x == "**********"), It.Is<bool>(x => x),
                It.IsAny<CancellationToken>())).ThrowsAsync(notFoundException);

        // Act
        var result = await _sut.GetEventFromPlayOptionsAsync(eventId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetHealthStatusAsync_WhenGetAllTeamsWorks_ReturnsHealthyAsync()
    {
        // Arrange
        _tvpClient.Setup(x => x.GetAllTeamAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new TeamsPaginatedResponse());

        // Act
        var result = await _sut.GetHealthStatusAsync();

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
    }

    [Fact]
    public async Task GetHealthStatusAsync_WhenGetAllTeamsDoesNotWork_ReturnsUnhealthyAsync()
    {
        // Arrange
        var headers =
            new ReadOnlyDictionary<string, IEnumerable<string>>(new Dictionary<string, IEnumerable<string>>());
        _tvpClient.Setup(x => x.GetAllTeamAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new MKTvpClientException("Everything is wrong!", 500, "Everything is wrong!", headers, null));

        // Act
        var result = await _sut.GetHealthStatusAsync();

        // Assert
        Assert.Equal(HealthStatus.Unhealthy, result.Status);
    }

    [Fact]
    public async Task
        GetProductionContentStateAsync_WhenMKTVPGetContentStateAsyncWorks_ReturnsTvpProductionContentStatesAsync()
    {
        // Arrange
        _tvpClient.Setup(x => x.GetContentStateAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ProductionContentStates
                { EventPartNames = new Collection<string> { TvpEventPartNamesProductionContentStates.Game } });

        // Act
        var result = await _sut.GetProductionContentStateAsync(_productionId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(TvpEventPartNamesProductionContentStates.Game, result.EventPartNames.First());
    }

    [Fact]
    public async Task GetProductionContentStateAsync_WhenMKTVPGetContentStateAsyncThrows_ShouldThrowAsWellAsync()
    {
        // Arrange
        _tvpClient.Setup(x => x.GetContentStateAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("This is a mocked exception."));

        // Act
        // Assert
        await Assert.ThrowsAsync<Exception>(() => _sut.GetProductionContentStateAsync(_productionId));
    }

    [Fact]
    public async Task
        PutProductionContentStateAsync_WhenMKTVPUpdateContentStateAsyncSucceeds_ShouldSucceedAsWellAndLogSuccessfullyAsync()
    {
        // Arrange
        var tvpProductionContentStates = new TvpProductionContentStates
            { EventPartNames = new Collection<string> { TvpEventPartNamesProductionContentStates.Game } };
        _tvpClient.Setup(x => x.UpdateContentStateAsync(It.IsAny<string>(), It.IsAny<ProductionContentStates>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _sut.PutProductionContentStateAsync(_productionId, tvpProductionContentStates);

        // Assert
        _logger.VerifyLogging(
            $"Successfully Updated Production Content State for ProductionId {_productionId} with Content State as {string.Join(',', tvpProductionContentStates.EventPartNames)}",
            LogLevel.Information, Times.Once());
    }

    [Fact]
    public async Task PutProductionContentStateAsync_WhenMKTVPUpdateContentStateAsyncThrows_ShouldThrowAsWellAsync()
    {
        // Arrange
        var tvpProductionContentStates = new TvpProductionContentStates
            { EventPartNames = new Collection<string> { TvpEventPartNamesProductionContentStates.Game } };
        _tvpClient.Setup(x => x.UpdateContentStateAsync(It.IsAny<string>(), It.IsAny<ProductionContentStates>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("This is a mocked exception."));

        // Act
        // Assert
        await Assert.ThrowsAsync<Exception>(() =>
                _sut.PutProductionContentStateAsync(_productionId, tvpProductionContentStates));
    }

    [Fact]
    public async Task
        DeleteProductionContentStateAsync_WhenMKTVPDeleteContentStateAsyncSucceeds_ShouldSucceedAsWellAndLogAsync()
    {
        // Arrange
        _tvpClient.Setup(x => x.DeleteContentStateAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _sut.DeleteProductionContentStateAsync(_productionId);

        // Assert
        _logger.VerifyLogging($"Successfully Deleted Production Content State for ProductionId {_productionId}",
            LogLevel.Information, Times.Once());
    }

    [Fact]
    public async Task DeleteProductionContentStateAsync_WhenMKTVPDeleteContentStateAsyncThrows_ShouldThrowAsWellAsync()
    {
        // Arrange
        _tvpClient.Setup(x => x.DeleteContentStateAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("This is a mocked exception."));

        // Act
        // Assert
        await Assert.ThrowsAsync<Exception>(() => _sut.DeleteProductionContentStateAsync(_productionId));
    }
}
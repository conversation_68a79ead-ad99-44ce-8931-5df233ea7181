// "//-----------------------------------------------------------------------".
// <copyright file="DmmClientServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Tests.Dmm.Services
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq.Expressions;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Dmm.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Dmm.Services;
    using NSubstitute;
    using Refit;
    using Xunit;

    /// <summary>
    /// The TvpClientServiceTests.
    /// </summary>
    public class DmmClientServiceTests
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The IReadReposiroty for GmsGame.
        /// </summary>
        private readonly IQueryableRepository<GmsGame> gmsGameRepository;

        /// <summary>
        /// The looger.
        /// </summary>
        private readonly ILogger<DmmClientService> logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The Dmm client.
        /// </summary>
        private readonly IDmmClient dmmClient;

        /// <summary>
        /// The Dmm client to test.
        /// </summary>
        private readonly DmmClientService sut;

        /// <summary>
        /// The TvpClientService.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DmmClientServiceTests"/> class.
        /// </summary>
        public DmmClientServiceTests()
        {
            this.repositoryFactory = Substitute.For<IQueryableRepositoryFactory>();
            this.gmsGameRepository = Substitute.For<IQueryableRepository<GmsGame>>();
            this.repositoryFactory.Resolve<GmsGame>().Returns(this.gmsGameRepository);
            this.logger = Substitute.For<ILogger<DmmClientService>>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new DmmProfile())).CreateMapper();
            this.dmmClient = Substitute.For<IDmmClient>();
            this.tvpClientService = Substitute.For<ITvpClientService>();
            this.sut = new DmmClientService(this.repositoryFactory, this.logger, this.mapper, this.dmmClient, this.tvpClientService);
        }

        /// <summary>
        /// Start content Protection return ok.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StartContentProtection_EmptyContentPRotectionUrlAsync()
        {
            // arrange
            var content = new ContentProtectionStatus { Status = "Stopped" };
            var gmsGame = new GmsGame
            {
                Id = "0022411517",
                ContentProtectionUrls = new List<ContentProtectionDetails>(),
                Media = new MediaInfo[]
                {
                    new MediaInfo()
                    {
                        Id = 1000242,
                    },
                },
            };
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            HttpResponseMessage message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            ApiResponse<ContentProtectionStatus> response = new ApiResponse<ContentProtectionStatus>(message, content, null);
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(gmsGame.Id).Returns(response);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
            this.dmmClient.PostStartContentProtectionAsync(Arg.Any<ContentProtection>()).Returns(response);
            this.dmmClient.PostStartLiveProductionAsync(Arg.Any<LiveProductionServicesDetails>())
                .Returns(liveResponse);

            var contentProtection = new ContentProtection()
            {
                GameId = gmsGame.Id,
                Clients = new List<ClientContentProtection>(),
            };

            // act
            var result = await this.sut.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);

            // assert
            this.logger.Received().LogError($"No valid Content Protection data for {gmsGame.Id}");
            Assert.Equal(HttpStatusCode.BadRequest, result.StatusCode);
        }

        /// <summary>
        /// Start content Protection return ok.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StartContentProtection_StatusNoStarted_returnOKAsync()
        {
            // arrange
            var gameId = "0022411517";
            var content = new ContentProtectionStatus { Status = "Stopped" };
            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails>()
                {
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "secondary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/yqpu-cb77-qy2e-ma5w-975s",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/yqpu-cb77-qy2e-ma5w-975s",
                        StreamId = "YTID-33",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        Name = "facebook",
                        PrimaryStreamUrl = "rtmps://live-api-s.facebook.com:443/rtmp/1142741283837190?s_asc=1&s_bl=1&s_oil=2&s_psm=1&s_pub=1&s_sw=0&s_tids=1&s_vt=api-s&a=AbyoNuQOdfFGTu4E",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/ckm0-8qfd-weuj-ekkj-4d6d",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/ckm0-8qfd-weuj-ekkj-4d6d",
                        StreamId = "YTID-13",
                    },
                },
                Media = new List<MediaInfo>(),
            };
            HttpResponseMessage message;
            ApiResponse<ContentProtectionStatus> response;
            using (message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK })
            {
                using (response = new ApiResponse<ContentProtectionStatus>(message, content, null))
                {
                    this.dmmClient.GetStatusContentProtectionAsync(gameId).Returns(response);
                    this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
                    this.dmmClient.PostStartContentProtectionAsync(Arg.Any<ContentProtection>()).Returns(response);

                    var contentProtection = new ContentProtection()
                    {
                        GameId = gmsGame.Id,
                        Clients = new List<ClientContentProtection>(),
                    };

                    // act
                    var result = await this.sut.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);

                    // assert
                    Assert.Equal(HttpStatusCode.BadRequest, result.StatusCode);
                }
            }
        }

        /// <summary>
        /// Start content Protection when no valid content Protection URLs for.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StartContentProtection_InvalidInputContentProtectionUrlsAsync()
        {
            // arrange
            var gameId = "0022411517";
            var content = new ContentProtectionStatus { Status = "Stopped" };
            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails>()
                {
                    new ContentProtectionDetails
                    {
                        Input = null,
                        Angle = "secondary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/yqpu-cb77-qy2e-ma5w-975s",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/yqpu-cb77-qy2e-ma5w-975s",
                        StreamId = "YTID-33",
                    },
                    new ContentProtectionDetails
                    {
                        Input = null,
                        Angle = "primary",
                        Name = "facebook",
                        PrimaryStreamUrl = "rtmps://live-api-s.facebook.com:443/rtmp/1142741283837190?s_asc=1&s_bl=1&s_oil=2&s_psm=1&s_pub=1&s_sw=0&s_tids=1&s_vt=api-s&a=AbyoNuQOdfFGTu4E",
                    },
                    new ContentProtectionDetails
                    {
                        Input = null,
                        Angle = "primary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/ckm0-8qfd-weuj-ekkj-4d6d",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/ckm0-8qfd-weuj-ekkj-4d6d",
                        StreamId = "YTID-13",
                    },
                },
                Media = new List<MediaInfo>(),
            };
            HttpResponseMessage message;
            ApiResponse<ContentProtectionStatus> response;
            using (message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK })
            {
                using (response = new ApiResponse<ContentProtectionStatus>(message, content, null))
                {
                    this.dmmClient.GetStatusContentProtectionAsync(gameId).Returns(response);
                    this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
                    this.dmmClient.PostStartContentProtectionAsync(Arg.Any<ContentProtection>()).Returns(response);

                    var contentProtection = new ContentProtection()
                    {
                        GameId = gmsGame.Id,
                        Clients = new List<ClientContentProtection>(),
                    };

                    // act
                    var result = await this.sut.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);

                    // assert
                    this.logger.Received().LogError($"No valid Content Protection data for {gameId}");
                    Assert.Equal(HttpStatusCode.BadRequest, result.StatusCode);
                }
            }
        }

        /// <summary>
        /// Start Content Protection Call return Not modified.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StartContentProtection_StatusStarted_returnNoModifiedAsync()
        {
            var productionId = "g0022411517uta1000242ind";
            var gameId = "0022411517";
            var content = new ContentProtectionStatus { Status = "Started" };
            var contentProtectionUrl = new ContentProtectionDetails
            {
                Angle = "secondary",
                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                Input = "CLE-2",
                StreamId = "YTIE-24",
                Name = "youtube",
            };

            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails> { contentProtectionUrl },
                Media = new List<MediaInfo>(),
            };
            HttpResponseMessage message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            ApiResponse<ContentProtectionStatus> response = new ApiResponse<ContentProtectionStatus>(message, content, null);
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(gameId).Returns(response);
            this.dmmClient.GetStatusLiveProductionAsync(productionId, gameId).Returns(liveResponse);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>()).Returns([gmsGame]);
            this.dmmClient.PostStartContentProtectionAsync(Arg.Any<ContentProtection>()).Returns(response);

            var contentProtection = new ContentProtection()
            {
                GameId = gmsGame.Id,
                Clients = new List<ClientContentProtection>(),
            };

            // act
            var result = await this.sut.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);

            // assert
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
        }

        /// <summary>
        /// Stop protection response 400.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StopContentProtectioncall_status_notFoundAsync()
        {
            var productionId = "g0022411517uta1000242ind";
            var gameId = "0022411517";
            var contentProtectionUrl = new ContentProtectionDetails
            {
                Angle = "secondary",
                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                Input = "CLE-2",
                StreamId = "YTIE-24",
                Name = "youtube",
            };

            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails> { contentProtectionUrl },
                Media = new List<MediaInfo>(),
            };

            var productionId1 = "g0022370330mem1000324dal";
            var productionId2 = "g0022370330mem1000258dal";

            ProductionsPaginatedResponse productionsRespose = new ProductionsPaginatedResponse
            {
                SkipToken = "null",
                Productions = new Collection<ProductionResponse>()
                {
                    new ProductionResponse
                    {
                        ExternalId = productionId1,
                    },
                    new ProductionResponse
                    {
                        ExternalId = productionId2,
                    },
                },
            };

            ProductionOperationalState productionOperationalState1 = new ProductionOperationalState
            {
                ExternalId = productionId1,
                OperationalState = "Over",
            };

            ProductionOperationalState productionOperationalState2 = new ProductionOperationalState
            {
                ExternalId = productionId2,
                OperationalState = "Over",
            };

            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
            var response = new ApiResponse<ContentProtectionStatus>(message, null, null);
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(Arg.Any<string>()).Returns(response);
            this.dmmClient.GetStatusLiveProductionAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(liveResponse);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>()).Returns([gmsGame]);
            this.dmmClient.PostStopContentProtectionAsync(gmsGame.Id).Returns(response);
            this.dmmClient.PostStopLiveProductionAsync(productionId, gameId).Returns(liveResponse);
            this.tvpClientService.GetAllProductionsByGameIdAsync(gmsGame.Id).Returns(productionsRespose);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[0].ExternalId).Returns(productionOperationalState1);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[1].ExternalId).Returns(productionOperationalState2);

            // act
            var result = await this.sut.StopContentProtectionAsync(gameId).ConfigureAwait(false);

            // assert
            Assert.Equal(HttpStatusCode.BadRequest, result.StatusCode);
        }

        /// <summary>
        /// StartContent protection response 400.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StartContentProtectioncall_status_notFoundAsync()
        {
            var gameId = "0022411517";
            var contentProtectionUrl = new ContentProtectionDetails
            {
                Angle = "secondary",
                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                Input = "CLE-2",
                StreamId = "YTIE-24",
                Name = "youtube",
            };

            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails> { contentProtectionUrl },
                Media = new List<MediaInfo>(),
            };
            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
            var response = new ApiResponse<ContentProtectionStatus>(message, null, null);
            var responseClientMessage = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            var responseClient = new ApiResponse<ContentProtectionStatus>(responseClientMessage, null, null);
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(Arg.Any<string>()).Returns(response);
            this.dmmClient.GetStatusLiveProductionAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(liveResponse);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
            this.dmmClient.PostStartContentProtectionAsync(Arg.Any<ContentProtection>()).Returns(responseClient);

            var contentProtection = new ContentProtection()
            {
                GameId = gmsGame.Id,
                Clients = new List<ClientContentProtection>()
                {
                    new ClientContentProtection()
                    {
                        Name = "youtube",
                        Streams = new List<StreamContentProtection>()
                        {
                            new StreamContentProtection()
                            {
                                Angle = "secondary",
                                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                                Input = "CLE-2",
                                StreamId = "YTIE-24",
                            },
                        },
                    },
                },
            };

            // act
            var result = await this.sut.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);

            // assert
            this.logger.Received().LogInformation($"Successfully started Content Protection for: {gameId}");
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
        }

        /// <summary>
        /// Stop content PRotection return ok.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StopContentProtection_StatusStarted_returnOkAsync()
        {
            var productionId = "g0022411517uta1000242ind";
            var gameId = "0022411517";
            var content = new ContentProtectionStatus { Status = "Started" };
            var contentProtectionUrl = new ContentProtectionDetails
            {
                Angle = "secondary",
                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                Input = "CLE-2",
                StreamId = "YTIE-24",
                Name = "youtube",
            };

            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails> { contentProtectionUrl },
                Media = new List<MediaInfo>(),
            };

            var productionId1 = "g0022370330mem1000324dal";
            var productionId2 = "g0022370330mem1000258dal";

            ProductionsPaginatedResponse productionsRespose = new ProductionsPaginatedResponse
            {
                SkipToken = "null",
                Productions = new Collection<ProductionResponse>()
                {
                    new ProductionResponse
                    {
                        ExternalId = productionId1,
                    },
                    new ProductionResponse
                    {
                        ExternalId = productionId2,
                    },
                },
            };

            ProductionOperationalState productionOperationalState1 = new ProductionOperationalState
            {
                ExternalId = productionId1,
                OperationalState = "Over",
            };

            ProductionOperationalState productionOperationalState2 = new ProductionOperationalState
            {
                ExternalId = productionId2,
                OperationalState = "Over",
            };

            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            var response = new ApiResponse<ContentProtectionStatus>(message, content, null);
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(Arg.Any<string>()).Returns(response);
            this.dmmClient.GetStatusLiveProductionAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(liveResponse);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
            this.dmmClient.PostStopContentProtectionAsync(gmsGame.Id).Returns(response);
            this.dmmClient.PostStopLiveProductionAsync(productionId, gameId).Returns(liveResponse);
            this.tvpClientService.GetAllProductionsByGameIdAsync(gmsGame.Id).Returns(productionsRespose);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[0].ExternalId).Returns(productionOperationalState1);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[1].ExternalId).Returns(productionOperationalState2);

            // act
            var result = await this.sut.StopContentProtectionAsync(gameId).ConfigureAwait(false);

            // assert
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
        }

        /// <summary>
        /// Stop content PRotection return not modified.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task StopContentProtection_StatusStopped_returnNotModifiedAsync()
        {
            var gameId = "0022411517";
            var content = new ContentProtectionStatus { Status = "Stopped" };
            var contentProtectionUrl = new ContentProtectionDetails
            {
                Angle = "secondary",
                BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                PrimaryStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/hyw4-jm1k-hh0m-06aw",
                Input = "CLE-2",
                StreamId = "YTIE-24",
                Name = "youtube",
            };

            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails> { contentProtectionUrl },
                Media = new List<MediaInfo>(),
            };

            var productionId1 = "g0022370330mem1000324dal";
            var productionId2 = "g0022370330mem1000258dal";

            ProductionsPaginatedResponse productionsRespose = new ProductionsPaginatedResponse
            {
                SkipToken = "null",
                Productions = new Collection<ProductionResponse>()
                {
                    new ProductionResponse
                    {
                        ExternalId = productionId1,
                    },
                    new ProductionResponse
                    {
                        ExternalId = productionId2,
                    },
                },
            };

            ProductionOperationalState productionOperationalState1 = new ProductionOperationalState
            {
                ExternalId = productionId1,
                OperationalState = "Over",
            };

            ProductionOperationalState productionOperationalState2 = new ProductionOperationalState
            {
                ExternalId = productionId2,
                OperationalState = "Over",
            };

            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            var response = new ApiResponse<ContentProtectionStatus>(message, content, null);
            var contentLiveProduction = new LiveProductionStatus() { Status = "Stopped" };
            ApiResponse<LiveProductionStatus> liveResponse = new ApiResponse<LiveProductionStatus>(message, contentLiveProduction, null);
            this.dmmClient.GetStatusContentProtectionAsync(Arg.Any<string>()).Returns(response);
            this.dmmClient.GetStatusLiveProductionAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(liveResponse);
            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns([gmsGame]);
            this.dmmClient.PostStopContentProtectionAsync(gmsGame.Id).Returns(response);
            this.tvpClientService.GetAllProductionsByGameIdAsync(gmsGame.Id).Returns(productionsRespose);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[0].ExternalId).Returns(productionOperationalState1);
            this.tvpClientService.GetOperationalStateByIdAsync(productionsRespose.Productions[1].ExternalId).Returns(productionOperationalState2);

            // act
            var result = await this.sut.StopContentProtectionAsync(gameId).ConfigureAwait(false);

            // assert
            this.logger.Received().LogInformation($"Content Protection stopped for {gameId}");
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
        }

        /// <summary>
        /// Get Content State status.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task GetContentProtection_StartedStatusAsync()
        {
            var productionId = "g0022411517uta1000242ind";
            var content = new ContentProtectionStatus { Status = "Started", Message = "GameID: 123 currently running" };
            HttpResponseMessage message;
            ApiResponse<ContentProtectionStatus> response;
            message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            response = new ApiResponse<ContentProtectionStatus>(message, content, null);
            this.dmmClient.GetStatusContentProtectionAsync(Arg.Any<string>()).Returns(response);

            // act
            var result = await this.sut.GetContentProtectionStatusAsync(productionId).ConfigureAwait(false);

            // assert
            Assert.Equal(response, result);
        }
    }
}

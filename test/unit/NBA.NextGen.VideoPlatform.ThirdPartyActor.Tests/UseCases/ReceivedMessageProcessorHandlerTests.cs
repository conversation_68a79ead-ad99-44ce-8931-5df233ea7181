using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MST.Common.Messaging;
using NBA.NextGen.Shared.Unit;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.CreateOttEndpoint;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.DeleteOttEndpoint;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Queries;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.Common;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Tests.WorkFlowsMessages;
using Xunit;

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Tests.UseCases;

public class ReceivedMessageProcessorHandlerTests
{
    private readonly Mock<ILogger<ReceivedMessageProcessorHandler>> _logger;
    
    private readonly Mock<IMediator> _mediator;
    
    private readonly ReceivedMessageProcessorHandler _sut;

    public ReceivedMessageProcessorHandlerTests()
    {
        _logger = new Mock<ILogger<ReceivedMessageProcessorHandler>>();
        _mediator = new Mock<IMediator>();
        var messageSender = new Mock<IMessageSender<InfrastructureStateChangedEventSQS>>();
        var messageSenderFactory = new Mock<IMessageSenderFactory>();
        var mapper = new MapperConfiguration(cfg => cfg.AddProfile(new ThirdPartyActorProfile())).CreateMapper();
        var myConfiguration = new Dictionary<string, string>
        {
            { "QueueNames:ThridPartyMessageReceiver", "test" }
        };

        IConfiguration configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(myConfiguration)
            .Build();

        messageSenderFactory.Setup(x => x.Resolve<InfrastructureStateChangedEventSQS>())
            .Returns(messageSender.Object);

        configuration.GetSection("QueueNames");

        _sut = new ReceivedMessageProcessorHandler(
            _logger.Object,
            _mediator.Object,
            mapper,
            messageSenderFactory.Object);
    }

    [Fact]
    public async Task ProcessMessage_CallQueryOrchestratorAsync()
    {
        var message = new ReceivedMessage
        {
            Content = TestsWorkflows.DeleteWorkFlow()
        };

        await _sut.ProcessMessage(message);

        _mediator.Verify(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }


    [Fact]
    public async Task ProcessMessage_DeleteProcessAsync()
    {
        var message = new ReceivedMessage
        {
            Content = TestsWorkflows.DeleteWorkFlow()
        };

        _mediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration);

        await _sut.ProcessMessage(message);

        _mediator.Verify(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        _mediator.Verify(
            x => x.Send(It.IsAny<SendOttEndpointStateChangeRequestAcknowldgementCommand>(),
                It.IsAny<CancellationToken>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<DeleteOttEndpointCommand>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _logger.VerifyLogging("Endpoint Deleted for g0022471878nyk1000397atl", LogLevel.Information, Times.Once());
    }

    [Fact]
    public async Task ProcessMessage_CreateProcessAsync()
    {
        var message = new ReceivedMessage
        {
            Content = TestsWorkflows.CreateWorkFlow()
        };

        _mediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration);

        await _sut.ProcessMessage(message);

        _mediator.Verify(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        _mediator.Verify(
            x => x.Send(It.IsAny<SendOttEndpointStateChangeRequestAcknowldgementCommand>(),
                It.IsAny<CancellationToken>()), Times.Once);
        _mediator.Verify(x => x.Send(It.IsAny<CreateOttEndpointCommand>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _logger.VerifyLogging("Endpoint Created for 0022471733", LogLevel.Information, Times.Once());
    }
}
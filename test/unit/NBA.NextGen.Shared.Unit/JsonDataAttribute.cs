// "//-----------------------------------------------------------------------".
// <copyright file="JsonDataAttribute.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.Shared.Unit
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Reflection;
    using Ardalis.GuardClauses;
    using AutoBogus;
    using Newtonsoft.Json.Linq;
    using Xunit.Sdk;

    /// <summary>
    /// Class for JSON Data Attribute.
    /// </summary>
    /// <seealso cref="Xunit.Sdk.DataAttribute" />
    [ExcludeFromCodeCoverage]
    public sealed class JsonDataAttribute : DataAttribute
    {
        /// <summary>
        /// The data file path.
        /// </summary>
        private readonly string dataFilePath;

        /// <summary>
        /// Initializes a new instance of the <see cref="JsonDataAttribute"/> class.
        /// </summary>
        /// <param name="dataFilePath">The data file path.</param>
        public JsonDataAttribute(string dataFilePath)
        {
            this.dataFilePath = dataFilePath;
        }

        /// <summary>
        /// Gets the data file path.
        /// </summary>
        /// <value>
        /// The data file path.
        /// </value>
        public string DataFilePath => this.dataFilePath;

        /// <summary>
        /// Gets the data.
        /// </summary>
        /// <param name="testMethod">The test method.</param>
        /// <returns>Object Array of Test data.</returns>
        public override IEnumerable<object[]> GetData(MethodInfo testMethod)
        {
            Guard.Against.Null(testMethod, nameof(testMethod));

            var dataText = System.IO.File.ReadAllText(@".\TestData\" + this.dataFilePath);

            JArray dataArray = JArray.Parse(dataText);
            var parameters = testMethod.GetParameters();

            foreach (var obj in dataArray.Children())
            {
                List<object> testData = new List<object>();

                JObject testObj = (JObject)obj;
                foreach (var parameter in parameters)
                {
                    var type = parameter.ParameterType;

                    if (parameter.GetCustomAttribute<BogusAttribute>() != null)
                    {
                        var overrideType = parameter.GetCustomAttribute(typeof(BogusOverrideAttribute));

                        var generateMethod = typeof(AutoFaker)
                                .GetMethod(
                                           "Generate",
                                           BindingFlags.Public | BindingFlags.Static,
                                           null,
                                           new[] { typeof(Action<IAutoGenerateConfigBuilder>) },
                                           null)
                                .MakeGenericMethod(type);

                        if (overrideType == null)
                        {
                            testData.Add(generateMethod.Invoke(null, new object[] { null }));
                        }
                        else
                        {
                            Action<IAutoGenerateConfigBuilder> builder = (builder) => builder.WithOverride(
                                (AutoGeneratorOverride)Activator.CreateInstance(((BogusOverrideAttribute)overrideType).OverrideType));

                            testData.Add(generateMethod.Invoke(null, new object[] { builder }));
                        }
                    }
                    else
                    {
                        var testMethodParameter = testObj.GetValue(parameter.Name, StringComparison.InvariantCultureIgnoreCase);
                        testData.Add(testMethodParameter.ToObject(type));
                    }
                }

                yield return testData.ToArray();
            }
        }
    }
}

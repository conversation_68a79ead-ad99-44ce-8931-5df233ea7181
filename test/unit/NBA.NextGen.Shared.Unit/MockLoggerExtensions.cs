// "//-----------------------------------------------------------------------".
// <copyright file="MockLoggerExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.Shared.Unit
{
    using System;
    using Microsoft.Extensions.Logging;
    using Moq;

    /// <summary>
    /// Mock logger extensions.
    /// </summary>
    public static class MockLoggerExtensions
    {
        /// <summary>
        /// Verifies the logging.
        /// </summary>
        /// <typeparam name="T">The type.</typeparam>
        /// <param name="logger">The logger.</param>
        /// <param name="expectedMessage">The expected message.</param>
        /// <param name="expectedLogLevel">The expected log level.</param>
        /// <param name="times">The times.</param>
        /// <returns> A Mock logger.</returns>
        public static Mock<ILogger<T>> VerifyLogging<T>(this Mock<ILogger<T>> logger, string expectedMessage, LogLevel expectedLogLevel = LogLevel.Debug, Times? times = null)
        {
            times ??= Times.Once();

            Func<object, Type, bool> state = (v, t) => v.ToString() == expectedMessage;

            logger?.Verify(
                x => x.Log(
                    It.Is<LogLevel>(l => l == expectedLogLevel),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => state(v, t)),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)), (Times)times);

            return logger;
        }

        /// <summary>
        /// Verifies anything.
        /// </summary>
        /// <typeparam name="T">The type.</typeparam>
        /// <param name="logger">The logger.</param>
        /// <param name="expectedLogLevel">The expected log level.</param>
        /// <param name="times">The times.</param>
        /// <returns> A Mock logger.</returns>
        public static Mock<ILogger<T>> VerifyAnyLogging<T>(this Mock<ILogger<T>> logger, LogLevel expectedLogLevel = LogLevel.Debug, Times? times = null)
        {
            times ??= Times.Once();

            Func<object, Type, bool> state = (v, t) => true;

            logger?.Verify(
                x => x.Log(
                    It.Is<LogLevel>(l => l == expectedLogLevel),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => state(v, t)),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)), (Times)times);

            return logger;
        }

        /// <summary>
        /// Setups anything.
        /// </summary>
        /// <typeparam name="T">The type.</typeparam>
        /// <param name="logger">The logger.</param>
        /// <returns> A Mock logger.</returns>
        public static Mock<ILogger<T>> SetupAnyLogging<T>(this Mock<ILogger<T>> logger)
        {
            logger?.Setup(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()));

            return logger;
        }
    }
}

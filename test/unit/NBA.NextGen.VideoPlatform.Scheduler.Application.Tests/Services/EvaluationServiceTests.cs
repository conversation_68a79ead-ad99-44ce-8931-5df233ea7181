// "//-----------------------------------------------------------------------".
// <copyright file="EvaluationServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.Services.Evaluation;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.Evaluation.Commands.Evaluate;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Xunit;

    /// <summary>
    /// Test for the service for evaluating the whole state against the schedule.
    /// </summary>
    public class EvaluationServiceTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<EvaluationService>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformSchedule>> mockVideoPlatformScheduleRepository;

        /// <summary>
        /// When setting AdjustedWorkflowRequestTime relative to LiveEventTime in our test cases,
        /// we need to use the same value in both the inputs and expected values. Use this constant.
        /// </summary>
        private readonly TimeSpan adjustedTimeSetByScheduleSerializer = TimeSpan.Zero;

        /// <summary>
        /// The DateTime now field.
        /// </summary>
        private readonly DateTime now;

        /// <summary>
        /// Initializes a new instance of the <see cref="EvaluationServiceTests"/> class.
        /// </summary>
        public EvaluationServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
            this.mockLogger = this.CreateLoggerMock<EvaluationService>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockVideoPlatformScheduleRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformSchedule>();
            this.now = new DateTime(2020, 1, 1);
        }

        /// <summary>
        /// Gets the <see cref="EvaluateCommandHandler"/>.
        /// </summary>
        /// <value>
        /// The <see cref="EvaluateCommandHandler"/>.
        /// </value>
        private EvaluationService EvaluationService =>
            new EvaluationService(
                this.mockLogger.Object,
                this.mockRepositoryFactory);

        /// <summary>
        /// GetScheduleByScheduleIdAsync with schedule id returns VideoPlatformSchedule.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task GetScheduleByScheduleIdAsync_WithScheduleId_ReturnsVideoPlatformScheduleAsync()
        {
            // Arrange
            var videoPlatformSchedule = this.GetSchedulesInTimeRange().First();
            this.mockVideoPlatformScheduleRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(videoPlatformSchedule);

            // Act
            var result = await this.EvaluationService.GetScheduleByScheduleIdAsync(videoPlatformSchedule.Id).ConfigureAwait(false);

            // Assert
            Assert.Equal(videoPlatformSchedule.Id, result.Id);
            Assert.Equal(videoPlatformSchedule.RequestorLiveEventId, result.RequestorLiveEventId);
            Assert.Equal(videoPlatformSchedule.WorkflowIntents, result.WorkflowIntents);
        }

        /// <summary>
        /// GetSchedulesByTimeRangeAsync with time range returns VideoPlatformSchedules within range.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task GetSchedulesByTimeRangeAsync_WithTimeRange_ReturnsVideoPlatformSchedulesWithinRangeAsync()
        {
            // Arrange
            var videoPlatformSchedules = this.GetSchedulesInTimeRange();
            var videoPlatformSchedule = videoPlatformSchedules.First();
            var adjustedWorkflowRequestTime = videoPlatformSchedule.WorkflowIntents.First().AdjustedWorkflowRequestTime;
            this.mockVideoPlatformScheduleRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>())).ReturnsAsync(videoPlatformSchedules);

            // Act
            var resultVideoPlatformSchedules = await this.EvaluationService.GetSchedulesByTimeRangeAsync(this.now, adjustedWorkflowRequestTime).ConfigureAwait(false);

            // Assert
            var resultVideoPlatformSchedule = resultVideoPlatformSchedules.First();

            Assert.NotNull(resultVideoPlatformSchedules);
            Assert.Equal(videoPlatformSchedules.Count(), resultVideoPlatformSchedules.Count());
            Assert.Equal(videoPlatformSchedule.Id, resultVideoPlatformSchedule.Id);
            Assert.Equal(videoPlatformSchedule.RequestorLiveEventId, resultVideoPlatformSchedule.RequestorLiveEventId);
            Assert.Equal(videoPlatformSchedule.WorkflowIntents, resultVideoPlatformSchedule.WorkflowIntents);
            Assert.Equal(videoPlatformSchedule.ConcurrencyToken, resultVideoPlatformSchedule.ConcurrencyToken);
            Assert.Equal(videoPlatformSchedule.RequestorEventType, resultVideoPlatformSchedule.RequestorEventType);
            Assert.Equal(videoPlatformSchedule.RequestorId, resultVideoPlatformSchedule.RequestorId);
            Assert.Equal(videoPlatformSchedule.RequestorIdentity, resultVideoPlatformSchedule.RequestorIdentity);
        }

        /// <summary>
        /// EvaluateAndClassifySchedules with SchedulesWithWorkflowDetails of each type returns one ScheduleWithWorkflowDetails per type.
        /// </summary>
        [Fact]
        public void EvaluateAndClassifySchedules_WithSchedulesWithWorkflowDetailsOfEachType_ReturnsOneScheduleWithWorkflowDetailsPerType()
        {
            // Arrange
            var schedulesWithWorkflowDetails = this.GetSchedulesToBeClassified();
            var evaluateCommand = new EvaluateCommand
            {
                CurrentEvaluation = this.now.AddMinutes(1),
                LastEvaluation = this.now,
            };

            // Act
            var classifiedSchedules = this.EvaluationService.EvaluateAndClassifySchedules(evaluateCommand, schedulesWithWorkflowDetails);

            // Assert
            Assert.Equal(schedulesWithWorkflowDetails.Count(), classifiedSchedules.Count);
            Assert.Single(classifiedSchedules[ScheduleEvaluationClassification.Past]);
            Assert.Single(classifiedSchedules[ScheduleEvaluationClassification.Future]);
            Assert.Single(classifiedSchedules[ScheduleEvaluationClassification.Requested]);
            Assert.Single(classifiedSchedules[ScheduleEvaluationClassification.Upcoming]);
        }

        /// <summary>
        /// Gets the schedules in time range.
        /// </summary>
        /// <returns>The schedules.</returns>
        private IEnumerable<VideoPlatformSchedule> GetSchedulesInTimeRange()
        {
            var eventTime = this.now.AddHours(1);
            var schedule = new VideoPlatformSchedule
            {
                Id = Guid.NewGuid().ToString(),
                RequestorLiveEventId = "RequestorLiveEventId",
                ConcurrencyToken = "ConcurrencyToken",
                RequestorEventType = "RequestorEventType",
                RequestorId = "RequestorId",
                RequestorIdentity = "RequestorIdentity",
                RequestorLiveEventScheduleId = "RequestorLiveEventScheduleId",
                WorkflowIntents = new List<VideoPlatformWorkflowIntent>
                {
                    new VideoPlatformWorkflowIntent
                    {
                        WorkflowId = "WorkflowId",
                        ChannelId = "ChannelId",
                        LiveEventTime = eventTime,
                        AdjustedWorkflowRequestTime = eventTime + this.adjustedTimeSetByScheduleSerializer,
                    },
                },
            };

            return new List<VideoPlatformSchedule>()
            {
                schedule,
            };
        }

        /// <summary>
        /// Gets the flattened schedule details.
        /// </summary>
        /// <returns>
        /// The schedule and workflow details.
        /// </returns>
        private IEnumerable<ScheduleWithWorkflowDetails> GetSchedulesToBeClassified()
        {
            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent
            {
                WorkflowId = "WorkflowId",
                ChannelId = "ChannelId",
                LiveEventTime = this.now,
                AdjustedWorkflowRequestTime = this.now + this.adjustedTimeSetByScheduleSerializer,
            };
            var videoPlatformSchedule = new VideoPlatformSchedule
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowIntents = new List<VideoPlatformWorkflowIntent> { videoPlatformWorkflowIntent },
            };

            return new List<ScheduleWithWorkflowDetails>
            {
                new ScheduleWithWorkflowDetails
                {
                    Schedule = videoPlatformSchedule,
                    WorkflowIntent = videoPlatformWorkflowIntent,
                    ScheduleId = videoPlatformSchedule.Id,
                    WorkflowId = videoPlatformWorkflowIntent.WorkflowId,
                    ChannelId = videoPlatformWorkflowIntent.ChannelId,
                    VideoPlatformActorSpecificDetails = videoPlatformWorkflowIntent.VideoPlatformActorSpecificDetails,
                    LiveEventTime = videoPlatformWorkflowIntent.LiveEventTime.AddHours(-2),
                    AdjustedWorkflowRequestTime = videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime.AddHours(-2),
                },
                new ScheduleWithWorkflowDetails
                {
                    Schedule = videoPlatformSchedule,
                    WorkflowIntent = videoPlatformWorkflowIntent,
                    ScheduleId = videoPlatformSchedule.Id,
                    WorkflowId = videoPlatformWorkflowIntent.WorkflowId,
                    ChannelId = videoPlatformWorkflowIntent.ChannelId,
                    VideoPlatformActorSpecificDetails = videoPlatformWorkflowIntent.VideoPlatformActorSpecificDetails,
                    LiveEventTime = videoPlatformWorkflowIntent.LiveEventTime.AddSeconds(5),
                    AdjustedWorkflowRequestTime = videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime.AddSeconds(5),
                },
                new ScheduleWithWorkflowDetails
                {
                    Schedule = videoPlatformSchedule,
                    WorkflowIntent = videoPlatformWorkflowIntent,
                    ScheduleId = videoPlatformSchedule.Id,
                    WorkflowId = videoPlatformWorkflowIntent.WorkflowId,
                    ChannelId = videoPlatformWorkflowIntent.ChannelId,
                    VideoPlatformActorSpecificDetails = videoPlatformWorkflowIntent.VideoPlatformActorSpecificDetails,
                    LiveEventTime = videoPlatformWorkflowIntent.LiveEventTime.AddHours(2),
                    AdjustedWorkflowRequestTime = videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime.AddHours(2),
                },
                new ScheduleWithWorkflowDetails
                {
                    Schedule = videoPlatformSchedule,
                    WorkflowIntent = videoPlatformWorkflowIntent,
                    ScheduleId = videoPlatformSchedule.Id,
                    WorkflowId = videoPlatformWorkflowIntent.WorkflowId,
                    ChannelId = videoPlatformWorkflowIntent.ChannelId,
                    VideoPlatformActorSpecificDetails = videoPlatformWorkflowIntent.VideoPlatformActorSpecificDetails,
                    LiveEventTime = videoPlatformWorkflowIntent.LiveEventTime.AddHours(5),
                    AdjustedWorkflowRequestTime = videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime.AddHours(5),
                },
            };
        }
    }
}

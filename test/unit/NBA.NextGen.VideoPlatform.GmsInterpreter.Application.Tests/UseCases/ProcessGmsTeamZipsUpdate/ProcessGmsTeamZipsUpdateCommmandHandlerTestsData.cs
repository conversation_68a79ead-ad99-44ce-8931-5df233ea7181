// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsTeamZipsUpdateCommmandHandlerTestsData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsTeamZipsUpdate
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsTeamZipsUpdate;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The data for the ProcessGmsTeamZipsUpdateCommmandHandlerTests.
    /// </summary>
    public static class ProcessGmsTeamZipsUpdateCommmandHandlerTestsData
    {
        /// <summary>
        /// Gets the testing data for Handle_CorrectParameters_WorkCorrectly.
        /// </summary>
        public static IEnumerable<object[]> HandleCorrectParametersWorkCorrectly =>
            new List<object[]>
            {
                new object[]
                {
                    new ProcessGmsTeamZipsUpdateCommand { Id = "Id" },
                    new GmsTeamZips
                    {
                        Id = "Id",
                        Abbr = "ABBR",
                        Markets = new List<Market>()
                        {
                            new Market
                            {
                                MarketCode = "RSN",
                                Zips = new List<string> { "1", "2" },
                            },
                            new Market
                            {
                                MarketCode = "OTA",
                                Zips = new List<string> { "3", "4" },
                            },
                        },
                    },
                    new EsniAudience[]
                    {
                        new EsniAudience
                        {
                            Description = "Team zips for the team abbr and market code rsn",
                            Id = "NBA.audience.abbr.rsn",
                            Match = "ANY",
                            PrismaId = "/NBA/audience/abbr/rsn",
                            ZipCodes = new List<string> { "1", "2" },
                        },
                        new EsniAudience
                        {
                            Description = "Team zips for the team abbr and market code ota",
                            Id = "NBA.audience.abbr.ota",
                            Match = "ANY",
                            PrismaId = "/NBA/audience/abbr/ota",
                            ZipCodes = new List<string> { "3", "4" },
                        },
                    },
                    new EsniAudience[]
                    {
                        new EsniAudience
                        {
                            Description = "United States EsniAudience wrapper for EsniAudience /NBA/audience/abbr/rsn",
                            EsniAudiences = new List<EsniAudience>
                            {
                                new EsniAudience { Match = "ANY", XlinkHref = "/NBA/audience/abbr/rsn" },
                                new EsniAudience { XlinkHref = "/NBA/audience/us" },
                            },
                            Id = "NBA.audience.us.abbr.rsn",
                            Match = "ALL",
                            PrismaId = "/NBA/audience/us/abbr/rsn",
                        },
                        new EsniAudience
                        {
                            Description = "United States EsniAudience wrapper for EsniAudience /NBA/audience/abbr/ota",
                            EsniAudiences = new List<EsniAudience>
                            {
                                new EsniAudience { Match = "ANY", XlinkHref = "/NBA/audience/abbr/ota" },
                                new EsniAudience { XlinkHref = "/NBA/audience/us" },
                            },
                            Id = "NBA.audience.us.abbr.ota",
                            Match = "ALL",
                            PrismaId = "/NBA/audience/us/abbr/ota",
                        },
                    },
                },
            };
    }
}

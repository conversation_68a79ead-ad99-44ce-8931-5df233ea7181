// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsUpdateCommandHandlerDataNoEncoder.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsUpdate;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    /// IntentMetaDataList Data to test GmsUpdateCommandHandler.
    /// </summary>
    public class ProcessGmsUpdateCommandHandlerDataNoEncoder : IEnumerable<object[]>
    {
        /// <summary>
        /// The data.
        /// </summary>
        private readonly List<object[]> data = new List<object[]>
        {
            new object[]
            {
                new ProcessGmsUpdateCommand()
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Shared.Domain.GMS.Enums.EventType.Game,
                    ContentChangeType = Shared.Domain.GMS.Enums.EventContentChangeType.Schedule,
                },
                GetScheduleChangeRequest(),
            },
        };

        /// <summary>
        /// Returns an enumerator that iterates through the collection.
        /// </summary>
        /// <returns>
        /// An enumerator that can be used to iterate through the collection.
        /// </returns>
        public IEnumerator<object[]> GetEnumerator()
        {
            return this.data.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through a collection.
        /// </summary>
        /// <returns>
        /// An <see cref="IEnumerator" /> object that can be used to iterate through the collection.
        /// </returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.GetEnumerator();
        }

        /// <summary>
        /// Gets the schedule change request.
        /// </summary>
        /// <returns>A Schedule change request.</returns>
        private static ScheduleChangeRequest GetScheduleChangeRequest()
        {
            var scheduleChangeRequest = new ScheduleChangeRequest()
            {
                ExistingScheduleId = "1",
                LongRunningOperationId = "1",
                RequestId = "1",
                RequestorActorId = "1",
                RequestorEventType = "game",
                RequestorId = "1",
                RequestorIdentity = "1",
                RequestorLiveEventId = "1",
                RequestorLiveEventScheduleId = "1",
            };
            scheduleChangeRequest.WorkflowIntents.Add(new WorkflowIntent()
            {
                ChannelId = "1",
                LiveEventTime = DateTime.Now,
                WorkflowId = "1",
                ActorSpecificDetails = new List<ActorSpecificDetail>()
                {
                    new ActorSpecificDetail()
                    {
                        ActorId = "1",
                        Data = null,
                    },
                },
            });
            return scheduleChangeRequest;
        }
    }
}

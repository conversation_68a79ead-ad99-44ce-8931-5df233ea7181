// "//-----------------------------------------------------------------------".
// <copyright file="RemoveEntitlementsFromProductionCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.UseCases.Tvp.Commands.RemoveEntitlementsFromProduction
{
    using Moq;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.RemoveEntitlementsFromProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using Xunit;

    /// <summary>
    /// Tests for RemoveEntitlementsFromProductionCommandValidator.
    /// </summary>
    public class RemoveEntitlementsFromProductionCommandValidatorTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveEntitlementsFromProductionCommandValidatorTests"/> class.
        /// </summary>
        public RemoveEntitlementsFromProductionCommandValidatorTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
        }

        /// <summary>
        /// Creates the remove entitlements from production command validator.
        /// </summary>
        /// <returns>RemoveEntitlementsFromProductionCommandValidator.</returns>
        public RemoveEntitlementsFromProductionCommandValidator CreateRemoveEntitlementsFromProductionCommandValidator()
        {
            return new RemoveEntitlementsFromProductionCommandValidator();
        }

        /// <summary>
        /// Validates the input state under test expected behavior asynchronous.
        /// </summary>
        [Fact]
        public void ValidateInput_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
            RemoveEntitlementsFromProductionCommand syncCommand = new RemoveEntitlementsFromProductionCommand
            {
                LongRunningOperationId = "123",
                ActorSpecificDetail = new MockCleanupActorSpecificDetail
                {
                    ActorId = "1c084dc2-bff5-4e7d-861c-67baa4171f6d",
                    Data = new Shared.Domain.TVP.Models.TvpEventCleanupInfo()
                    {
                        EventExternalId = "989086c988",
                    },
                },
            };

            var removeEntitlementsFromProductionCommandValidator = this.CreateRemoveEntitlementsFromProductionCommandValidator();

            // Act
            var result = removeEntitlementsFromProductionCommandValidator.Validate(syncCommand);

            // Assert
            Assert.Equal(0, result.Errors.Count);
        }
    }
}

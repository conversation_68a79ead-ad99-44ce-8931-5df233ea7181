// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventStatusCommandHandlerTestsData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateEventStatus
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatus;

    /// <summary>
    /// Data for <see cref="UpdateEventStatusCommandHandlerTests"/>.
    /// </summary>
    public class UpdateEventStatusCommandHandlerTestsData : IEnumerable<object[]>
    {
        /// <summary>
        /// The data.
        /// </summary>
        private readonly List<object[]> data = new List<object[]>
        {
            new object[]
            {
                new UpdateEventStatusCommand
                {
                    Data = new TvpUpdateEventStatus
                    {
                        EventStatus = TvpEventStatus.Final,
                        ExternalId = "123",
                    },
                },
                new DateTimeOffset(2021, 06, 25, 1, 0, 0, new TimeSpan(0, 0, 0)),
                new TvpEvent
                {
                    ExternalId = "123",
                    Schedules = new List<TvpEventSchedule>
                    {
                        new TvpEventSchedule
                        {
                            ExternalId = "123",
                        },
                    },
                },
            },
        };

        /// <summary>
        /// Returns an enumerator that iterates through the collection.
        /// </summary>
        /// <returns>
        /// An enumerator that can be used to iterate through the collection.
        /// </returns>
        public IEnumerator<object[]> GetEnumerator()
        {
            return this.data.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through a collection.
        /// </summary>
        /// <returns>
        /// An <see cref="IEnumerator" /> object that can be used to iterate through the collection.
        /// </returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.GetEnumerator();
        }
    }
}

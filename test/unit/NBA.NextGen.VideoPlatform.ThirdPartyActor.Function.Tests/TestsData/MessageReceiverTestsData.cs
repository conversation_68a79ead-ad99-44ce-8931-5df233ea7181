// <copyright file="MessageReceiverTestsData.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Function.Tests.TestsData
{
    using System.Collections.Generic;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;

    /// <summary>
    /// The MessageReceiver tests data.
    /// </summary>
    public static class MessageReceiverTestsData
    {
        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncNoRepeatedDesiredStatesAndNoOrchestrastorRunningStartsNewOrchestationAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.CreatingDefinition,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Configured,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "2",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Completed,
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Completed,
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.CreatingDefinition,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Configured,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Running,
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncWithRepeatedDesiredStatesAndOrchestratorStillRunningLogsErrorAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        DesiredState = InfrastructureState.CreatingDefinition,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.CreatingDefinition,
                    },
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncOrchestrastorDoesNotExistStartsNewOrchestrationAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId1",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Starting,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    {
                                        "Channels", new OttEndpointCreationInfo
                                        {
                                            ChannelId = "172f3615-719d-4a3c-a0e7-5ec33f416837",
                                            EventId = "123",
                                            PoolUuid = "test",
                                            CustomPath = "test",
                                            Enabled = true,
                                            StartTime = null,
                                            InputUuid = "input_test",
                                            TargetUuid = "input_test",
                                            ProcessingUuid = "input_Test",
                                        }
                                    },
                                },
                        },
                    },
                },
            };

        /// <summary>
        /// Gets the thirdParty channel sources. check Sources list object.
        /// </summary>
        private static ICollection<ThirdPartyChannelSource> GetThirdPartyChannelSources => null;
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="GetLeagueSeasonQueryHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.Games.Queries.GetLeagueSeason
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Queries.GetLeagueSeason;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using Xunit;

    /// <summary>
    /// Get League Season Query Handler Tests.
    /// </summary>
    public class GetLeagueSeasonQueryHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock game service.
        /// </summary>
        private readonly Mock<IGameSeasonService> mockGameSeasonService;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetLeagueSeasonQueryHandlerTests"/> class.
        /// </summary>
        public GetLeagueSeasonQueryHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockGameSeasonService = this.mockRepository.Create<IGameSeasonService>();
        }

        /// <summary>
        /// Gets the synchronize games snapshot command handler.
        /// </summary>
        /// <value>
        /// The Get League season query handler.
        /// </value>
        private GetLeagueSeasonQueryHandler GetLeagueSeasonQueryHandler => new GetLeagueSeasonQueryHandler(this.mockGameSeasonService.Object);

        /// <summary>
        /// Handle_ShouldUploadSnapshot Asynchronously.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task Handle_ShouldGetSeasonandLeagueAsync()
        {
            // Arrange
            var leagueSeasons = new List<LeagueSeason>();

            this.mockGameSeasonService.Setup(x => x.GetCurrentSeasonsAsync()).ReturnsAsync(leagueSeasons);

            // Act
            var leagueSeasonsResponse = await this.GetLeagueSeasonQueryHandler.Handle(It.IsAny<GetLeagueSeasonQuery>(), CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockGameSeasonService.Verify(x => x.GetCurrentSeasonsAsync(), Times.Once);
            Assert.Equal(leagueSeasons, leagueSeasonsResponse);
        }
    }
}

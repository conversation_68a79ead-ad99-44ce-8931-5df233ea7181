// "//-----------------------------------------------------------------------".
// <copyright file="UpdateGameNotificationStateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.Games.Commands.UpdateState
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using Xunit;

    /// <summary>
    /// The Update Game Notification State command tests.
    /// </summary>
    public class UpdateGameNotificationStateCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateNotificationStateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsGame>> mockGameRepository;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEvent>> mockEventRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateGameNotificationStateCommandHandlerTests"/> class.
        /// </summary>
        public UpdateGameNotificationStateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpdateNotificationStateCommandHandler>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockGameRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();
            this.mockEventRepository = this.mockRepositoryFactory.ResolveMock<GmsEvent>();
        }

        /// <summary>
        /// Gets the update game notification state command handler.
        /// </summary>
        /// <value>
        /// The update game notification state command handler.
        /// </value>
        private UpdateNotificationStateCommandHandler UpdateNotificationStateCommandHandler =>
            new UpdateNotificationStateCommandHandler(
                this.mockRepositoryFactory,
                this.mockLogger.Object);

        /// <summary>
        /// Handle with command for game updates game.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommandForGame_UpdatesGameAsync()
        {
            // Arrange
            var updateNotificationStateCommand = new UpdateNotificationStateCommand
            {
                State = NotificationState.WaitingForNotification,
                Type = Shared.Domain.GMS.Enums.EventType.Game,
            };

            var game = new GmsGame { NotificationState = NotificationState.Notified };
            this.mockGameRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(game);

            // Act
            await this.UpdateNotificationStateCommandHandler.Handle(updateNotificationStateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockGameRepository.Verify(x => x.GetItemAsync(It.Is<string>(x => x == updateNotificationStateCommand.Id)), Times.Once);
            this.mockGameRepository.Verify(x => x.UpdateItemAsync(It.Is<GmsGame>(x => x.NotificationState == updateNotificationStateCommand.State)), Times.Once);
        }

        /// <summary>
        /// Handle with command for event updates event.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommandForEvent_UpdatesEventAsync()
        {
            // Arrange
            var updateNotificationStateCommand = new UpdateNotificationStateCommand
            {
                State = NotificationState.WaitingForNotification,
                Type = Shared.Domain.GMS.Enums.EventType.Event,
            };

            var game = new GmsEvent { NotificationState = NotificationState.Notified };
            this.mockEventRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(game);

            // Act
            await this.UpdateNotificationStateCommandHandler.Handle(updateNotificationStateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockEventRepository.Verify(x => x.GetItemAsync(It.Is<string>(x => x == updateNotificationStateCommand.Id)), Times.Once);
            this.mockEventRepository.Verify(x => x.UpdateItemAsync(It.Is<GmsEvent>(x => x.NotificationState == updateNotificationStateCommand.State)), Times.Once);
        }

        /// <summary>
        /// Handle with null command throws exception asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNullCommandThrowsExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.UpdateNotificationStateCommandHandler.Handle(null, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}

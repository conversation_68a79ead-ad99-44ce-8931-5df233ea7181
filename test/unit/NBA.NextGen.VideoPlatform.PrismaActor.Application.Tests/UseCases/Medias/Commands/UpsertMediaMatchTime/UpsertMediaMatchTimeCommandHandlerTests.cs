// "//-----------------------------------------------------------------------".
// <copyright file="UpsertMediaMatchTimeCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Tests.UseCases.Channels.Commands
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using NBA.NextGen.VideoPlatform.PrismaActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using Xunit;

    /// <summary>
    /// The UpsertEsniResourceCommandHandler Tests.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UpsertMediaMatchTimeCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpsertMediaMatchTimeCommandHandler>> mockLogger;

        /// <summary>
        /// The mock prisma client service.
        /// </summary>
        private readonly Mock<IPrismaClientService> mockPrismaWorkerClientService;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The mock prisma worker options.
        /// </summary>
        private readonly Mock<IOptions<MediaMatchPointTimeOptions>> mockMediaMatchPointTimeOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertMediaMatchTimeCommandHandlerTests"/> class.
        /// </summary>
        public UpsertMediaMatchTimeCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpsertMediaMatchTimeCommandHandler>();
            this.mockPrismaWorkerClientService = this.mockRepository.Create<IPrismaClientService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfiles(new Profile[] { new PrismaActorProfile(), new EntityProfile(this.mockDateTime.Object) })).CreateMapper();
            this.mockMediaMatchPointTimeOptions = this.mockRepository.Create<IOptions<MediaMatchPointTimeOptions>>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpsertMediaMatchTimeCommandHandler UpsertMediaMatchTimeCommandHandler => new UpsertMediaMatchTimeCommandHandler(
            this.mockLogger.Object,
            this.mockPrismaWorkerClientService.Object,
            this.mockTelemetryService.Object,
            this.mapper,
            this.mockDateTime.Object,
            this.mockMediaMatchPointTimeOptions.Object);

        /// <summary>
        /// Handle the with command calls upsert methods.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_CallsUpsertMethodsAsync()
        {
            var prismaMedia = new PrismaMedia()
            {
                Id = "/NBA/media/g9942174785tor751bos",
                MediaPoint = new List<PrismaMediaPoint>()
                {
                    new PrismaMediaPoint()
                    {
                        Id = "/NBA/mediapoint/g9942174785tor751bos/start",
                        MatchTime = DateTimeOffset.UtcNow,
                        Effective = DateTimeOffset.UtcNow.AddMinutes(-60),
                        Expires = DateTimeOffset.UtcNow.AddMinutes(60),
                    },
                },
            };

            var media = new Media()
            {
                Id = "/NBA/media/g9942174785tor751bos",
                MediaPoint = new Collection<MediaPoint>
                {
                   new MediaPoint()
                   {
                       Id = "/NBA/mediapoint/g9942174785tor751bos/start",
                       MatchTime = DateTimeOffset.UtcNow,
                       Effective = DateTimeOffset.UtcNow.AddMinutes(-60),
                       Expires = DateTimeOffset.UtcNow.AddMinutes(60),
                   },
                },
            };

            var request = new UpsertMediaMatchTimeCommand
            {
                LongRunningOperationId = "2",
                ProductionId = "g9942174785tor751bos",
                EventId = "9942174785",
            };

            this.mockMediaMatchPointTimeOptions.Setup(x => x.Value).Returns(new MediaMatchPointTimeOptions());
            this.mockPrismaWorkerClientService.Setup(x => x.TryGetEsniResourceAsync<Media>(It.IsAny<string>())).ReturnsAsync(media);

            this.mockDateTime.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            // Act
            await this.UpsertMediaMatchTimeCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpsertEsniMediaAsync(
                    It.Is<PrismaMedia>(
                        x => x.Id == prismaMedia.Id
                        && x.MediaPoint.First().Id == prismaMedia.MediaPoint.First().Id
                        && x.MediaPoint.First().AltID.Contains(PrismaActorGeneralConstants.ChannelHasStartedAltID))),
                Times.Once);
            this.mockPrismaWorkerClientService.Verify(x => x.TryGetEsniResourceAsync<Media>(It.Is<string>(x => x == "/NBA/media/" + request.ProductionId)), Times.Once);
        }

        /// <summary>
        /// Handle the with command not calls upsert methods.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_NotCallUpsertMethodsAsync()
        {
            var prismaMedia = new PrismaMedia()
            {
                Id = "/NBA/media/g9942174785tor751bos",
                MediaPoint = new List<PrismaMediaPoint>()
                {
                    new PrismaMediaPoint()
                    {
                        Id = "/NBA/mediapoint/g9942174785tor751bos/start",
                        RelatedProductionId = "g9942174785tor751bos",
                        MatchTime = DateTimeOffset.UtcNow,
                    },
                },
            };

            this.mockDateTime.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            var request = new UpsertMediaMatchTimeCommand
            {
                LongRunningOperationId = "2",
                ProductionId = "g9942174785tor751bos",
                EventId = "9942174785",
            };
            var idsChecked = new List<string>();
            this.mockMediaMatchPointTimeOptions.Setup(x => x.Value).Returns(new MediaMatchPointTimeOptions());
            this.mockPrismaWorkerClientService.Setup(x => x.TryGetEsniResourceAsync<Media>(It.IsAny<string>())).Callback<string>(id =>
            {
                idsChecked.Add(id);
            });

            // Act
            await this.UpsertMediaMatchTimeCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpsertEsniMediaAsync(
                    It.Is<PrismaMedia>(
                        x => x.Id == prismaMedia.Id
                        && x.MediaPoint.First().AltID.Contains(PrismaActorGeneralConstants.ChannelHasStartedAltID))),
                Times.Never);
            this.mockPrismaWorkerClientService.Verify(x => x.TryGetEsniResourceAsync<Media>(It.Is<string>(x => x == "/NBA/media/" + request.ProductionId)), Times.Once);
        }
    }
}

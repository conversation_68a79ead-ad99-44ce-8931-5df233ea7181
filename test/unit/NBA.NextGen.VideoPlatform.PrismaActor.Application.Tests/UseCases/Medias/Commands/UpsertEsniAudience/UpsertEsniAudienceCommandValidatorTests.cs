// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEsniAudienceCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations.Tests.UseCases.Medias.Commands.UpsertEsniAudience
{
    using System;
    using System.Collections.Generic;
    using System.Linq.Expressions;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using Xunit;

    /// <summary>
    /// The UpsertEsniResourceCommandValidator Tests.
    /// </summary>
    public class UpsertEsniAudienceCommandValidatorTests
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly UpsertEsniAudienceCommandValidator validator;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The esni audience repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<EsniAudience>> esniAudienceRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertEsniAudienceCommandValidatorTests"/> class.
        /// </summary>
        public UpsertEsniAudienceCommandValidatorTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.esniAudienceRepository = this.mockRepositoryFactory.ResolveMock<EsniAudience>();
            this.validator = new UpsertEsniAudienceCommandValidator(this.mockRepositoryFactory);
        }

        /// <summary>
        /// ValidateInput with invalid command fails validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithInvalidCommand_FailsValidation()
        {
            // Arrange
            var request = new UpsertEsniAudienceCommand
            {
                LongRunningOperationId = string.Empty,
                AudienceIdsToUpsert = new List<string>() { string.Empty },
            };

            this.esniAudienceRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<EsniAudience, bool>>>())).ReturnsAsync([]);

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(3, result.Errors.Count);
            Assert.Equal("LongRunningOperationId cannot be null", result.Errors[0].ErrorMessage);
            Assert.Equal("Ids cannot be null", result.Errors[1].ErrorMessage);
            Assert.Equal("VideoPlatformEntity with given data is not valid or is already deleted.", result.Errors[2].ErrorMessage);
        }

        /// <summary>
        /// ValidateInput with valid command passes validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithValidCommand_PassesValidation()
        {
            // Arrange
            var request = new UpsertEsniAudienceCommand
            {
                LongRunningOperationId = "LongRunningOperationId",
                AudienceIdsToUpsert = new List<string>() { "EsniResourceId" },
            };

            this.esniAudienceRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<EsniAudience, bool>>>())).ReturnsAsync([new EsniAudience()]);

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
// "//-----------------------------------------------------------------------".
// <copyright file="LiveEventOrchestrationTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Function.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models;
    using NBA.NextGen.VideoPlatform.Orchestrator.Function.Orchestrators;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Xunit;

    /// <summary>
    /// The LiveEventOrchestrationTests tests.
    /// </summary>
    public class LiveEventOrchestrationTests
    {
        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<LiveEventOrchestration>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mock Video Platform Provider factory.
        /// </summary>
        private readonly Mock<IVideoPlatformCorrelationProviderFactory> mockVideoPlatformCorrelationProviderFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="LiveEventOrchestrationTests"/> class.
        /// </summary>
        public LiveEventOrchestrationTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockLogger = this.mockRepository.Create<ILogger<LiveEventOrchestration>>();
            this.mockVideoPlatformCorrelationProviderFactory = this.mockRepository.Create<IVideoPlatformCorrelationProviderFactory>();
            var mockIVideoPlatformCorrelationProvider = this.mockRepository.Create<IVideoPlatformCorrelationProvider>();
            this.mockVideoPlatformCorrelationProviderFactory.Setup(x => x.GetProvider()).Returns(mockIVideoPlatformCorrelationProvider.Object);
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new OrchestratorProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the live event orchestration.
        /// </summary>
        /// <value>
        /// The live event orchestration.
        /// </value>
        private LiveEventOrchestration LiveEventOrchestration => new LiveEventOrchestration(
            this.mockLogger.Object,
            this.mapper,
            this.mockMediator.Object,
            this.mockVideoPlatformCorrelationProviderFactory.Object);

        /// <summary>
        /// RunAsync with multiple actors OrchestratorRequest calls all activities.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task RunAsync_WithMultipleActorsOrchestratorRequest_CallsAllActivitiesAsync()
        {
            // Arrange
            var context = this.mockRepository.Create<IDurableOrchestrationContext>();
            var orchestratorRequest = TestData.GetMockOrchestratorRequestWithMultipleActors("123456789", NbaWorkflowIds.EventInfrastructureSetup, "123");
            var workflowStatesOccurrences = new Dictionary<WorkflowState, int>();
            context.Setup(x => x.GetInput<OrchestratorRequest>()).Returns(orchestratorRequest);
            context.Setup(x => x.WaitForExternalEvent<InfrastructureStateChangedEvent>(It.IsAny<string>())).ReturnsAsync(new InfrastructureStateChangedEvent { State = InfrastructureState.Provisioned });
            context.Setup(x => x.CallActivityAsync(It.Is<string>(s => s.Equals(ActivityFunctionNames.PublishWorkflowStateUpdatedEvent, StringComparison.OrdinalIgnoreCase)), It.IsAny<OrchestratorRequest>())).Callback<string, object>((name, input) =>
            {
                var workflowState = ((OrchestratorRequest)input).WorkflowState;

                if (workflowStatesOccurrences.ContainsKey(workflowState))
                {
                    workflowStatesOccurrences[workflowState]++;
                }
                else
                {
                    workflowStatesOccurrences[workflowState] = 1;
                }
            });

            // Act
            await this.LiveEventOrchestration.RunAsync(context.Object).ConfigureAwait(false);

            // Assert
            Assert.Equal(1, workflowStatesOccurrences[WorkflowState.InProgress]);
            Assert.Equal(1, workflowStatesOccurrences[WorkflowState.Completed]);
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishRequestAcknowledgementEvent), It.Is<OrchestratorRequest>(x => x == orchestratorRequest)), Times.Once);
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishWorkflowStateUpdatedEvent), It.IsAny<OrchestratorRequest>()), Times.Exactly(2));
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorId == x.ActorSpecificDetails.ActorId
                    && x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[0].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[0].Data
                    && x.CorrelationId == orchestratorRequest.CorrelationId
                    && x.DesiredState == InfrastructureState.Provisioned
                    && x.ExternalSystemInfrastructureId == orchestratorRequest.ExternalSystemInfrastructureId
                    && x.InfrastructureId == orchestratorRequest.InfrastructureId
                    && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                    && x.RequestId == orchestratorRequest.RequestId
                    && x.WorkflowId == orchestratorRequest.WorkflowId)), Times.Once);
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorId == x.ActorSpecificDetails.ActorId
                    && x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[1].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[1].Data
                    && x.CorrelationId == orchestratorRequest.CorrelationId
                    && x.DesiredState == InfrastructureState.Provisioned
                    && x.ExternalSystemInfrastructureId == orchestratorRequest.ExternalSystemInfrastructureId
                    && x.InfrastructureId == orchestratorRequest.InfrastructureId
                    && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                    && x.RequestId == orchestratorRequest.RequestId
                    && x.WorkflowId == orchestratorRequest.WorkflowId)), Times.Once);
        }

        /// <summary>
        /// RunAsync with multiple actors OrchestratorRequest and first actor failes and ContinueOnError is false, does not call second actor.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task RunAsync_WithMultipleActorsOrchestratorRequestAndFirstActorFailsAndContiueOnErrorIsFalse_DoesNotCallSecondActorAsync()
        {
            IDictionary<string, object> data = new Dictionary<string, object>();
            data.Add("Error", "Actor returned mock error");
            InfrastructureStateChangedEvent infrastructureStateChangedEvent = new InfrastructureStateChangedEvent()
            {
                State = InfrastructureState.Failed,
                Data = data,
            };

            // Arrange
            var context = this.mockRepository.Create<IDurableOrchestrationContext>();
            var orchestratorRequest = TestData.GetMockOrchestratorRequestWithMultipleActors("123456789", NbaWorkflowIds.EventInfrastructureSetup, "123");
            context.Setup(x => x.GetInput<OrchestratorRequest>()).Returns(orchestratorRequest);
            context.Setup(x => x.WaitForExternalEvent<InfrastructureStateChangedEvent>(It.IsAny<string>())).ReturnsAsync(infrastructureStateChangedEvent);
            context.Setup(x => x.IsReplaying).Returns(false);
            context.Setup(x => x.CallActivityAsync(
                It.Is<string>(x => x == ActivityFunctionNames.PublishWorkflowStateUpdatedEvent),
                It.IsAny<OrchestratorRequest>())).Callback<string, object>((name, input) => Assert.True(
                    ((OrchestratorRequest)input).WorkflowState == WorkflowState.InProgress || ((OrchestratorRequest)input).WorkflowState == WorkflowState.Failed));

            // Act
            await this.LiveEventOrchestration.RunAsync(context.Object).ConfigureAwait(false);

            // Assert
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishRequestAcknowledgementEvent), It.Is<OrchestratorRequest>(x => x == orchestratorRequest)), Times.Once);
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishWorkflowStateUpdatedEvent), It.IsAny<OrchestratorRequest>()), Times.Exactly(2));
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorId == x.ActorSpecificDetails.ActorId
                    && x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[0].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[0].Data
                    && x.CorrelationId == orchestratorRequest.CorrelationId
                    && x.DesiredState == InfrastructureState.Provisioned
                    && x.ExternalSystemInfrastructureId == orchestratorRequest.ExternalSystemInfrastructureId
                    && x.InfrastructureId == orchestratorRequest.InfrastructureId
                    && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                    && x.RequestId == orchestratorRequest.RequestId
                    && x.WorkflowId == orchestratorRequest.WorkflowId)), Times.Once);
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[1].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[1].Data)), Times.Never);
        }

        /// <summary>
        /// RunAsync with multiple actors OrchestratorRequest and first actor failes and ContinueOnError is false, continue when ThirdPartyActor is next.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task RunAsync_WithMultipleActorsOrchestratorRequestAndFirstActorFailsAndContiueOnErrorIsFalse_TriggerThirdPartyActorAsync()
        {
            IDictionary<string, object> data = new Dictionary<string, object>();
            data.Add("Error", "Actor returned mock error");
            InfrastructureStateChangedEvent infrastructureStateChangedEvent = new InfrastructureStateChangedEvent()
            {
                State = InfrastructureState.Failed,
                Data = data,
            };

            // Arrange
            var context = this.mockRepository.Create<IDurableOrchestrationContext>();
            var orchestratorRequest = TestData.GetMockOrchestratorRequestWithAquilaAndThridPartyActors("123456789", NbaWorkflowIds.EventInfrastructureSetup, "123");
            context.Setup(x => x.GetInput<OrchestratorRequest>()).Returns(orchestratorRequest);
            context.Setup(x => x.WaitForExternalEvent<InfrastructureStateChangedEvent>(It.IsAny<string>())).ReturnsAsync(infrastructureStateChangedEvent);
            context.Setup(x => x.IsReplaying).Returns(false);
            context.Setup(x => x.CallActivityAsync(
                It.Is<string>(x => x == ActivityFunctionNames.PublishWorkflowStateUpdatedEvent),
                It.IsAny<OrchestratorRequest>())).Callback<string, object>((name, input) => Assert.True(
                    ((OrchestratorRequest)input).WorkflowState == WorkflowState.InProgress || ((OrchestratorRequest)input).WorkflowState == WorkflowState.Failed));

            // Act
            await this.LiveEventOrchestration.RunAsync(context.Object).ConfigureAwait(false);

            // Assert
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishRequestAcknowledgementEvent), It.Is<OrchestratorRequest>(x => x == orchestratorRequest)), Times.Once);
            context.Verify(x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.PublishWorkflowStateUpdatedEvent), It.IsAny<OrchestratorRequest>()), Times.Exactly(2));
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorId == x.ActorSpecificDetails.ActorId
                    && x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[0].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[0].Data
                    && x.CorrelationId == orchestratorRequest.CorrelationId
                    && x.DesiredState == InfrastructureState.Provisioned
                    && x.ExternalSystemInfrastructureId == orchestratorRequest.ExternalSystemInfrastructureId
                    && x.InfrastructureId == orchestratorRequest.InfrastructureId
                    && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                    && x.RequestId == orchestratorRequest.RequestId
                    && x.WorkflowId == orchestratorRequest.WorkflowId)), Times.Once);
            context.Verify(
                x => x.CallActivityAsync(It.Is<string>(x => x == ActivityFunctionNames.SendInfrastructureStateChangeRequest), It.Is<SendInfrastructureStateChangeRequestCommand>(
                    x => x.ActorSpecificDetails.ActorId == orchestratorRequest.ActorSpecificDetails[1].ActorId
                    && x.ActorSpecificDetails.Data == orchestratorRequest.ActorSpecificDetails[1].Data)), Times.Once);
        }

        /// <summary>
        /// PublishRequestAcknowledgementEventAsync with OrchestratorRequest calls Mediator.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task PublishRequestAcknowledgementEventAsync_WithOrchestratorRequest_CallMediatorAsync()
        {
            // Arrange
            var orchestratorRequest = TestData.GetMockOrchestratorRequestWithMultipleActors("123456789", NbaWorkflowIds.EventInfrastructureSetup, "123");

            // Act
            await this.LiveEventOrchestration.PublishRequestAcknowledgementEventAsync(orchestratorRequest).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<PublishRequestAcknowledgementEventCommand>(
                        x => x.CorrelationId == orchestratorRequest.CorrelationId
                        && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                        && x.ReceivedRequestActorId == orchestratorRequest.RequestorActorId
                        && x.ReceivedRequestId == orchestratorRequest.RequestId), It.IsAny<CancellationToken>()), Times.Once);
        }

        /// <summary>
        /// PublishWorkflowStateUpdatedEventAsync with OrchestratorRequest calls Mediator.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task PublishWorkflowStateUpdatedEventAsync_WithOrchestratorRequest_CallMediatorAsync()
        {
            // Arrange
            var orchestratorRequest = TestData.GetMockOrchestratorRequestWithMultipleActors("123456789", NbaWorkflowIds.EventInfrastructureSetup, "123");

            // Act
            await this.LiveEventOrchestration.PublishWorkflowStateUpdatedEventAsync(orchestratorRequest).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                It.Is<PublishWorkflowStateUpdatedEventCommand>(
                    x => x.InfrastructureState == orchestratorRequest.Result
                    && x.CorrelationId == orchestratorRequest.CorrelationId
                    && x.InfrastructureId == orchestratorRequest.InfrastructureId
                    && x.InfrastructureState == default
                    && x.LongRunningOperationId == orchestratorRequest.LongRunningOperationId
                    && x.RequestId == orchestratorRequest.RequestId
                    && x.RequestorLiveEventId == orchestratorRequest.RequestorLiveEventId
                    && x.ScheduleId == orchestratorRequest.ScheduleId
                    && x.WorkflowId == orchestratorRequest.WorkflowId
                    && x.WorkflowState == orchestratorRequest.WorkflowState), It.IsAny<CancellationToken>()), Times.Once);
        }

        /// <summary>
        /// SendChannelInfrastructureStateChangeRequestAsync with OrchestratorRequest calls Mediator.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task SendChannelInfrastructureStateChangeRequestAsync__WithOrchestratorRequest_CallMediatorAsync()
        {
            // Arrange
            var request = new SendInfrastructureStateChangeRequestCommand
            {
                RequestId = "123",
                DesiredState = InfrastructureState.Provisioned,
            };

            // Act
            await this.LiveEventOrchestration.SendChannelInfrastructureStateChangeRequestAsync(request).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.Is<SendInfrastructureStateChangeRequestCommand>(x => x == request), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="TimerTriggerInterpreterTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Function.Tests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;

    using MediatR;

    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Timers;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;

    using Moq;

    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.VerifyEncoder;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Function;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configurations;

    using Xunit;

    /// <summary>
    /// The TimerTriggerInterpreter Tests.
    /// </summary>
    public class TimerTriggerInterpreterTests : BaseUnitTest
    {
        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<TimeTriggerInterpreter>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The options.
        /// </summary>
        private readonly Mock<IOptions<GmsScheduleValidatorOptions>> mockOptions;

        /// <summary>
        /// The mock timer schedule.
        /// </summary>
        private readonly Mock<TimerSchedule> mockTimerSchedule;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="TimerTriggerInterpreterTests"/> class.
        /// </summary>
        public TimerTriggerInterpreterTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.mockRepository.Create<ILogger<TimeTriggerInterpreter>>();
            this.mockTimerSchedule = this.mockRepository.Create<TimerSchedule>();
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockOptions = this.mockRepository.Create<IOptions<GmsScheduleValidatorOptions>>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfiles(new Profile[] { new GmsInterpreterProfile() })).CreateMapper();
        }

        /// <summary>
        /// Gets the TimeTriggerInterpreter function.
        /// </summary>
        /// <value>
        /// The TimeTriggerInterpreter function.
        /// </value>
        private TimeTriggerInterpreter TimeTriggerInterpreter => new TimeTriggerInterpreter(this.mockMediator.Object, this.mockLogger.Object, this.mapper, this.mockOptions.Object);

        /// <summary>
        /// VerifyEncoderShouldRunAsync will build the evaluation window and calls mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task VerifyEncoderShouldRunAsync()
        {
            // Arrange
            var scheduleStatus = new ScheduleStatus
            {
                Next = DateTime.UtcNow,
            };

            var timer = new TimerInfo(this.mockTimerSchedule.Object, scheduleStatus, true);
            var options = new GmsScheduleValidatorOptions() { VerificationPeriodInDays = 2 };
            this.mockOptions.SetReturnsDefault(options);

            // Act
            await this.TimeTriggerInterpreter.VerifyEncoderAsync(timer).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<VerifyEncoderCommand>(
                        x => x.CurrentEvaluation == timer.ScheduleStatus.Next &&
                        x.VerificationPeriodInDays == options.VerificationPeriodInDays), CancellationToken.None), Times.Once);
            this.VerifyLogger(this.mockLogger, LogLevel.Information, Times.Exactly(2));
        }

        /// <summary>
        /// VerifyEncoder Throws ArgumentNull Exception When null timer object is sent.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task VerifyEncoderThrowsArgumentNullExceptionWhenTimerObjectIsNullAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.TimeTriggerInterpreter.VerifyEncoderAsync(null).ConfigureAwait(false)).ConfigureAwait(false);

            this.mockMediator.Verify(
                x => x.Send(
                    It.IsAny<VerifyEncoderCommand>(), CancellationToken.None), Times.Never);
            this.VerifyLogger(this.mockLogger, LogLevel.Information, Times.Never());
        }

        /// <summary>
        /// VerifyEncoder Throws null reference Exception When Gms Scheduler Validatior value is Null.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task VerifyEncoderThrowsNullReferenceExceptionWhenGmsSchedulerValidatiorOptionsValueIsNullAsync()
        {
            // Arrange
            var scheduleStatus = new ScheduleStatus
            {
                Next = DateTime.UtcNow,
            };

            var timer = new TimerInfo(this.mockTimerSchedule.Object, scheduleStatus, true);

            // Assert
            await Assert.ThrowsAsync<NullReferenceException>(async () => await this.TimeTriggerInterpreter.VerifyEncoderAsync(timer).ConfigureAwait(false)).ConfigureAwait(false);

            this.mockMediator.Verify(
                x => x.Send(
                    It.IsAny<VerifyEncoderCommand>(), CancellationToken.None), Times.Never);
            this.VerifyLogger(this.mockLogger, LogLevel.Information, Times.Never());
        }
    }
}

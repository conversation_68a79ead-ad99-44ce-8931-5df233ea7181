// "//-----------------------------------------------------------------------".
// <copyright file="GameEndStreamMarkerStrategyTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Application.Tests.Factories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.PlayOptions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.ModelModels;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Strategies;
    using Xunit;

    /// <summary>
    /// The <see cref="GameEndStreamMarkerStrategyTests"/>.
    /// </summary>
    public class GameEndStreamMarkerStrategyTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// Mock datetime service.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// Mock logger.
        /// </summary>
        private readonly Mock<ILogger<GameEndStreamMarkerStrategy>> mockGameEndStrategyLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// Mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<WorkflowRequest>> mockQueueClient;

        /// <summary>
        /// Mock repository factory.
        /// </summary>
        private readonly MockObjectRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// Mock VideoPlatformChannel repository.
        /// </summary>
        private readonly Mock<IObjectRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        /// <summary>
        /// Mock GmsGame repository.
        /// </summary>
        private readonly Mock<IObjectRepository<GmsGame>> mockGmsGameRepository;

        /// <summary>
        /// Mock service bus options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="GameEndStreamMarkerStrategyTests" /> class.
        /// </summary>
        public GameEndStreamMarkerStrategyTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            var serviceBusOptions = new ServiceBusOptions
            {
                StreamMarkersNotifierQueue = nameof(ServiceBusOptions.StreamMarkersNotifierQueue),
                WorkflowRequest = nameof(ServiceBusOptions.WorkflowRequest),
            };

            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
            

            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<WorkflowRequest>();

            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mockGameEndStrategyLogger = this.mockRepository.Create<ILogger<GameEndStreamMarkerStrategy>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();

            this.mockRepositoryFactory = new MockObjectRepositoryFactory();
            this.mockVideoPlatformChannelRepository = mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();
            this.mockGmsGameRepository = mockRepositoryFactory.ResolveMock<GmsGame>();
        }

        /// <summary>
        /// Gets the game end stream marker strategy.
        /// </summary>
        private GameEndStreamMarkerStrategy GameEndStreamMarkerStrategy =>
            new GameEndStreamMarkerStrategy(
                this.mockDateTime.Object,
                this.mockGameEndStrategyLogger.Object,
                this.mockTelemetryService.Object,
                this.mockQueueClientProvider,
                this.mockRepositoryFactory,
                this.mockServiceBusOptions.Object);

#pragma warning disable CA1506
        /// <summary>
        /// Tests that the game end strategy works properly for a valid channel and a valid marker.
        /// </summary>
        /// <param name="isPrimaryFeed">Whether the feed is primary or not.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task ProcessStreamMarkerRequest_ForValidMarkerAndChannel_ShouldWorkAsync(bool isPrimaryFeed)
        {
            // Arrange
            var liveEventId = "**********";
            var channelId = "g**********tor7517511bos";
            var instanceId = "123";
            var correlationId = "correlationIdTest";
            var esniMediaId = $"/NBA/media/{channelId}";
            var startMediaPointId = $"/NBA/mediapoint/{channelId}/start";
            var endMediaPointId = $"/NBA/mediapoint/{channelId}/end";
            var localPolicyId = $"/NBA/policy/local/{channelId}";
            var regionalPolicyId = $"/NBA/policy/regional/{channelId}";
            var gameEndMediaPointId = $"/NBA/mediapoint/{channelId}/gameend";

            var streamMarkerEvent = new StreamMarkerEvent
            {
                AcquisitionTime = new DateTimeOffset(2022, 06, 13, 20, 5, 0, TimeSpan.Zero),
            };

            var videoPlatformChannel = new VideoPlatformChannel
            {
                Id = channelId,
                PrimaryFeed = isPrimaryFeed,
            };

            var gmsGame = new GmsGame
            {
                Id = liveEventId,
                DateTime = new DateTimeOffset(2022, 06, 13, 21, 0, 0, TimeSpan.Zero),
                Media = new List<MediaInfo>()
                {
                    new MediaInfo()
                    {
                        Active = true,
                        Id = 7517511,
                        Schedules = new List<Schedule>()
                        {
                            new Schedule()
                            {
                                Id = 1,
                                Active = true,
                                Channel = channelId,
                                Operations = new Operation()
                                {
                                    KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>()
                                    {
                                        new Shared.Domain.GMS.Entities.KeyValuePair()
                                        {
                                            Key = "NSS-Associated-Experiences",
                                            Value = "pregame",
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };

            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemAsync(It.Is<string>(x => x == channelId))).ReturnsAsync(videoPlatformChannel);
            this.mockGmsGameRepository.Setup(x => x.GetItemAsync(It.Is<string>(x => x == liveEventId))).ReturnsAsync(gmsGame);
            var now = new DateTimeOffset(2022, 06, 13, 20, 10, 0, TimeSpan.Zero);
            this.mockDateTime.Setup(x => x.Now).Returns(now);

            // Act
            await this.GameEndStreamMarkerStrategy.ProcessStreamMarkerRequestAsync(channelId, instanceId, correlationId, streamMarkerEvent).ConfigureAwait(false);

            // Assert
            Assert.Equal(StreamMarkerSegmentationType.GameEnd, this.GameEndStreamMarkerStrategy.StreamMarkerType);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                It.Is<WorkflowRequest>(wr =>
                    wr.RequestorActorId == ActorIds.StreamMarkerListener
                    && wr.RequestorLiveEventId == liveEventId
                    && wr.WorkflowIntent.ActorSpecificDetails.Count == 3
                    && wr.WorkflowIntent.ActorSpecificDetails[0].ActorId == ActorIds.PrismaMedias
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).EventId == liveEventId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).EventStartTime == gmsGame.DateTime
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().EsniMediaId == esniMediaId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().StartMediaPointId == startMediaPointId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().EndMediaPointId == endMediaPointId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().LocalPolicyId == localPolicyId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().RegionalPolicyId == regionalPolicyId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().GameEndMediaPointId == gameEndMediaPointId
                    && Assert.IsType<PrismaProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[0].Data).Details.First().HasInBandScte35
                    && wr.WorkflowIntent.ActorSpecificDetails[1].ActorId == ActorIds.TvpActor
                    && Assert.IsType<TvpProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[1].Data).LiveEventId == liveEventId
                    && Assert.IsType<TvpProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[1].Data).ProductionId == channelId
                    && Assert.IsType<TvpProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[1].Data).IsPrimaryFeed == isPrimaryFeed
                    && Assert.IsType<TvpProcessGameEndMarkerModel>(wr.WorkflowIntent.ActorSpecificDetails[1].Data).MarkerAcquisitionTime == streamMarkerEvent.AcquisitionTime
                    && wr.WorkflowIntent.ActorSpecificDetails[2].ActorId == ActorIds.DmmActor
                    && Assert.IsType<LiveProductionServicesDetails>(wr.WorkflowIntent.ActorSpecificDetails[2].Data).MediaId == channelId)), Times.Once);
        }
#pragma warning restore CA1506

        /// <summary>
        /// Tests that the strategy doesn't trigger any workflow if the VideoPlatformChannel does not exist.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task ProcessStreamMarkerRequest_ForNonExistingVideoPlatformChannel_ShouldDoAnythingAsync()
        {
            // Arrange
            var channelId = "g**********tor751bos";
            var instanceId = "123";
            var correlationId = "correlationIdTest";
            var streamMarkerEvent = new StreamMarkerEvent();

            // Act
            await this.GameEndStreamMarkerStrategy.ProcessStreamMarkerRequestAsync(channelId, instanceId, correlationId, streamMarkerEvent).ConfigureAwait(false);

            // Assert
            Assert.Equal(StreamMarkerSegmentationType.GameEnd, this.GameEndStreamMarkerStrategy.StreamMarkerType);
            this.mockQueueClient.Setup(x => x.SendAsync(It.IsAny<WorkflowRequest>()))
                .Returns(Task.FromResult(true));
        }
    }
}

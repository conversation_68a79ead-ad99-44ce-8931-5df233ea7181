// "//-----------------------------------------------------------------------".
// <copyright file="NextGenExtensionsTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tests.Gms.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Xml;
    using FluentAssertions;
    using Moq;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.Vendor.Api.PlayOptions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Newtonsoft.Json;
    using Xunit;
    using KeyValuePair = Domain.GMS.Entities.KeyValuePair;

#pragma warning disable CA1506 // Avoid excessive class coupling
    /// <summary>
    /// The NextGenExtensionsTests.
    /// </summary>
    public class NextGenExtensionsTests
    {
        /// <summary>
        /// The NSS PrimaryFeedKey.
        /// </summary>
        private const string NssPrimaryFeedKey = "NSS-Primary";

        /// <summary>
        /// The NSS Geo-Allow key.
        /// </summary>
        private const string NssGeoAllowKey = "NSS-Geo-Allow";

        /// <summary>
        /// The NSS Geo-Block key.
        /// </summary>
        private const string NssGeoBlockKey = "NSS-Geo-Block";

        /// <summary>
        /// The NSS Georedundant regions key.
        /// </summary>
        private const string NssGeoredundantRegionsKey = "NSS-Georedundant-Regions";

        /// <summary>
        /// The CustomOffsetKey for start.
        /// </summary>
        private const string CustomOffsetKeyStart = "NSS-Offset-ChannelStart";

        /// <summary>
        /// The CustomOffsetKey for over.
        /// </summary>
        private const string CustomOffsetKeyOver = "NSS-Offset-ChannelOver";

        /// <summary>
        /// The CustomOffsetKey for broadcast.
        /// </summary>
        private const string CustomOffsetKeyBroadcast = "NSS-Offset-ChannelBroadcast";

        /// <summary>
        /// The Nss blackout team RSN key.
        /// </summary>
        private const string NssBlackoutTeamRsnKey = "NSS-Blackout-Team-RSN";

        /// <summary>
        /// The Nss blackout team OTA key.
        /// </summary>
        private const string NssBlackoutTeamOtaKey = "NSS-Blackout-Team-OTA";

        /// <summary>
        /// The Nss associated experiences key.
        /// </summary>
        private const string NssAssociatedExperiencesKey = "NSS-Associated-Experiences";

        /// <summary>
        /// The game-level PrismaMedia created count.
        /// </summary>
        private const int GameLevelPrismaMediaCount = 1;

        /// <summary>
        /// Gets the <see cref="IEnumerable{IntentMetaData}"/> when game is not relevant should work.
        /// </summary>
        [Fact]
        public void GetIntentMetaDatas_WhenGameIsNotRelevant_ShouldWork()
        {
            // Arrange
            var game = this.GetMockGmsGameData();
            game.Active = false;

            // Act
            var result = NextGenExtensions.GetIntentMetaDatas(game, new CustomWorkflowOffsetOptions(), new EsniResourcesCreationOptions(), new AquilaChannelCreationOptions(), new ThirdPartyEndpointCreationOptions());

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        /// <summary>
        /// Gets the <see cref="IEnumerable{IntentMetaData}"/> should work.
        /// </summary>
        [Fact]
        public void GetIntentMetaDatas_ShouldWork()
        {
            // Arrange
            var game = this.GetMockGmsGameData();
            var customOffsetOptions = new CustomWorkflowOffsetOptions
            {
                CustomWorkflowOffsets = new List<CustomWorkflowOffset>
                {
                    new CustomWorkflowOffset
                    {
                        KvpKey = CustomOffsetKeyStart,
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = CustomOffsetKeyBroadcast,
                        NbaWorkflowId = NbaWorkflowIds.EventReachedTipoffTime,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = CustomOffsetKeyOver,
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureEnd,
                    },
                },
            };

            // Act
            var result = NextGenExtensions.GetIntentMetaDatas(game, customOffsetOptions, new EsniResourcesCreationOptions(), new AquilaChannelCreationOptions(), new ThirdPartyEndpointCreationOptions());

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count());
            Assert.Equal($"g{game.Id}{game.AwayTeam.Abbr}{game.Media.ElementAt(1).Id}{game.HomeTeam.Abbr}", result.ElementAt(0).PlayoutId, true);
            Assert.Equal($"g{game.Id}{game.AwayTeam.Abbr}{game.Media.ElementAt(2).Id}{game.HomeTeam.Abbr}", result.ElementAt(1).PlayoutId, true);
            Assert.Equal($"g{game.Id}{game.AwayTeam.Abbr}{game.Media.ElementAt(3).Id}{game.HomeTeam.Abbr}", result.ElementAt(2).PlayoutId, true);

            foreach (var intentMetadata in result)
            {
                Assert.Equal(3, intentMetadata.CustomOffsets.Count);
                Assert.True(intentMetadata.CustomOffsets.TryGetValue(NbaWorkflowIds.EventInfrastructureStart, out var offsetStart));
                Assert.Equal(TimeSpan.FromMinutes(-5), offsetStart);
                Assert.True(intentMetadata.CustomOffsets.TryGetValue(NbaWorkflowIds.EventInfrastructureSetup, out var offsetSetup));
                Assert.Equal(TimeSpan.FromMinutes(-20), offsetSetup);
                Assert.True(intentMetadata.CustomOffsets.TryGetValue(NbaWorkflowIds.EventInfrastructureEnd, out var offsetEnd));
                Assert.Equal(TimeSpan.FromMinutes(30), offsetEnd);
                Assert.False(intentMetadata.CustomOffsets.ContainsKey(NbaWorkflowIds.EventReachedTipoffTime));
            }
        }

        /// <summary>
        /// Gets the <see cref="IEnumerable{IntentMetaData}"/> when medias has the same encorder should returns one with a valid PlayoutId.
        /// </summary>
        [Fact]
        public void GetIntentMetaDatas_WhenMediasHasTheSameEncoder_ShouldReturnOneWithValidPlayoutId()
        {
            // Arrange
            var game = this.GetMockGmsGameData();
            var customWorkflowOffsetOptions = new CustomWorkflowOffsetOptions
            {
                CustomWorkflowOffsets = new List<CustomWorkflowOffset>(),
            };
            game.Media.ElementAt(2).Schedules.First().Operations.Encoder = "1500";
            game.Media.ElementAt(3).Schedules.First().Operations.Encoder = "1500";

            // Act
            var result = NextGenExtensions.GetIntentMetaDatas(game, customWorkflowOffsetOptions, new EsniResourcesCreationOptions(), new AquilaChannelCreationOptions(), new ThirdPartyEndpointCreationOptions());

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count());
            Assert.Equal($"g{game.Id}{game.AwayTeam.Abbr}{game.Media.ElementAt(1).Id}{game.HomeTeam.Abbr}", result.ElementAt(0).PlayoutId, true);
            Assert.Null(result.ElementAt(1).PlayoutId);
            Assert.Null(result.ElementAt(2).PlayoutId);
        }

        /// <summary>
        /// HasCustomOffset should returns true.
        /// </summary>
        [Fact]
        public void HasCustomOffset_GetTrue()
        {
            var customOffsetOptions = new CustomWorkflowOffsetOptions
            {
                CustomWorkflowOffsets = new List<CustomWorkflowOffset>
                {
                    new CustomWorkflowOffset
                    {
                        KvpKey = CustomOffsetKeyStart,
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                    },
                },
            };

            var keyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                   Key = CustomOffsetKeyStart,
                   Value = "-5",
                },
            };

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.True(NextGenExtensions.HasCustomOffset(schedule, new MediaInfo(), customOffsetOptions, NbaWorkflowIds.EventInfrastructureStart));
        }

        /// <summary>
        /// HasCustomOffset should returns false when not exists KeyValuePair.
        /// </summary>
        [Fact]
        public void HasCustomOffset_GetFalse_WhenNotExistsKeyValuePair()
        {
            var customOffsetOptions = new CustomWorkflowOffsetOptions
            {
                CustomWorkflowOffsets = new List<CustomWorkflowOffset>
                {
                    new CustomWorkflowOffset
                    {
                        KvpKey = CustomOffsetKeyStart,
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                    },
                },
            };

            var keyValuePairs = new List<KeyValuePair>();

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.False(NextGenExtensions.HasCustomOffset(schedule, new MediaInfo(), customOffsetOptions, NbaWorkflowIds.EventInfrastructureStart));
        }

        /// <summary>
        /// HasPreGameExperience should returns true.
        /// </summary>
        [Fact]
        public void ProductionHasPostGame_GetTrue()
        {
            var game = this.GetMockGmsGameForDynEData();

            // Act
            var hasPostGame = NextGenExtensions.ProductionHasPostGame("g0022271070atl1000437bos", game);

            // Assert
            Assert.True(hasPostGame);
        }

        /// <summary>
        /// HasPreGameExperience should returns true.
        /// </summary>
        [Fact]
        public void HasPreGameExperience_GetTrue()
        {
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();

            var keyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                   Key = NssAssociatedExperiencesKey,
                   Value = "pregame , postgame",
                },
            };

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.True(NextGenExtensions.HasPreGameExperience(schedule, new MediaInfo(), esniResourcesCreationOptions));
        }

        /// <summary>
        /// HasPreGameExperience should returns false when not exists KeyValuePair.
        /// </summary>
        [Fact]
        public void HasPreGameExperience_GetFalse_WhenNotExistsKeyValuePair()
        {
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();

            var keyValuePairs = new List<KeyValuePair>();

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.False(NextGenExtensions.HasPreGameExperience(schedule, new MediaInfo(), esniResourcesCreationOptions));
        }

        /// <summary>
        /// HasPostGameExperience should returns true.
        /// </summary>
        [Fact]
        public void HasPostGameExperience_GetTrue()
        {
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();

            var keyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                   Key = NssAssociatedExperiencesKey,
                   Value = "pregame , postgame",
                },
            };

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.True(NextGenExtensions.HasPostGameExperience(schedule, new MediaInfo(), esniResourcesCreationOptions));
        }

        /// <summary>
        /// HasPostGameExperience should returns false when not exists KeyValuePair.
        /// </summary>
        [Fact]
        public void HasPostGameExperience_GetFalse_WhenNotExistsKeyValuePair()
        {
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();

            var keyValuePairs = new List<KeyValuePair>();

            var schedule = new Schedule { Operations = new Operation { KeyValuePairs = keyValuePairs } };

            // Act & assert
            Assert.False(NextGenExtensions.HasPostGameExperience(schedule, new MediaInfo(), esniResourcesCreationOptions));
        }

        /// <summary>
        /// Gets the AquilaChannelsCreationInfo from game should work.
        /// </summary>
        [Fact]
        public void GetThirdPartyCreationInfo_FromGame_ShouldWork()
        {
            // Arrange
            var game = this.GetMockGmsGameData();
            var thirdPartyEndpointCreationOptions = new ThirdPartyEndpointCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
                PoolUUID = "pool",
            };
            game.Media.FirstOrDefault().KeyValuePairs.FirstOrDefault().Key = "NSS-Third-Party";
            game.Media.FirstOrDefault().KeyValuePairs.FirstOrDefault().Value = "true";
            game.Media.FirstOrDefault().ThirdPartyStreamUrls = new List<ThirdPartyStreamUrl>()
            {
                new ThirdPartyStreamUrl()
                {
                    Name = "Fairplay",
                    Url = new Uri("https://digitalstream.nba.com/radio/ratl-eng-01/1080p/play"),
                },
            };
            game.Media.FirstOrDefault().Schedules.FirstOrDefault().Active = true;
            game.Media.FirstOrDefault().Schedules.FirstOrDefault().Operations.Encoder = "syn-01";
            game.Media.FirstOrDefault().Schedules.FirstOrDefault().Resolution = "1080p";

            // Act
            var results = NextGenExtensions.GetQuortexEndpointsCreationInfo(game, thirdPartyEndpointCreationOptions, game.Media);

            // Assert
            var result = results.FirstOrDefault();
            Assert.NotNull(result);
            Assert.Single(results);
            Assert.Equal("ratl-eng-01/1080p/play/index.m3u8", result.CustomPath);
        }

        /// <summary>
        /// Gets the AquilaChannelsCreationInfo from game should work.
        /// </summary>
        [Fact]
        public void GetAquilaChannelsCreationInfo_FromGame_ShouldWork()
        {
            // Arrange
            var game = this.GetMockGmsGameData();
            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions
            {
                NSSGeoredundantRegions = NssGeoredundantRegionsKey,
                NSSGeoRedundantRegionsSeparator = ",",
            };

            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                LocalPolicyDuration = "PT75H",
                RegionalPolicyDuration = "PT5H",
                WorldPolicyDuration = "PT3H",
                MediaPointEffectiveTimeOffset = new TimeSpan(-1, 0, 0),
                MediaPointExpiresTimeOffset = new TimeSpan(1, 0, 0),
                MediaPointMatchTimeOffset = new TimeSpan(0, -1, 0),
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };

            // Act
            var result = NextGenExtensions.GetAquilaChannelsCreationInfo(game, aquilaChannelCreationOptions, esniResourcesCreationOptions, game.Media);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(game.Media.Count(), result.Count());

            var gameNameWithId1 = game.GetMediaName(game, game.Media.ToArray()[1]);
            var channel1 = result.First(x => x.ChannelId == gameNameWithId1);
            Assert.NotNull(channel1);
            Assert.Equal(gameNameWithId1, channel1.ChannelName);
            Assert.Equal("1500", channel1.EncoderId);
            Assert.Equal("Game_null", channel1.MediaResolution);
            Assert.True(channel1.PrimaryFeed);
            Assert.Contains("eastus", channel1.Regions);
            Assert.Contains("centralus", channel1.Regions);

            var gameNameWithId2 = game.GetMediaName(game, game.Media.ToArray()[2]);
            var channel2 = result.First(x => x.ChannelId == gameNameWithId2);
            Assert.NotNull(channel2);
            Assert.Equal(gameNameWithId2, channel2.ChannelName);
            Assert.Equal("1200", channel2.EncoderId);
            Assert.Equal("Game_1080p", channel2.MediaResolution);
            Assert.False(channel2.PrimaryFeed);
            Assert.Contains("eastus", channel2.Regions);
            Assert.Contains("centralus", channel2.Regions);

            var gameNameWithId3 = game.GetMediaName(game, game.Media.ToArray()[3]);
            var channel3 = result.First(x => x.ChannelId == gameNameWithId3);
            Assert.NotNull(channel3);
            Assert.Equal(gameNameWithId3, channel3.ChannelName);
            Assert.Null(channel3.EncoderId);
            Assert.Equal("Game_null", channel3.MediaResolution);
            Assert.False(channel3.PrimaryFeed);
            Assert.Null(channel3.Regions);
        }

        /// <summary>
        /// Get AquilaChannelCreation info with low latency template.
        /// </summary>
        [Fact]
        public void GetAquilaChannelsCreationInfo_ForLowLatencyTempate_ShouldWorl()
        {
            var game = this.GetMockGmsGameForDynEData();
            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions
            {
                NSSGeoredundantRegions = NssGeoredundantRegionsKey,
                NSSGeoRedundantRegionsSeparator = ",",
                DynamicEntitlementsCustomAquilaTemplate = "Event_Date_Range",
            };

            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                LocalPolicyDuration = "PT75H",
                RegionalPolicyDuration = "PT5H",
                WorldPolicyDuration = "PT3H",
                MediaPointEffectiveTimeOffset = new TimeSpan(-1, 0, 0),
                MediaPointExpiresTimeOffset = new TimeSpan(1, 0, 0),
                MediaPointMatchTimeOffset = new TimeSpan(0, -1, 0),
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };

            // Act
            var result = NextGenExtensions.GetAquilaChannelsCreationInfo(game, aquilaChannelCreationOptions, esniResourcesCreationOptions, game.Media);

            Assert.NotNull(result);

            var gameNameWithId1 = game.GetMediaName(game, game.Media.ToArray()[0]);
            var channel1 = result.First(x => x.ChannelId == gameNameWithId1);
            Assert.NotNull(channel1);
            Assert.Equal(gameNameWithId1, channel1.ChannelName);
            Assert.Equal("13", channel1.EncoderId);
            Assert.Equal(aquilaChannelCreationOptions.DynamicEntitlementsCustomAquilaTemplate, channel1.MediaResolution);
        }

        /// <summary>
        /// Gets the AquilaChannelsCreationInfo from event should work.
        /// </summary>
        [Fact]
        public void GetAquilaChannelsCreationInfo_FromEvent_ShouldWork()
        {
            // Arrange
            var gmsEvent = GetEventWithNssMedia();
            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions();
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                LocalPolicyDuration = "PT75H",
                RegionalPolicyDuration = "PT5H",
                WorldPolicyDuration = "PT3H",
                MediaPointEffectiveTimeOffset = new TimeSpan(-1, 0, 0),
                MediaPointExpiresTimeOffset = new TimeSpan(1, 0, 0),
                MediaPointMatchTimeOffset = new TimeSpan(0, -1, 0),
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };

            // Act
            var result = NextGenExtensions.GetAquilaChannelsCreationInfo(gmsEvent, aquilaChannelCreationOptions, esniResourcesCreationOptions, gmsEvent.Media);

            // Assert
            Assert.NotNull(result);
            var gameNameWithId1 = gmsEvent.GetMediaName(gmsEvent, gmsEvent.Media.First());
            var channel = result.First(x => x.ChannelId == gameNameWithId1);
            Assert.NotNull(channel);
            Assert.Equal(gameNameWithId1, channel.ChannelName);
            Assert.Equal("Event_null", channel.MediaResolution);
            Assert.True(channel.PrimaryFeed);
            Assert.Null(channel.EncoderId);
            Assert.Null(channel.Regions);
        }

        /// <summary>
        /// Get Tvp Event CreationInfo ShouldWork.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_ShouldWork()
        {
            var game = this.GetMockGmsGameData();
            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                ProductionQualityLevel = "a",
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = "1,4,6",
                NssGeoAllow = NssGeoAllowKey,
                NssGeoDeny = NssGeoBlockKey,
                GeoPolicyDeliminator = ",",
            };
            var dateTimeServiceMock = new Mock<IDateTime>();
            dateTimeServiceMock.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(game, TimeSpan.Zero, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(result.EventExternalId, game.Id);
            Assert.Equal($"{game.AwayTeam.Abbr} @ {game.HomeTeam.Abbr} on {game.DateTime.Value.ToEasternTimeZone():yyyy-MM-dd}", result.EventName);
        }

        /// <summary>
        /// Get Tvp Event CreationInfo ShouldWork.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_ThirdPartyProduction_ShouldWork()
        {
            var game = this.GetMockGmsGameForThirdParty();
            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                ProductionQualityLevel = "a",
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = "1,4,6",
                NssGeoAllow = NssGeoAllowKey,
                NssGeoDeny = NssGeoBlockKey,
                GeoPolicyDeliminator = ",",
                IsNSSThirdPartyKey = "NSS-ThirdParty",
                ThirdPartyDRMs = "Fairplay,Widevine,Widevine1,Widevine3,Playready",
            };
            var dateTimeServiceMock = new Mock<IDateTime>();
            dateTimeServiceMock.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(game, TimeSpan.Zero, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(result.EventExternalId, game.Id);
            Assert.Single(result.Productions);
            Assert.NotEmpty(result.Productions.FirstOrDefault().ThirdPartyPlaybackInfo);
        }

        /// <summary>
        /// Get Tvp Event CreationInfo PreGameOffset ShouldWork.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_PreGameOffset_KVP_exist_ShouldWork()
        {
            var game = this.GetMockGmsGameData();
            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                ProductionQualityLevel = "a",
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = "1,4,6",
                NssGeoAllow = NssGeoAllowKey,
                NssGeoDeny = NssGeoBlockKey,
                GeoPolicyDeliminator = ",",
            };
            var dateTimeServiceMock = new Mock<IDateTime>();
            dateTimeServiceMock.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(game, TimeSpan.Zero, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(game.Id, result.EventExternalId);
            Assert.Equal("90", result.Productions[1].PreGameStartOffSetMins);
        }

        /// <summary>
        /// Tests <see cref="NextGenExtensions.GetTvpEventCreationInfo"/> for a game without NSS medias.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_WithoutNssMedias_ShouldReturnEventWithDummyProduction()
        {
            var game = this.GetMockGmsGameData();
            game.Media.ForEach(x => x.MediaType.Name = "NotNSS");

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                ProductionQualityLevel = "a",
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = "1,4,6",
                NssGeoAllow = NssGeoAllowKey,
                NssGeoDeny = NssGeoBlockKey,
                GeoPolicyDeliminator = ",",
            };
            var dateTimeServiceMock = new Mock<IDateTime>();
            dateTimeServiceMock.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(game, TimeSpan.Zero, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(result.EventExternalId, game.Id);
            Assert.Equal($"{game.AwayTeam.Abbr} @ {game.HomeTeam.Abbr} on {game.DateTime.Value.ToEasternTimeZone():yyyy-MM-dd}", result.EventName);
            Assert.Single(result.Productions);
            Assert.Equal(game.GetDummyMediaName(), result.Productions[0].ProductionExternalId);
            Assert.Equal(game.GetDummyMediaName(), result.Productions[0].ProductionName);
        }

        /// <summary>
        /// GetTvpEventCreationInfo with Event returns correct TvpEventCreationInfo.
        /// </summary>
        /// <param name="geoAllow">The GeoAllow list.</param>
        /// <param name="geoBlock">The GeoBlock list.</param>
        [Theory]
        [InlineData("A,B", "C,D")]
        [InlineData("A, B", "C, D")]
        public void GetTvpEventCreationInfo_WithEvent_ReturnsCorrectTvpEventCreationInfo(string geoAllow, string geoBlock)
        {
            // Arrange
            var now = DateTimeOffset.MinValue.AddYears(2000);
            var dateTimeServiceMock = new Mock<IDateTime>();
            var gameDuration = TimeSpan.FromHours(3);
            var tvpEventCreationOptions = GetTvpEventCreationOptions();
            var gmsEvent = GetEventWithNssMedia(111);
            gmsEvent.Media.Single().Resolution = "Audio_L";
            var videoMedia = GetNssMedia(222);
            videoMedia.Name = tvpEventCreationOptions.NbaTvMediaName;
            videoMedia.Resolution = "Video_1080p";
            videoMedia.DefaultLanguage = "DefaultLanguage";
            videoMedia.KeyValuePairs = new Collection<KeyValuePair>();
            foreach (var mapping in tvpEventCreationOptions.MediaScheduleKVPToLabelMapping)
            {
                videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = mapping.KeyValuePairKey, Value = "Value" });
            }

            gmsEvent.Media = gmsEvent.Media.Append(videoMedia);
            videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = NssGeoAllowKey, Value = geoAllow });
            videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = NssGeoBlockKey, Value = geoBlock });

            dateTimeServiceMock.Setup(x => x.Now).Returns(now);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(gmsEvent, gameDuration, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(gmsEvent.Name, result.EventName);
            Assert.Equal(gmsEvent.Id, result.EventExternalId);
            Assert.Equal("Event", result.ShowType);
            Assert.Equal(TvpEventStatus.Scheduled, result.EventStatus);
            Assert.Null(result.TournamentSeasonId);
            Assert.Equal(gmsEvent.Name, result.EventScheduleName);
            Assert.Equal(now.AddMinutes(-1), result.EventScheduleStartUtc);
            Assert.Equal(gmsEvent.DateTime + gameDuration, result.EventScheduleEndUtc);
            Assert.Equal(string.Empty, result.EventScheduleUpid);
            Assert.Equal("Special", result.EventType);
            Assert.Single(result.Productions.Where(x => x.AudioOnly));
            Assert.Single(result.Productions.Where(x => !x.AudioOnly));
            Assert.Equal(gmsEvent.Media.Count(), result.Productions.Count);
            Assert.Equal(Enumerable.Empty<string>(), result.GamePackageParentPackages);

            // Labels
            Assert.DoesNotContain(tvpEventCreationOptions.LanguageKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.LiftBlackoutSubscriptionLabelKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.Contains(tvpEventCreationOptions.LanguageKey, result.Productions[1].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelKey, result.Productions[1].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.LiftBlackoutSubscriptionLabelKey, result.Productions[1].ProductionLabels.Select(x => x.Key));

            // GeoRestrictions
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-A-Allow"
                    && x.ExternalId == "ContentRestriction-A-Allow"
                    && x.Mode == "Allow"
                    && x.Locations.Single() == "A");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-B-Allow"
                    && x.ExternalId == "ContentRestriction-B-Allow"
                    && x.Mode == "Allow"
                    && x.Locations.Single() == "B");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-C-Block"
                    && x.ExternalId == "ContentRestriction-C-Block"
                    && x.Mode == "Block"
                    && x.Locations.Single() == "C");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-D-Block"
                    && x.ExternalId == "ContentRestriction-D-Block"
                    && x.Mode == "Block"
                    && x.Locations.Single() == "D");

            // Media/Schedule KVP -> TVP Production label
            foreach (var mapping in tvpEventCreationOptions.MediaScheduleKVPToLabelMapping)
            {
                foreach (var media in gmsEvent.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules))
                {
                    var productionId = gmsEvent.GetChannelId(media);
                    Assert.Equal(1, result.Productions.Count(x => x.ProductionExternalId == productionId));
                    var production = result.Productions.Single(x => x.ProductionExternalId == productionId);
                    var labelCount = media.KeyValuePairs?.Count(x => x.Key == mapping.KeyValuePairKey) ?? 0;
                    Assert.Equal(labelCount, production.ProductionLabels.Count(x => x.Key == mapping.TvpProductionLabelName));
                }
            }
        }

        /// <summary>
        /// GetTvpEventCreationInfo with game returns correct TvpEventCreationInfo.
        /// </summary>
        /// <param name="withParentPackages">Whether to test with a <see cref="KeyValuePair"/> for the ParentPackages feature or not.</param>
        /// <param name="geoAllow">The GeoAllow list.</param>
        /// <param name="geoBlock">The GeoBlock list.</param>
        [Theory]
        [InlineData(true, "A,B", "C,D")]
        [InlineData(false, "A, B", "C, D")]
        public void GetTvpEventCreationInfo_WithGame_ReturnsCorrectTvpEventCreationInfo(bool withParentPackages, string geoAllow, string geoBlock)
        {
            // Arrange
            var now = DateTimeOffset.MinValue.AddYears(2000);
            var dateTimeServiceMock = new Mock<IDateTime>();
            var gameDuration = TimeSpan.FromHours(3);
            var tvpEventCreationOptions = GetTvpEventCreationOptions();
            var gmsGame = GetGameWithNssMedia(111);

            if (withParentPackages)
            {
                gmsGame.KeyValuePairs = new List<KeyValuePair> { new KeyValuePair { Key = "NSS-Parent-Packages", Value = "a,b" } };
            }

            gmsGame.Media.Single().Resolution = "Audio_L";
            var videoMedia = GetNssMedia(222);
            videoMedia.Name = tvpEventCreationOptions.NbaTvMediaName;
            videoMedia.Resolution = "Video_1080p";
            videoMedia.DefaultLanguage = "DefaultLanguage";
            videoMedia.KeyValuePairs = new Collection<KeyValuePair>();
            foreach (var mapping in tvpEventCreationOptions.MediaScheduleKVPToLabelMapping)
            {
                videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = mapping.KeyValuePairKey, Value = "Value" });
            }

            videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = "NSS-SCTE-Available", Value = "true" });
            videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = NssGeoAllowKey, Value = geoAllow });
            videoMedia.KeyValuePairs = videoMedia.KeyValuePairs.Append(new KeyValuePair { Key = NssGeoBlockKey, Value = geoBlock });

            gmsGame.Media = gmsGame.Media.Append(videoMedia);

            dateTimeServiceMock.Setup(x => x.Now).Returns(now);

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(gmsGame, gameDuration, tvpEventCreationOptions, dateTimeServiceMock.Object);

            // Assert
            Assert.Equal(gmsGame.Id, result.EventExternalId);
            Assert.Equal("Event", result.ShowType);
            Assert.Equal(TvpEventStatus.Scheduled, result.EventStatus);
            Assert.Null(result.TournamentSeasonId);
            Assert.Equal(now.AddMinutes(-1), result.EventScheduleStartUtc);
            Assert.Equal(gmsGame.DateTime + gameDuration, result.EventScheduleEndUtc);
            Assert.Equal(string.Empty, result.EventScheduleUpid);
            Assert.Equal("Game", result.EventType);
            Assert.Single(result.Productions.Where(x => x.AudioOnly));
            Assert.Single(result.Productions.Where(x => !x.AudioOnly));

            if (withParentPackages)
            {
                Assert.Equal("a", result.GamePackageParentPackages.ElementAt(0));
                Assert.Equal("b", result.GamePackageParentPackages.ElementAt(1));
            }
            else
            {
                Assert.Equal(Enumerable.Empty<string>(), result.GamePackageParentPackages);
            }

            // Labels
            Assert.DoesNotContain(tvpEventCreationOptions.LanguageKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.LiftBlackoutSubscriptionLabelKey, result.Productions[0].ProductionLabels.Select(x => x.Key));
            Assert.Contains(tvpEventCreationOptions.AdInsertionPlaybackRestriction, result.Productions[0].PlaybackRestrictions);
            Assert.False(result.Productions[0].HasInBandScte35);
            Assert.Contains(tvpEventCreationOptions.LanguageKey, result.Productions[1].ProductionLabels.Select(x => x.Key));
            Assert.Contains(tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelKey, result.Productions[1].ProductionLabels.Select(x => x.Key));
            Assert.Contains(tvpEventCreationOptions.LiftBlackoutSubscriptionLabelKey, result.Productions[1].ProductionLabels.Select(x => x.Key));
            Assert.DoesNotContain(tvpEventCreationOptions.AdInsertionPlaybackRestriction, result.Productions[1].PlaybackRestrictions);
            Assert.True(result.Productions[1].HasInBandScte35);

            // GeoRestrictions
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-A-Allow"
                    && x.ExternalId == "ContentRestriction-A-Allow"
                    && x.Mode == "Allow"
                    && x.Locations.Single() == "A");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-B-Allow"
                    && x.ExternalId == "ContentRestriction-B-Allow"
                    && x.Mode == "Allow"
                    && x.Locations.Single() == "B");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-C-Block"
                    && x.ExternalId == "ContentRestriction-C-Block"
                    && x.Mode == "Block"
                    && x.Locations.Single() == "C");
            Assert.Contains(
                result.Productions[1].GeoRestrictionPolicies,
                x => x.Name == "ContentRestriction-D-Block"
                    && x.ExternalId == "ContentRestriction-D-Block"
                    && x.Mode == "Block"
                    && x.Locations.Single() == "D");

            // Media/Schedule KVP -> TVP Production label
            foreach (var mapping in tvpEventCreationOptions.MediaScheduleKVPToLabelMapping)
            {
                foreach (var media in gmsGame.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules))
                {
                    var productionId = gmsGame.GetChannelId(media);
                    Assert.Equal(1, result.Productions.Count(x => x.ProductionExternalId == productionId));
                    var production = result.Productions.Single(x => x.ProductionExternalId == productionId);
                    var labelCount = media.KeyValuePairs?.Count(x => x.Key == mapping.KeyValuePairKey) ?? 0;
                    Assert.Equal(labelCount, production.ProductionLabels.Count(x => x.Key == mapping.TvpProductionLabelName));
                }
            }
        }

        /// <summary>
        /// GetTvpEventCreationInfo when game has ScheduleCodeUnexistent, throws NotImplementedException.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_WhenGameHasScheduleCodeUnexistent_ThrowsNotImplementedException()
        {
            // Arrange
            var now = DateTimeOffset.MinValue.AddYears(2000);
            var dateTimeServiceMock = new Mock<IDateTime>();
            var game = this.GetMockGmsGameData();
            game.ScheduleCode = "UnexistentScheduleCode";

            dateTimeServiceMock.Setup(x => x.Now).Returns(now);

            // Act
            Assert.Throws<NotSupportedException>(
                () => NextGenExtensions.GetTvpEventCreationInfo(game, TimeSpan.Zero, new TvpEventCreationOptions(), dateTimeServiceMock.Object));
        }

        /// <summary>
        /// GetTvpEventCreationInfo with Event not relevant for ochestration platform returns null.
        /// </summary>
        [Fact]
        public void GetTvpEventCreationInfo_WithEventNotRelevantForOrchestrationPlatform_ReturnsNull()
        {
            // Arrange
            var dateTimeServiceMock = new Mock<IDateTime>();
            var gmsEvent = new GmsEvent();

            // Act
            var result = NextGenExtensions.GetTvpEventCreationInfo(gmsEvent, TimeSpan.Zero, new TvpEventCreationOptions(), dateTimeServiceMock.Object);

            // Assert
            Assert.Null(result);
        }

        /// <summary>
        /// IsChannelPrimary when Kvp is false or invalid bool returns false.
        /// </summary>
        /// <param name="falsyValue">The falsy value.</param>
        [Theory]
        [InlineData("false")]
        [InlineData("False")]
        [InlineData("invalidBool")]
        [InlineData(null)]
        public void IsChannelPrimary_WhenKvpIsfalseOrInvalidBool_ReturnsFalse(string falsyValue)
        {
            var game = GetGameWithTwoNssMedia();
            var secondMedia = game.Media.ElementAt(1);
            var secondMediaName = game.GetMediaName(game, secondMedia);
            secondMedia.Schedules.Single().Operations.KeyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = NssPrimaryFeedKey,
                    Value = falsyValue,
                },
            };

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, secondMediaName);

            // Assert
            Assert.False(result);
        }

        /// <summary>
        /// IsChannelPrimary when second media has kvp and first has encoder, returns true for second media.
        /// </summary>
        /// <param name="trueAsString">The falsy value.</param>
        [Theory]
        [InlineData("true")]
        [InlineData("True")]
        public void IsChannelPrimary_WhenSecondMediaHasKvpAndFirstHasEncoder_ReturnsTrueForSecondMedia(string trueAsString)
        {
            var game = GetGameWithTwoNssMedia();
            var firstMedia = game.Media.ElementAt(0);
            var secondMedia = game.Media.ElementAt(1);
            var secondMediaName = game.GetMediaName(game, secondMedia);
            firstMedia.Schedules.Single().Operations.Encoder = "any";
            secondMedia.Schedules.Single().Operations.KeyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = NssPrimaryFeedKey,
                    Value = trueAsString,
                },
            };

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, secondMediaName);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsChannelPrimary when second media has encoder and first no encoder nor kvp, returns true for second media.
        /// </summary>
        [Fact]
        public void IsChannelPrimary_WhenSecondMediaHasEncoderAndFirstNoEncoderNorKvp_ReturnsTrueForSecondMedia()
        {
            var game = GetGameWithTwoNssMedia();
            var secondMedia = game.Media.ElementAt(1);
            var secondMediaName = game.GetMediaName(game, secondMedia);
            secondMedia.Schedules.Single().Operations.Encoder = "any";

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, secondMediaName);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsChannelPrimary when both media have Kvp returns true for first media.
        /// </summary>
        [Fact]
        public void IsChannelPrimary_WhenBothMediaHaveKvp_ReturnsTrueForFirstMedia()
        {
            var game = GetGameWithTwoNssMedia();
            var kvp = new List<KeyValuePair> { new KeyValuePair { Key = NssPrimaryFeedKey, Value = "true" } };
            var firstMedia = game.Media.ElementAt(0);
            var secondMedia = game.Media.ElementAt(1);
            var firstMediaName = game.GetMediaName(game, firstMedia);
            firstMedia.Schedules.Single().Operations.KeyValuePairs = kvp;
            secondMedia.Schedules.Single().Operations.KeyValuePairs = kvp;

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, firstMediaName);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsChannelPrimary when both media have encoder returns true for first media.
        /// </summary>
        [Fact]
        public void IsChannelPrimary_WhenBothMediaHaveEncoder_ReturnsTrueForFirstMedia()
        {
            var game = GetGameWithTwoNssMedia();
            var firstMedia = game.Media.ElementAt(0);
            var secondMedia = game.Media.ElementAt(1);
            var firstMediaName = game.GetMediaName(game, firstMedia);
            firstMedia.Schedules.Single().Operations.Encoder = "any";
            secondMedia.Schedules.Single().Operations.Encoder = "any";

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, firstMediaName);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsChannelPrimary when both media have no Kvp nor encoder returns true for first media.
        /// </summary>
        [Fact]
        public void IsChannelPrimary_WhenBothMediaHaveNoKvpNorEncoder_ReturnsTrueForFirstMedia()
        {
            var game = GetGameWithTwoNssMedia();
            var firstMedia = game.Media.ElementAt(0);
            var firstMediaName = game.GetMediaName(game, firstMedia);

            var result = NextGenExtensions.IsChannelPrimary(game, NssPrimaryFeedKey, firstMediaName);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// Gets the primary media should returns a media.
        /// </summary>
        /// <param name="gmsGame">The <see cref="GmsGame"/>.</param>
        /// <param name="nssPrimaryMediaKey">The nss primary media key.</param>
        [Theory]
#pragma warning disable CA1825 // Avoid zero-length array allocations
        [MemberData(nameof(NextGenExtensionsTestsData.GetPrimaryMedia_ShouldReturnMedia), MemberType = typeof(NextGenExtensionsTestsData))]
#pragma warning restore CA1825 // Avoid zero-length array allocations
        public void GetPrimaryMedia_ShouldReturnMedia(GmsGame gmsGame, string nssPrimaryMediaKey)
        {
            var primaryMedia = gmsGame.GetPrimaryMedia(nssPrimaryMediaKey);

            Assert.NotNull(primaryMedia);
            Assert.Equal(1234567, primaryMedia.Id);
        }

        /// <summary>
        /// IsMediaLiveToOnDemand returns true if media id is not present in the list of media ids without L2V.
        /// </summary>
        [Fact]
        public void IsMediaLiveToOnDemand_GetTrue()
        {
            var game = this.GetMockGmsGameData();
            var mediaId = game.Media.FirstOrDefault(x => x.Schedules.Any(y => y.Operations.KeyValuePairs != null)).Id;

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = string.Empty,
            };

            var result = NextGenExtensions.IsMediaLiveToOnDemand(game, tvpEventCreationOptions, mediaId);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsMediaLiveToOnDemand returns false if media id is present in the list of media ids without L2V.
        /// </summary>
        [Fact]
        public void IsMediaLiveToOnDemand_GetFalse()
        {
            var game = this.GetMockGmsGameData();
            var mediaId = game.Media.FirstOrDefault(x => x.Schedules.Any(y => y.Operations.KeyValuePairs != null)).Id;

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = mediaId.ToString(CultureInfo.InvariantCulture),
            };

            var result = NextGenExtensions.IsMediaLiveToOnDemand(game, tvpEventCreationOptions, mediaId);

            // Assert
            Assert.False(result);
        }

        /// <summary>
        /// IsMediaLiveToOnDemand returns false if media id is present in the list of media ids without L2V.
        /// </summary>
        [Fact]
        public void MediaHasScte35Available_GetFalse()
        {
            var game = this.GetMockGmsGameData();
            var mediaId = game.Media.FirstOrDefault(x => x.Schedules.Any(y => y.Operations.KeyValuePairs != null)).Id;

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = mediaId.ToString(CultureInfo.InvariantCulture),
            };

            var result = NextGenExtensions.MediaHasScte35Available(game, tvpEventCreationOptions, mediaId);

            // Assert
            Assert.False(result);
        }

        /// <summary>
        /// IsMediaLiveToOnDemand returns false if media id is present in the list of media ids without L2V.
        /// </summary>
        [Fact]
        public void MediaHasScte35Available_WhenNoneOfTheSchedulesHaveEncoder_ShouldUseGetScheduleWithoutEncoderAndReturnFalse()
        {
            var game = this.GetMockGmsGameData();
            game.Media
                .ForEach(media => media.Schedules
                .ForEach(schedule => schedule.Operations.Encoder = null));
            var mediaId = game.Media.FirstOrDefault(x => x.Schedules.Any(y => y.Operations.KeyValuePairs != null)).Id;

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = mediaId.ToString(CultureInfo.InvariantCulture),
            };

            var result = NextGenExtensions.MediaHasScte35Available(game, tvpEventCreationOptions, mediaId);

            // Assert
            Assert.False(result);
        }

        /// <summary>
        /// Gets the TVP event cleanup information with proper parameter should pass.
        /// </summary>
        [Fact]
        public void GetTvpEventCleanupInfo_WithProperParameter_ShouldPass()
        {
            var tvpEventCleanupInfo = NextGenExtensions.GetTvpEventCleanupInfo(this.GetMockGmsGameData());

            Assert.Equal("Test", tvpEventCleanupInfo.EventExternalId);
        }

        /// <summary>
        /// Gets the team zip update workflow intents with proper parameter should pass.
        /// </summary>
        [Fact]
        public void GetTeamZipUpdateWorkflowIntents_WithProperParameter_ShouldPass()
        {
            var datetimeNow = new DateTimeOffset(1900, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var mockRepository = new MockRepository(MockBehavior.Loose);
            var mockDateTime = mockRepository.Create<IDateTime>();
            mockDateTime.Setup(x => x.Now).Returns(datetimeNow);

            var gmsTeamZips = new GmsTeamZips
            {
                TeamId = "TeamIdTest",
            };

            var esniAudienceList = new List<EsniAudience>
            {
                new EsniAudience
                {
                    Id = "IdTest",
                },
            };

            var result = NextGenExtensions.GetTeamZipUpdateWorkflowIntent(gmsTeamZips, esniAudienceList, mockDateTime.Object);

            Assert.NotNull(result);
            Assert.Equal("TeamIdTest", result.ChannelId);
            Assert.Equal(datetimeNow.UtcDateTime, result.LiveEventTime);
            Assert.Equal(NbaWorkflowIds.AudienceSetup, result.WorkflowId);
            Assert.Null(result.WorkflowOffset);
            var prismaActorSpecificDetails = result.ActorSpecificDetails.First(x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(prismaActorSpecificDetails.Data);
            Assert.IsType<PrismaAudienceInfo>(prismaActorSpecificDetails.Data);
            var prismaActorSpecificDetailsData = (PrismaAudienceInfo)prismaActorSpecificDetails.Data;
            Assert.Single(prismaActorSpecificDetailsData.AudienceIdsToAdd);
            Assert.Equal("IdTest", prismaActorSpecificDetailsData.AudienceIdsToAdd.First());
            var tvpActorSpecificDetails = result.ActorSpecificDetails[1];
            Assert.NotNull(tvpActorSpecificDetails.Data);
            Assert.IsType<GmsTeamZips>(tvpActorSpecificDetails.Data);
        }

        /// <summary>
        /// GetPrismaInfo with proper parameter for event should pass.
        /// </summary>
        /// <param name="iso3166Country">The ISO3166 country.</param>
        /// <param name="esniMediaRegion">The ESNI media region.</param>
        /// <param name="policyDuration">The policy duration.</param>
        /// <param name="esniMediaDistribution">The ESNI media distribution.</param>
        /// <param name="distributionPath">The distribution path for the policy id.</param>
        /// <param name="priority">The priority.</param>
        [Theory]
        [InlineData(EsniMediaNames.US, EsniMediaNames.RegionUnitedStates, "PT5H", EsniMediaNames.DistributionRegional, "regional", 1)]
        [InlineData(EsniMediaNames.CA, EsniMediaNames.RegionCanada, null, EsniMediaNames.DistributionRegional, "world", 2)]
        public void GetPrismaInfo_WithProperParametersForEvent_ShouldPass([NotNull] string iso3166Country, string esniMediaRegion, string policyDuration, string esniMediaDistribution, [NotNull] string distributionPath, int priority)
        {
            // Arrange
            var utcNow = DateTimeOffset.UtcNow;
            var anyValidGmsEventId = "any";
            var expectedMediaId = $"e{anyValidGmsEventId}e0";
            string[] iso3166value = iso3166Country == "us" ? new[] { "US", "PR", "VI", "GU" } : new[] { "CA" };
            var validGmsEvent = new GmsEvent
            {
                Id = anyValidGmsEventId,
                Active = true,
                DateTime = utcNow,
                Location = new Location(),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation { Encoder = "abc" } } },
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                    },
                    new MediaInfo
                    {
                        Active = true,
                        MediaType = new MediaType { Name = EsniMediaNames.MediaTV },
                        Distribution = new Distribution { Name = esniMediaDistribution },
                        Region = new Region { Name = esniMediaRegion },
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                    },
                },
            };
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                LocalPolicyDuration = "PT75H",
                RegionalPolicyDuration = "PT5H",
                WorldPolicyDuration = "PT3H",
                MediaPointEffectiveTimeOffset = new TimeSpan(-1, 0, 0),
                MediaPointExpiresTimeOffset = new TimeSpan(1, 0, 0),
                MediaPointMatchTimeOffset = new TimeSpan(0, -1, 0),
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };

            TimeSpan timeSpan = new TimeSpan(0, 0, 0);

            var defaultPolicy = new PrismaPolicy()
            {
                Id = $"/NBA/policy/blackout",
                ViewingPolicy = new List<PrismaViewingPolicy>()
                {
                    new PrismaViewingPolicy()
                    {
                        Id = $"/NBA/viewingpolicy/event",
                        ActionContent = EsniMediaNames.BlackoutActionContent,
                        Audience = new PrismaAudience()
                        {
                            Id = $"/NBA/audience/event",
                            Match = PrismaAudienceMatch.ANY,
                        },
                    },
                },
            };

            var expectedMediaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = new List<PrismaMedia>
                {
                    new PrismaMedia
                    {
                        Id = $"/NBA/media/{expectedMediaId}",
                        MediaPoint = new List<PrismaMediaPoint>
                        {
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/start",
                                MatchTime = utcNow.Add(esniResourcesCreationOptions.MediaPointMatchTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Effective = utcNow.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = utcNow.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                Apply = new List<PrismaPolicyApply>
                                {
                                    new PrismaPolicyApply
                                    {
                                        Duration = policyDuration,
                                        Priority = priority,
                                        Policy = new PrismaPolicy
                                        {
                                            Id = $"/NBA/policy/{distributionPath.ToLowerInvariant()}/{expectedMediaId}",
                                            ViewingPolicy = new List<PrismaViewingPolicy>
                                            {
                                                new PrismaViewingPolicy
                                                {
                                                    Id = $"/NBA/viewingpolicy/{iso3166Country}",
                                                    ActionContent = EsniMediaNames.BlackoutActionContent,
                                                    Audience = new PrismaAudience
                                                    {
                                                        Id = $"/NBA/audience/{iso3166Country}",
                                                        ISO3166CountryCodes = iso3166value,
                                                        Match = PrismaAudienceMatch.ANY,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                    new PrismaPolicyApply
                                    {
                                        Priority = 3,
                                        Policy = defaultPolicy,
                                    },
                                },
                            },
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/end",
                                MatchTime = utcNow,
                                Effective = utcNow.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = utcNow.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Remove = new List<PrismaPolicyRemove>(),
                            },
                        },
                    },
                },
            };

            if (esniMediaRegion.EqualsIgnoreCase(EsniMediaNames.RegionCanada))
            {
                var matchSignal = new PrismaPolicyMatchSignal
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.First().MatchSignal = matchSignal;

                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).MatchSignal = new PrismaPolicyMatchSignal
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = $"/NBA/policy/{distributionPath.ToLowerInvariant()}/{expectedMediaId}" } });
            }

            expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = "/NBA/policy/blackout" } });

            var gameLevelPrismaMedia = JsonConvert.DeserializeObject<PrismaMedia>(JsonConvert.SerializeObject(expectedMediaInfo.MediasToUpsert.First()));
            gameLevelPrismaMedia.Id = $"/NBA/media/{anyValidGmsEventId}";

            foreach (var mediaPoint in gameLevelPrismaMedia.MediaPoint)
            {
                mediaPoint.Id = mediaPoint.Id.Replace(expectedMediaId, anyValidGmsEventId, StringComparison.InvariantCultureIgnoreCase);
            }

            expectedMediaInfo.MediasToUpsert = expectedMediaInfo.MediasToUpsert.Append(gameLevelPrismaMedia);

            // Act
            var result = NextGenExtensions.GetPrismaInfo(validGmsEvent, esniResourcesCreationOptions, NssPrimaryFeedKey, timeSpan);

            // Assert
            result.Should().BeEquivalentTo(expectedMediaInfo);
        }

        /// <summary>
        /// GetPrismaInfo with proper parameter for event should pass.
        /// </summary>
        /// <param name="entitlementExperience">The entitlement.</param>
        [Theory]
        [InlineData("pregame")]
        [InlineData("postgame")]
        [InlineData("pregame,postgame")]
        public void GetPrismaInfo_WithOnlyPreOrPostGameExperienceEvent_ShouldPass([NotNull] string entitlementExperience)
        {
            // Arrange
            var utcNow = DateTimeOffset.UtcNow;
            var anyValidGmsEventId = "any";
            var validGmsEvent = new GmsEvent
            {
                Id = anyValidGmsEventId,
                Active = true,
                DateTime = utcNow,
                Location = new Location(),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation { Encoder = "abc" } } },
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        KeyValuePairs = new List<KeyValuePair>() { new KeyValuePair { Key = NssAssociatedExperiencesKey, Value = entitlementExperience }, },
                    },
                },
            };
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsToIgnore = null;
            esniResourcesCreationOptions.NssBlackoutTeamSeparator = null;
            esniResourcesCreationOptions.NssBlackoutTeamOtaKey = null;
            esniResourcesCreationOptions.NssBlackoutTeamRsnKey = null;

            TimeSpan gameDuration = new TimeSpan(0, 0, 0);

            var expectedProductionId = validGmsEvent.GetMediaName(validGmsEvent, validGmsEvent.Media.First());
            var expectedMediaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = new List<PrismaMedia>()
                {
                    new PrismaMedia
                    {
                        Id = EsniExtensions.GetEsniMediaId(expectedProductionId),
                        GmsMediaId = validGmsEvent.Media.First().Id,
                        MediaPoint = GetGameMediaPoints(validGmsEvent, esniResourcesCreationOptions, gameDuration, expectedProductionId),
                    },
                },
            };

            var mediaToUpsert = expectedMediaInfo.MediasToUpsert.First();
            if (entitlementExperience.Contains("pregame", StringComparison.InvariantCultureIgnoreCase))
            {
                mediaToUpsert.MediaPoint = mediaToUpsert.MediaPoint.Concat(GetPreGameMediaPoints(validGmsEvent, esniResourcesCreationOptions, expectedProductionId)).ToList();
            }

            if (entitlementExperience.Contains("postgame", StringComparison.InvariantCultureIgnoreCase))
            {
                mediaToUpsert.MediaPoint = mediaToUpsert.MediaPoint.Concat(GetPostGameMediaPoints(validGmsEvent, esniResourcesCreationOptions, gameDuration, expectedProductionId)).ToList();
            }

            // Act
            var result = NextGenExtensions.GetPrismaInfo(validGmsEvent, esniResourcesCreationOptions, NssPrimaryFeedKey, gameDuration);

            // Assert
            result.Should().BeEquivalentTo(expectedMediaInfo);
        }

        /// <summary>
        /// GetPrismaInfo with proper parameter for event should pass.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithNoBlackoutsButPreAndPostGameExperienceForGame_ShouldPass()
        {
            // Arrange
            var utcNow = DateTimeOffset.UtcNow;
            var anyValidGmsGameId = "any";
            var validGmsGame = new GmsGame
            {
                Id = anyValidGmsGameId,
                Active = true,
                DateTime = utcNow,
                HomeTeam = new Team { Abbr = "BOS", Id = 1234, Name = "BOSTON" },
                AwayTeam = new Team { Abbr = "NJW", Id = 4567, Name = "NJW" },
                TournamentSeasonId = "2022-2023",
                Location = new Location(),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Id = 100,
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, TeamContext = EsniMediaNames.Home, Operations = new Operation { Encoder = "abc" } } },
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        KeyValuePairs = new List<KeyValuePair> { new KeyValuePair() { Key = NssAssociatedExperiencesKey, Value = "pregame,postgame", } },
                    },
                },
            };
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsToIgnore = null;
            esniResourcesCreationOptions.NssBlackoutTeamSeparator = null;
            esniResourcesCreationOptions.NssBlackoutTeamOtaKey = null;
            esniResourcesCreationOptions.NssBlackoutTeamRsnKey = null;

            TimeSpan gameDuration = new TimeSpan(0, 0, 0);

            var expectedProductionId = validGmsGame.GetMediaName(validGmsGame, validGmsGame.Media.First());

            var mediapoints = new List<PrismaMediaPoint>();
            mediapoints.AddRange(GetGameMediaPoints(validGmsGame, esniResourcesCreationOptions, gameDuration, expectedProductionId));
            mediapoints.AddRange(GetPreGameMediaPoints(validGmsGame, esniResourcesCreationOptions, expectedProductionId));
            mediapoints.AddRange(GetPostGameMediaPoints(validGmsGame, esniResourcesCreationOptions, gameDuration, expectedProductionId));

            var expectedMediaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = new List<PrismaMedia>()
                {
                    new PrismaMedia
                    {
                        Id = EsniExtensions.GetEsniMediaId(expectedProductionId),
                        GmsMediaId = validGmsGame.Media.First().Id,
                        TeamContext = "Home",
                        MediaPoint = mediapoints,
                    },
                },
            };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(validGmsGame, esniResourcesCreationOptions, NssPrimaryFeedKey, gameDuration);

            // Assert
            result.Should().BeEquivalentTo(expectedMediaInfo);
        }

        /// <summary>
        /// GetPrismaInfo with proper parameter for game should pass.
        /// </summary>
        /// <param name="iso3166Country">The ISO3166 country.</param>
        /// <param name="regionName">The PrismaMedia region name.</param>
        /// <param name="policyDuration">The policy duration.</param>
        /// <param name="distributionName">The PrismaMedia distribution name.</param>
        /// <param name="distributionPath">The distribution path for the policy id.</param>
        /// <param name="priority">The priority.</param>
        [Theory]
        [InlineData(EsniMediaNames.US, EsniMediaNames.RegionUnitedStates, "PT6H", EsniMediaNames.DistributionRegional, "regional", 1)]
        [InlineData(EsniMediaNames.CA, EsniMediaNames.RegionCanada, null, EsniMediaNames.DistributionRegional, "world", 2)]
        [InlineData(null, EsniMediaNames.RegionUnitedStates, "PT75H", EsniMediaNames.DistributionRSN, "local", 0)]
        public void GetPrismaInfo_WithBlackoutsAndPreAndPostGameExperiencesForGame_ShouldPass(string iso3166Country, string regionName, string policyDuration, [NotNull] string distributionName, [NotNull] string distributionPath, int priority)
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            string[] iso3166value = iso3166Country == "us" ? new[] { "US", "PR", "VI", "GU" } : new[] { "CA" };
            mediaTv.Distribution.Name = distributionName;
            mediaTv.Region.Name = regionName;
            mediaTv.Schedules.Single().TeamContext = EsniMediaNames.Home;
            game.Media.Single(x => x.IsNssMedia).Schedules.Single().TeamContext = EsniMediaNames.Home;
            game.Media.Single(x => x.IsNssMedia).KeyValuePairs = new List<KeyValuePair> { new KeyValuePair() { Key = NssAssociatedExperiencesKey, Value = "pregame,postgame", } };
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var expectedMediaId = $"ggameidawayteamabbr0hometeamabbr";
            var viewingPolicyGlobalRegional = new PrismaViewingPolicy
            {
                Id = $"/NBA/viewingpolicy/{iso3166Country}",
                ActionContent = EsniMediaNames.BlackoutActionContent,
                Audience = new PrismaAudience
                {
                    Id = $"/NBA/audience/{iso3166Country}",
                    ISO3166CountryCodes = iso3166value,
                    Match = PrismaAudienceMatch.ANY,
                },
            };
#pragma warning disable CA1308
            var viewingPolicyLocal = new PrismaViewingPolicy
            {
                Id = $"/NBA/viewingpolicy/{distributionPath}/{expectedMediaId}",
                ActionContent = EsniMediaNames.BlackoutActionContent,
                Audience = new PrismaAudience
                {
                    Id = $"/NBA/audience/{distributionPath}/{expectedMediaId}",
                    Match = PrismaAudienceMatch.ANY,
                    Audience = new List<PrismaAudience>
                    {
                        new PrismaAudience
                        {
                            XlinkHref = $"/NBA/audience/{EsniMediaNames.US}/{game.HomeTeam.Abbr.ToLowerInvariant()}/{distributionName.ToLowerInvariant()}",
                        },
                    },
                },
            };

            var defaultPolicy = new PrismaPolicy()
            {
                Id = $"/NBA/policy/blackout",
                ViewingPolicy = new List<PrismaViewingPolicy>()
                {
                    new PrismaViewingPolicy()
                    {
                        Id = $"/NBA/viewingpolicy/event",
                        ActionContent = EsniMediaNames.BlackoutActionContent,
                        Audience = new PrismaAudience()
                        {
                            Id = $"/NBA/audience/event",
                            Match = PrismaAudienceMatch.ANY,
                        },
                    },
                },
            };

            TimeSpan gameDuration = new TimeSpan(0, 0, 0);
            var expectedProductionId = game.GetMediaName(game, game.Media.First());
            var expectedMediaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = new List<PrismaMedia>
                {
                    new PrismaMedia
                    {
                        Id = EsniExtensions.GetEsniMediaId(expectedMediaId),
                        MediaPoint = new List<PrismaMediaPoint>
                        {
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/start",
                                MatchTime = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointMatchTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Effective = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                Apply = new List<PrismaPolicyApply>
                                {
                                    new PrismaPolicyApply
                                    {
                                        Duration = policyDuration,
                                        Priority = priority,
                                        Policy = new PrismaPolicy
                                        {
                                            Id = $"/NBA/policy/{distributionPath}/{expectedMediaId}",
                                            ViewingPolicy = new List<PrismaViewingPolicy>()
                                            {
                                                distributionName == EsniMediaNames.DistributionRSN ? viewingPolicyLocal : viewingPolicyGlobalRegional,
                                            },
                                        },
                                    },
                                    new PrismaPolicyApply
                                    {
                                        Priority = 3,
                                        Policy = defaultPolicy,
                                    },
                                },
                            },
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/end",
                                MatchTime = game.DateTime.Value,
                                Effective = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Remove = new List<PrismaPolicyRemove>(),
                            },
                        },
                        TeamContext = EsniMediaNames.Home,
                    },
                },
            };

            if (regionName.EqualsIgnoreCase(EsniMediaNames.RegionCanada))
            {
                var matchSignal = new PrismaPolicyMatchSignal()
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.First().MatchSignal = matchSignal;
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).MatchSignal = new PrismaPolicyMatchSignal
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = $"/NBA/policy/{distributionPath.ToLowerInvariant()}/{expectedMediaId}" } });
            }

            expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = "/NBA/policy/blackout" } });

            var gameLevelPrismaMedia = JsonConvert.DeserializeObject<PrismaMedia>(JsonConvert.SerializeObject(expectedMediaInfo.MediasToUpsert.First()));
            gameLevelPrismaMedia.Id = $"/NBA/media/{game.Id}";

            foreach (var mediaPoint in gameLevelPrismaMedia.MediaPoint)
            {
                mediaPoint.Id = mediaPoint.Id.Replace(expectedMediaId, game.Id, StringComparison.InvariantCultureIgnoreCase);
            }

            expectedMediaInfo.MediasToUpsert = expectedMediaInfo.MediasToUpsert.Append(gameLevelPrismaMedia);

            var dynamicEntitlementsMediapoints = new List<PrismaMediaPoint>();
            dynamicEntitlementsMediapoints.AddRange(GetGameMediaPoints(game, esniResourcesCreationOptions, gameDuration, expectedProductionId));
            dynamicEntitlementsMediapoints.AddRange(GetPreGameMediaPoints(game, esniResourcesCreationOptions, expectedProductionId));
            dynamicEntitlementsMediapoints.AddRange(GetPostGameMediaPoints(game, esniResourcesCreationOptions, gameDuration, expectedProductionId));

            expectedMediaInfo.MediasToUpsert.First().MediaPoint = expectedMediaInfo.MediasToUpsert.First().MediaPoint.Concat(dynamicEntitlementsMediapoints).ToList();

#pragma warning restore CA1308

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            result.Should().BeEquivalentTo(expectedMediaInfo);
        }

        /// <summary>
        /// GetPrismaInfo with proper parameter for game should pass.
        /// </summary>
        /// <param name="iso3166Country">The ISO3166 country.</param>
        /// <param name="regionName">The PrismaMedia region name.</param>
        /// <param name="policyDuration">The policy duration.</param>
        /// <param name="distributionName">The PrismaMedia distribution name.</param>
        /// <param name="distributionPath">The distribution path for the policy id.</param>
        /// <param name="priority">The priority.</param>
        [Theory]
        [InlineData(EsniMediaNames.US, EsniMediaNames.RegionUnitedStates, "PT6H", EsniMediaNames.DistributionRegional, "regional", 1)]
        [InlineData(EsniMediaNames.CA, EsniMediaNames.RegionCanada, null, EsniMediaNames.DistributionRegional, "world", 2)]
        [InlineData(null, EsniMediaNames.RegionUnitedStates, "PT75H", EsniMediaNames.DistributionRSN, "local", 0)]
        public void GetPrismaInfo_WithProperParametersForGame_ShouldPass(string iso3166Country, string regionName, string policyDuration, [NotNull] string distributionName, [NotNull] string distributionPath, int priority)
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            string[] iso3166value = iso3166Country == "us" ? new[] { "US", "PR", "VI", "GU" } : new[] { "CA" };
            mediaTv.Distribution.Name = distributionName;
            mediaTv.Region.Name = regionName;
            mediaTv.Schedules.Single().TeamContext = EsniMediaNames.Home;
            game.Media.Single(x => x.IsNssMedia).Schedules.Single().TeamContext = EsniMediaNames.Home;
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var expectedMediaId = $"ggameidawayteamabbr0hometeamabbr";
            var viewingPolicyGlobalRegional = new PrismaViewingPolicy
            {
                Id = $"/NBA/viewingpolicy/{iso3166Country}",
                ActionContent = EsniMediaNames.BlackoutActionContent,
                Audience = new PrismaAudience
                {
                    Id = $"/NBA/audience/{iso3166Country}",
                    ISO3166CountryCodes = iso3166value,
                    Match = PrismaAudienceMatch.ANY,
                },
            };
            var viewingPolicyLocal = new PrismaViewingPolicy
            {
                Id = $"/NBA/viewingpolicy/{distributionPath}/{expectedMediaId}",
                ActionContent = EsniMediaNames.BlackoutActionContent,
                Audience = new PrismaAudience
                {
                    Id = $"/NBA/audience/{distributionPath}/{expectedMediaId}",
                    Match = PrismaAudienceMatch.ANY,
                    Audience = new List<PrismaAudience>
                    {
                        new PrismaAudience
                        {
                            XlinkHref = $"/NBA/audience/{EsniMediaNames.US}/{game.HomeTeam.Abbr.ToLowerInvariant()}/{distributionName.ToLowerInvariant()}",
                        },
                    },
                },
            };

            var defaultPolicy = new PrismaPolicy()
            {
                Id = $"/NBA/policy/blackout",
                ViewingPolicy = new List<PrismaViewingPolicy>()
                {
                    new PrismaViewingPolicy()
                    {
                        Id = $"/NBA/viewingpolicy/event",
                        ActionContent = EsniMediaNames.BlackoutActionContent,
                        Audience = new PrismaAudience()
                        {
                            Id = $"/NBA/audience/event",
                            Match = PrismaAudienceMatch.ANY,
                        },
                    },
                },
            };

            var expectedMediaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = new List<PrismaMedia>
                {
                    new PrismaMedia
                    {
                        Id = $"/NBA/media/{expectedMediaId}",
                        MediaPoint = new List<PrismaMediaPoint>
                        {
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/start",
                                MatchTime = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointMatchTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Effective = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                Apply = new List<PrismaPolicyApply>
                                {
                                    new PrismaPolicyApply
                                    {
                                        Duration = policyDuration,
                                        Priority = priority,
                                        Policy = new PrismaPolicy
                                        {
                                            Id = $"/NBA/policy/{distributionPath}/{expectedMediaId}",
                                            ViewingPolicy = new List<PrismaViewingPolicy>()
                                            {
                                                distributionName == EsniMediaNames.DistributionRSN ? viewingPolicyLocal : viewingPolicyGlobalRegional,
                                            },
                                        },
                                    },
                                    new PrismaPolicyApply
                                    {
                                        Priority = 3,
                                        Policy = defaultPolicy,
                                    },
                                },
                            },
                            new PrismaMediaPoint
                            {
                                Id = $"/NBA/mediapoint/{expectedMediaId}/end",
                                MatchTime = game.DateTime.Value,
                                Effective = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = game.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                RelatedProductionId = expectedMediaId,
                                Remove = new List<PrismaPolicyRemove>(),
                            },
                        },
                        TeamContext = EsniMediaNames.Home,
                    },
                },
            };

            if (regionName.EqualsIgnoreCase(EsniMediaNames.RegionCanada))
            {
                var matchSignal = new PrismaPolicyMatchSignal()
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.First().MatchSignal = matchSignal;
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).MatchSignal = new PrismaPolicyMatchSignal
                {
                    Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]" },
                    Match = PrismaMatchSignalMatch.ANY,
                };
                expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = $"/NBA/policy/{distributionPath.ToLowerInvariant()}/{expectedMediaId}" } });
            }

            expectedMediaInfo.MediasToUpsert.First().MediaPoint.ElementAt(1).Remove.Add(new PrismaPolicyRemove { Policy = new PrismaPolicy { XlinkHref = "/NBA/policy/blackout" } });

            var gameLevelPrismaMedia = JsonConvert.DeserializeObject<PrismaMedia>(JsonConvert.SerializeObject(expectedMediaInfo.MediasToUpsert.First()));
            gameLevelPrismaMedia.Id = $"/NBA/media/{game.Id}";

            foreach (var mediaPoint in gameLevelPrismaMedia.MediaPoint)
            {
                mediaPoint.Id = mediaPoint.Id.Replace(expectedMediaId, game.Id, StringComparison.InvariantCultureIgnoreCase);
            }

            expectedMediaInfo.MediasToUpsert = expectedMediaInfo.MediasToUpsert.Append(gameLevelPrismaMedia);

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            result.Should().BeEquivalentTo(expectedMediaInfo);
        }

        /// <summary>
        /// GetPrismaInfo with world or regional blackout, adds game level PrismaMedia.
        /// </summary>
        /// <param name="regionName">The name of the region.</param>
        [Theory]
        [InlineData(EsniMediaNames.RegionCanada)]
        [InlineData(EsniMediaNames.RegionUnitedStates)]
        public void GetPrismaInfo_WithWorldOrRegionalBlackout_AddsGameLevelPrismaMedia(string regionName)
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = regionName;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(1);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal("/NBA/media/ggameidawayteamabbr0hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/ggameidawayteamabbr0hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with world and regional blackout and two NSS medias, adds game level PrismaMedia using as PrimaryNssMedia the first NSS media.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithWorldAndRegionalBlackoutAndTwoNssMedias_AddsGameLevelPrismaMediaUsingAsPrimaryNssMediaTheFirstNssMedia()
        {
            // Arrange
            var game = GetGameWithTwoNssAndTwoTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 1);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            var mediaTv2 = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 3);
            mediaTv2.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv2.Region.Name = EsniMediaNames.RegionUnitedStates;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(2);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal("/NBA/media/ggameidawayteamabbr0hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/ggameidawayteamabbr0hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with world and regional blackout and two NSS medias, adds game level PrismaMedia using as PrimaryNssMedia the one that has encoder.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithWorldAndRegionalBlackoutAndTwoNssMedias_AddsGameLevelPrismaMediaUsingAsPrimaryNssMediaTheOneThatHasEncoder()
        {
            // Arrange
            var game = GetGameWithTwoNssAndTwoTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 1);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            var mediaTv2 = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 3);
            mediaTv2.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv2.Region.Name = EsniMediaNames.RegionUnitedStates;
            var primaryNssMedia = game.Media.Single(x => x.IsActiveNssMediaWithActiveSchedules && x.Id == 2);
            primaryNssMedia.Schedules.Single().Operations.Encoder = "0";

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(1);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(2);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal($"/NBA/media/ggameidawayteamabbr{primaryNssMedia.Id}hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith($"/NBA/mediapoint/ggameidawayteamabbr{primaryNssMedia.Id}hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with world and regional blackout and two NSS medias, adds game level PrismaMedia using as PrimaryNssMedia the one that has NssPrimaryFeedKey.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithWorldAndRegionalBlackoutAndTwoNssMedias_AddsGameLevelPrismaMediaUsingAsPrimaryNssMediaTheOneThatHasNssPrimaryFeedKey()
        {
            // Arrange
            var game = GetGameWithTwoNssAndTwoTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 1);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            var mediaTv2 = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 3);
            mediaTv2.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv2.Region.Name = EsniMediaNames.RegionUnitedStates;
            var primaryNssMedia = game.Media.Single(x => x.IsActiveNssMediaWithActiveSchedules && x.Id == 2);
            primaryNssMedia.Schedules.Single().Operations.KeyValuePairs = new List<KeyValuePair> { new KeyValuePair { Key = NssPrimaryFeedKey } };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(1);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(2);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal($"/NBA/media/ggameidawayteamabbr{primaryNssMedia.Id}hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith($"/NBA/mediapoint/ggameidawayteamabbr{primaryNssMedia.Id}hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with world and local blackout and two NSS medias, adds game level PrismaMedia using as PrimaryNssMedia the first NSS media.
        /// </summary>
        /// <param name="distributionName">The name of the distribution.</param>
        /// <param name="teamContext">The team context.</param>
        [Theory]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Home)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Home)]
        public void GetPrismaInfo_WithWorldAndLocalBlackoutAndTwoNssMedias_AddsGameLevelPrismaMediaUsingAsPrimaryNssMediaTheFirstNssMedia(string distributionName, string teamContext)
        {
            // Arrange
            var game = GetGameWithTwoNssAndTwoTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 1);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            var mediaTv2 = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 3);
            mediaTv2.Distribution.Name = distributionName;
            mediaTv2.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv2.Schedules.Single().TeamContext = teamContext;
            game.Media.Single(x => x.IsNssMedia && x.Id == 2).Schedules.Single().TeamContext = teamContext;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(2);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal("/NBA/media/ggameidawayteamabbr0hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/ggameidawayteamabbr0hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with world or regional blackout, adds event level PrismaMedia.
        /// </summary>
        /// <param name="regionName">The name of the region.</param>
        [Theory]
        [InlineData(EsniMediaNames.RegionCanada)]
        [InlineData(EsniMediaNames.RegionUnitedStates)]
        public void GetPrismaInfo_WithWorldOrRegionalBlackout_AddsEventLevelPrismaMedia(string regionName)
        {
            // Arrange
            var gmsEvent = GetEventWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = gmsEvent.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = regionName;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(gmsEvent, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(1);
            Assert.Equal(gmsEvent.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal("/NBA/media/eeventide0", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/eventId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eeventide0/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eventId/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo with local blackouts, adds event level PrismaMedia.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithWorldOrRegionalBlackoutAndBlackoutsFromKvp_AddsEventLevelPrismaMedia()
        {
            // Arrange
            var gmsEvent = GetEventWithNssMedia();
            var nssMediaWithOta = GetNssMedia(1000);
            nssMediaWithOta.KeyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair { Key = NssBlackoutTeamOtaKey, Value = " AAA , BBB " },
            };
            gmsEvent.Media = gmsEvent.Media.Append(nssMediaWithOta);

            var nssMediaWithOtaAndRsn = GetNssMedia(2000);
            nssMediaWithOtaAndRsn.KeyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair { Key = NssBlackoutTeamOtaKey, Value = "AAA" },
                new KeyValuePair { Key = NssBlackoutTeamRsnKey, Value = "ZZZ , ZZZ" },
            };
            gmsEvent.Media = gmsEvent.Media.Append(nssMediaWithOtaAndRsn);

            var nssMediaToIgnore = GetNssMedia(3000);
            gmsEvent.Media = gmsEvent.Media.Append(nssMediaToIgnore);

            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsToIgnore = new long[] { 3000 };
            var nssBlackoutKeys = new string[] { NssBlackoutTeamRsnKey, NssBlackoutTeamOtaKey };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(gmsEvent, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            Assert.True(gmsEvent.IsRelevantForOrchestrationPlatform);
            Assert.True(gmsEvent.HasActiveNssMediasWithActiveSchedules);
            Assert.True(gmsEvent.IncludesBlackoutData(nssBlackoutKeys));
            Assert.Equal(4, result.MediasToUpsert.Count());

            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(3);
            Assert.Equal("/NBA/media/eventId", gameLevelPrismaMedia.Id);
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eventId/", StringComparison.OrdinalIgnoreCase)));

            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            Assert.Equal("/NBA/media/eeventide0", primaryPrismaMedia.Id);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eeventide0/", StringComparison.OrdinalIgnoreCase)));
            var primaryMediaPoint = primaryPrismaMedia.MediaPoint.First(x => x.Id == EsniExtensions.GetStartMediaPointId("eeventide0"));
            var primaryAudience = primaryMediaPoint.Apply.First(x => x.Priority == 0).Policy.ViewingPolicy.First(x => x.Id == EsniExtensions.GetEsniLocalViewingPolicyId("eeventide0")).Audience;
            Assert.Equal(EsniExtensions.GetEsniLocalAudienceId("eeventide0"), primaryAudience.Id);

            var otaPrismaMedia = result.MediasToUpsert.ElementAt(1);
            Assert.Equal("/NBA/media/eeventide1000", otaPrismaMedia.Id);
            Assert.True(otaPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eeventide1000/", StringComparison.OrdinalIgnoreCase)));
            var otaStartMediaPoint = otaPrismaMedia.MediaPoint.First(x => x.Id == EsniExtensions.GetStartMediaPointId("eeventide1000"));
            var otaAudience = otaStartMediaPoint.Apply.First(x => x.Priority == 0).Policy.ViewingPolicy.First(x => x.Id == EsniExtensions.GetEsniLocalViewingPolicyId("eeventide1000")).Audience;
            Assert.Equal(EsniExtensions.GetEsniLocalAudienceId("eeventide1000"), otaAudience.Id);

            var otaAndRsnPrismaMedia = result.MediasToUpsert.ElementAt(2);
            Assert.Equal("/NBA/media/eeventide2000", otaAndRsnPrismaMedia.Id);
            Assert.True(otaAndRsnPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/eeventide2000/", StringComparison.OrdinalIgnoreCase)));
            var otaAndRsnStartMediaPoint = otaAndRsnPrismaMedia.MediaPoint.First(x => x.Id == EsniExtensions.GetStartMediaPointId("eeventide2000"));
            var otaAndRsnAudience = otaAndRsnStartMediaPoint.Apply.First(x => x.Priority == 0).Policy.ViewingPolicy.First(x => x.Id == EsniExtensions.GetEsniLocalViewingPolicyId("eeventide2000")).Audience;
            Assert.Equal(EsniExtensions.GetEsniLocalAudienceId("eeventide2000"), otaAndRsnAudience.Id);

            var audiencesToAssert = new List<PrismaAudience> { primaryAudience, otaAudience, otaAndRsnAudience };
            foreach (var audience in audiencesToAssert)
            {
                Assert.Equal(3, audience.Audience.Count);
                Assert.Single(audience.Audience, x => x.XlinkHref == EsniExtensions.GetOtaLocalAudienceHref("aaa"));
                Assert.Single(audience.Audience, x => x.XlinkHref == EsniExtensions.GetOtaLocalAudienceHref("bbb"));
                Assert.Single(audience.Audience, x => x.XlinkHref == EsniExtensions.GetRsnLocalAudienceHref("zzz"));
            }
        }

        /// <summary>
        /// GetPrismaInfo with local blackout, adds game level PrismaMedia.
        /// </summary>
        /// <param name="distributionName">The name of the distribution.</param>
        /// <param name="teamContext">The team context.</param>
        [Theory]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Home)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Home)]
        public void GetPrismaInfo_WithLocalBlackout_AddsGameLevelPrismaMedia(string distributionName, string teamContext)
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = distributionName;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv.Schedules.Single().TeamContext = teamContext;
            game.Media.Single(x => x.IsNssMedia).Schedules.Single().TeamContext = teamContext;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(1);
            Assert.Equal(game.Media.Count(x => x.IsNssMedia) + GameLevelPrismaMediaCount, result.MediasToUpsert.Count());
            Assert.Equal("/NBA/media/ggameidawayteamabbr0hometeamabbr", primaryPrismaMedia.Id);
            Assert.Equal("/NBA/media/gameId", gameLevelPrismaMedia.Id);
            Assert.Equal(primaryPrismaMedia.MediaPoint.Count, gameLevelPrismaMedia.MediaPoint.Count);
            Assert.True(primaryPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/ggameidawayteamabbr0hometeamabbr/", StringComparison.OrdinalIgnoreCase)));
            Assert.True(gameLevelPrismaMedia.MediaPoint.All(x => x.Id.StartsWith("/NBA/mediapoint/gameid/", StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// GetPrismaInfo without blackouts returns null.
        /// </summary>
        /// <param name="gmsEntity">The GmsEntity.</param>
        [Theory]
#pragma warning disable CA1825 // Avoid zero-length array allocations
        [MemberData(nameof(NextGenExtensionsTestsData.GetPrismaInfo_WithoutBlackouts_ReturnsNull), MemberType = typeof(NextGenExtensionsTestsData))]
#pragma warning restore CA1825 // Avoid zero-length array allocations
        public void GetPrismaInfo_WithoutBlackouts_ReturnsNull(GmsEntity gmsEntity)
        {
            // Arrange
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.NssBlackoutTeamOtaKey = null;
            esniResourcesCreationOptions.NssBlackoutTeamRsnKey = null;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(gmsEntity, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            Assert.Null(result);
        }

        /// <summary>
        /// GetPrismaInfo ignores the blackout creation when medias ids are present in the configuration.
        /// </summary>
        /// <param name="game">The GmsGame.</param>
        [Theory]
#pragma warning disable CA1825 // Avoid zero-length array allocations
        [MemberData(nameof(NextGenExtensionsTestsData.GetPrismaInfo_MediaExclusion), MemberType = typeof(NextGenExtensionsTestsData))]
#pragma warning restore CA1825 // Avoid zero-length array allocations
        public void GetPrismaInfo_GetPrismaInfo_MediaExclusion_IgnoresMedia([NotNull] GmsGame game)
        {
            // Arrange
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();

            var mediasCountToCreate = game.Media.Count(x => x.IsActiveNssMediaWithActiveSchedules && !esniResourcesCreationOptions.MediaIdsToIgnore.Contains(x.Id));

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            if (result.MediasToUpsert.Any())
            {
                mediasCountToCreate++; // Extra Media.
            }

            // Assert
            result.MediasToUpsert.Count().Should().Be(mediasCountToCreate);
        }

        /// <summary>
        /// GetPrismaInfo with United States regional blackout, correctly copies primary media to game level PrismaMedia.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithUnitedStatesRegionalBlackout_CorrectlyCopiesPrimaryMediaToGameLevelPrismaMedia()
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, default);

            // Assert
            var primaryPrismaMedia = result.MediasToUpsert.ElementAt(0);
            var primaryMediaPoint = primaryPrismaMedia.MediaPoint.First();
            var primaryPolicyApply = primaryPrismaMedia.MediaPoint.First().Apply.First();
            var gameLevelPrismaMedia = result.MediasToUpsert.ElementAt(1);
            var gameLevelMediaPoint = gameLevelPrismaMedia.MediaPoint.First();
            var gameLevelyPolicyApply = gameLevelPrismaMedia.MediaPoint.First().Apply.First();

            Assert.Equal(primaryPrismaMedia.TeamContext, gameLevelPrismaMedia.TeamContext);
            Assert.NotEqual(primaryPrismaMedia.Id, gameLevelPrismaMedia.Id);

            Assert.Equal(primaryMediaPoint.Effective, gameLevelMediaPoint.Effective);
            Assert.Equal(primaryMediaPoint.Expires, gameLevelMediaPoint.Expires);
            Assert.Equal(primaryMediaPoint.MatchTime, gameLevelMediaPoint.MatchTime);
            Assert.Equal(primaryMediaPoint.RelatedProductionId, gameLevelMediaPoint.RelatedProductionId);
            Assert.NotEqual(primaryMediaPoint.Id, gameLevelMediaPoint.Id);

            Assert.Equal(primaryPolicyApply.Duration, gameLevelyPolicyApply.Duration);
            Assert.Equal(primaryPolicyApply.Policy.Id, gameLevelyPolicyApply.Policy.Id);
            Assert.Equal(primaryPolicyApply.Policy.ViewingPolicy.Single().ActionContent, gameLevelyPolicyApply.Policy.ViewingPolicy.Single().ActionContent);
            Assert.Equal(primaryPolicyApply.Policy.ViewingPolicy.Single().Audience.Id, gameLevelyPolicyApply.Policy.ViewingPolicy.Single().Audience.Id);
            Assert.Equal(primaryPolicyApply.Policy.ViewingPolicy.Single().Audience.ISO3166CountryCodes, gameLevelyPolicyApply.Policy.ViewingPolicy.Single().Audience.ISO3166CountryCodes);
            Assert.Equal(primaryPolicyApply.Policy.ViewingPolicy.Single().Audience.Match, gameLevelyPolicyApply.Policy.ViewingPolicy.Single().Audience.Match);
            Assert.Equal(primaryPolicyApply.Policy.ViewingPolicy.Single().Id, gameLevelyPolicyApply.Policy.ViewingPolicy.Single().Id);
            Assert.Equal(primaryPolicyApply.Priority, gameLevelyPolicyApply.Priority);
            Assert.Equal(primaryPolicyApply.Priority, gameLevelyPolicyApply.Priority);
        }

        /// <summary>
        /// UpdateEsniIdsForGameUpdate with proper parameter for game should pass.
        /// </summary>
        [Fact]
        public void UpdateEsniIdsForGameUpdate_WithProperParametersForNewGame_ShouldPass()
        {
            var prismaInfo = new PrismaMediaInfo()
            {
                MediasToUpsert = new List<PrismaMedia>()
                {
                    this.GetMockPrismaMedia("id1"),
                },
            };

            // Act
            NextGenExtensions.UpdateEsniIdsForGameUpdate(prismaInfo, null);

            // Assert
            prismaInfo.EsniResourceIdsToDelete.Should().BeEquivalentTo(null);
        }

        /// <summary>
        /// UpdateEsniIdsForGameUpdate with proper parameter for existing game should pass.
        /// </summary>
        [Fact]
        public void UpdateEsniIdsForGameUpdate_WithProperParametersForExistingGame_ShouldPass()
        {
            var prismaInfo = new PrismaMediaInfo()
            {
                MediasToUpsert = new List<PrismaMedia>()
                {
                    this.GetMockPrismaMedia("id1"),
                },
            };

            VideoPlatformSchedule videoPlatformSchedule = this.GetMockVideoPlatformSchedule(NbaWorkflowIds.EventMetadataSetup, ActorIds.PrismaMedias);
            var intent = videoPlatformSchedule.WorkflowIntents.First(x => x.WorkflowId == NbaWorkflowIds.EventMetadataSetup);
            var prismaDetails = intent.VideoPlatformActorSpecificDetails.First(x => x.ActorId == ActorIds.PrismaMedias);
            prismaDetails.Data = new PrismaMediaInfo
            {
                EsniResourceIdsToDelete = prismaInfo.GetEsniResourcesIdsForCleanup().EsniResourceIds,
            };

            var updatedPrismaInfo = new PrismaMediaInfo()
            {
                MediasToUpsert = new List<PrismaMedia>()
                {
                    this.GetMockPrismaMedia("id2"),
                },
            };

            // Act
            NextGenExtensions.UpdateEsniIdsForGameUpdate(updatedPrismaInfo, videoPlatformSchedule);

            // Assert
            updatedPrismaInfo.EsniResourceIdsToDelete.Should().BeEquivalentTo(((PrismaMediaInfo)prismaDetails.Data).EsniResourceIdsToDelete);
        }

        /// <summary>
        /// <see cref="NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs"/> should return key from schedule if KVP is present in both media and schedule.
        /// </summary>
        [Fact]
        public void GetValueFromScheduleOrMediaKeyValuePairs_WhenKvpExistsInScheduleAndMedia_ShouldReturnValueFromSchedule()
        {
            // Arrange
            string key = "SomeKey";
            string value = "SomeValue";
            var schedule = new Schedule
            {
                Operations = new Operation
                {
                    KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = key,
                            Value = value,
                        },
                    },
                },
            };
            var media = new MediaInfo()
            {
                KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = key,
                            Value = "SomeVeryDifferentValue",
                        },
                    },
            };

            // Act
            var actualValue = NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs(schedule, key, media);

            // Assert
            Assert.Equal(value, actualValue);
        }

        /// <summary>
        /// <see cref="NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs"/> should return key from media if KVP is present only in media.
        /// </summary>
        [Fact]
        public void GetValueFromScheduleOrMediaKeyValuePairs_WhenKvpExistsOnlyInMedia_ShouldReturnValueFromMedia()
        {
            // Arrange
            string key = "SomeKey";
            string value = "SomeValue";
            var schedule = new Schedule
            {
                Operations = new Operation
                {
                    KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = "SomeVeryDifferentKey",
                            Value = "SomeVeryDifferentValue",
                        },
                    },
                },
            };
            var media = new MediaInfo()
            {
                KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = key,
                            Value = value,
                        },
                    },
            };

            // Act
            var actualValue = NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs(schedule, key, media);

            // Assert
            Assert.Equal(value, actualValue);
        }

        /// <summary>
        /// <see cref="NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs"/> should return null if KVP is not present in neither media nor schedule.
        /// </summary>
        [Fact]
        public void GetValueFromScheduleOrMediaKeyValuePairs_WhenKvpDoesNotExist_ShouldReturnNull()
        {
            // Arrange
            string key = "SomeKey";
            var schedule = new Schedule
            {
                Operations = new Operation
                {
                    KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = "SomeVeryDifferentKey",
                            Value = "SomeVeryDifferentValue",
                        },
                    },
                },
            };
            var media = new MediaInfo()
            {
                KeyValuePairs = new List<KeyValuePair>
                    {
                        new KeyValuePair
                        {
                            Key = "JustAnotherVeryDifferentKey",
                            Value = "JustAnotherVeryDifferentValue",
                        },
                    },
            };

            // Act
            var actualValue = NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs(schedule, key, media);

            // Assert
            Assert.Null(actualValue);
        }

        /// <summary>
        /// <see cref="NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs"/> should return null if KVP is the KVP collections are null.
        /// </summary>
        [Fact]
        public void GetValueFromScheduleOrMediaKeyValuePairs_WhenKvpAreNull_ShouldReturnNull()
        {
            // Arrange
            string key = "SomeKey";
            var schedule = new Schedule
            {
                Operations = new Operation(),
            };
            var media = new MediaInfo();

            // Act
            var actualValue = NextGenExtensions.GetValueFromScheduleOrMediaKeyValuePairs(schedule, key, media);

            // Assert
            Assert.Null(actualValue);
        }

        /// <summary>
        /// Test for <see cref="NextGenExtensions.GetLiveEventIdFromChannelId(string)"/>.
        /// </summary>
        [Fact]
        public void GetLiveEventIdFromChannelId_WhenInvoked_ShouldReturnCorrectValue()
        {
            // Arrange
            var productionId = "g9942174785tor751bos";

            // Act
            var result = NextGenExtensions.GetLiveEventIdFromChannelId(productionId);

            // Assert
            Assert.Equal("9942174785", result);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and a single media of Type TV and Region US (regional blackouts), but one of the NSS medias is configured to exempt regional blackouts.
        /// The expectation is that there are no ESNI Media to be created.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithMediaIdWithoutRegionalBlackouts_ShouldReturnMediaWithEmptyMediaPoints()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsWithoutRegionalBlackouts = new List<long>
            {
                firstNssMediaId,
            };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, new TimeSpan(0, 0, 0));

            // Assert
            Assert.Empty(result.MediasToUpsert);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and a single media of Type TV and Region CA (world blackouts), but one of the NSS medias is configured to exempt world blackouts.
        /// The expectation is that there are no ESNI Media to be created.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithMediaIdWithoutWorldBlackouts_ShouldReturnMediaWithEmptyMediaPoints()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetCanadaTvMedia(mediaTvId);
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsWithoutWorldBlackouts = new List<long>
            {
                firstNssMediaId,
            };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, new TimeSpan(0, 0, 0));

            // Assert
            Assert.Empty(result.MediasToUpsert);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and a single media of Type TV, Region US and Distribution RSN (local blackouts), but one of the NSS medias is configured to exempt local blackouts.
        /// The expectation is that there are no ESNI Media to be created.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithMediaIdWithoutLocalBlackouts_ShouldReturnMediaWithEmptyMediaPoints()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRSN;
            mediaTv.GetPreferredSchedule().TeamContext = EsniMediaNames.Home;
            game.Media = game.Media.Append(mediaTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.MediaIdsWithoutLocalBlackouts = new List<long>
            {
                firstNssMediaId,
            };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, new TimeSpan(0, 0, 0));

            // Assert
            Assert.Empty(result.MediasToUpsert);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and a single media of Type TV and Region CA (world blackouts), but that media doesn't support World blackouts.
        /// The expectation is that there are no ESNI Media to be created.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithNonNssMediaIdWithoutWorldBlackouts_ShouldReturnNoWorldBlackouts()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvId,
            };
            esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts = new List<long>();

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, new TimeSpan(0, 0, 0));

            // Assert
            Assert.Empty(result.MediasToUpsert);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and 2 medias of Type TV and Region CA (world blackouts)
        /// The expectation is that there are ESNI Media to be created.
        /// </summary>
        [Fact]
        public void GetPrismaInfo_WithNonNssMediaIdWithoutWorldBlackouts_ShouldReturnWorldBlackouts()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var mediaTvIdTSN = 556;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            var mediaTsnTv = GetTvMedia(mediaTvIdTSN);
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTsnTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTsnTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            game.Media = game.Media.Append(mediaTsnTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvId,
            };
            esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvIdTSN,
            };

            // Act
            var result = NextGenExtensions.GetPrismaInfo(game, esniResourcesCreationOptions, NssPrimaryFeedKey, new TimeSpan(0, 0, 0));

            // Assert
            Assert.NotEmpty(result.MediasToUpsert);
        }

        /// <summary>
        /// Test to check whether the Game's Media Id is being correctly extracted from the ChannelID.
        /// </summary>
        /// <param name="channelId">The channel Id.</param>
        /// <param name="expectedMediaId">The expected mediaId based on the channelId.</param>
        [Theory]
        [InlineData("g0022271048atl1000245bos", "1000245")]
        [InlineData("g0022271048atl1000246bos", "1000246")]
        [InlineData("g0022271048atl1003245bos", "1003245")]
        [InlineData("g0022271048atl1020245bos", "1020245")]
        [InlineData("e0022390210e1000431", "1000431")]
        [InlineData("e0022390210e1000123", "1000123")]
        [InlineData("e0022390210e1000111", "1000111")]
        public void GetGameMediaIdFromChannelId_WhenGivenAChannelIdInTheCorrectFormat_ShouldReturnTheMediaId(string channelId, string expectedMediaId)
        {
            // Arrange
            // Act
            var result = NextGenExtensions.GetGameMediaIdFromChannelId(channelId);

            // Assert.
            Assert.Equal(expectedMediaId, result);
        }

        /// <summary>
        /// GetblackoutServiceInfo with proper parameter for event should pass.
        /// </summary>
        /// <param name="distributionName">The channel Id.</param>
        /// <param name="teamContext">The expected mediaId based on the channelId.</param>
        [Theory]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Home)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Home)]
        public void GetBlackoutInformation_Ok(string distributionName, string teamContext)
        {
            // Arrange
            var game = GetGameWithTwoNssAndTwoTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 1);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            var mediaTv2 = game.Media.Single(x => x.IsMediaTypeTV && x.Id == 3);
            mediaTv2.Distribution.Name = distributionName;
            mediaTv2.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv2.Schedules.Single().TeamContext = teamContext;
            game.Media.Single(x => x.IsNssMedia && x.Id == 2).Schedules.Single().TeamContext = teamContext;

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.NotEmpty(newBlackout);
            Assert.Empty(updateBlackout);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and 2 medias of Type TV and Region CA (world blackouts)
        /// The expectation is that there no wlobalblackout.
        /// </summary>
        [Fact]
        public void GetBlackoutInfo_WithNonNssMediaIdWithoutWorldBlackouts_ShouldReturnWorldBlackouts()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var mediaTvIdTSN = 556;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            var mediaTsnTv = GetTvMedia(mediaTvIdTSN);
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTsnTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTsnTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            game.Media = game.Media.Append(mediaTsnTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvId,
            };
            esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvIdTSN,
            };

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.Equal(2, newBlackout.Count);
            Assert.Empty(updateBlackout);
        }

        /// <summary>
        /// Test for the use case of a game with two NSS medias and a single media of Type TV and Region CA (world blackouts), but that media doesn't support World blackouts.
        /// The expectation is that there are no blackouts.
        /// </summary>
        [Fact]
        public void GetBlackoutInfo_WithNonNssMediaIdWithoutWorldBlackouts_ShouldReturnNoWorldBlackouts()
        {
            // Arrange
            var firstNssMediaId = 444;
            var secondNssMediaId = 445;
            var mediaTvId = 555;
            var game = GetGameWithTwoNssMedia();
            game.Media.First().Id = firstNssMediaId;
            game.Media.Last().Id = secondNssMediaId;
            var mediaTv = GetTvMedia(mediaTvId);
            mediaTv.Region.Name = EsniMediaNames.RegionCanada;
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            game.Media = game.Media.Append(mediaTv);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts = new List<long>
            {
                mediaTvId,
            };
            esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts = new List<long>();

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.Empty(newBlackout);
            Assert.Empty(updateBlackout);
        }

          /// <summary>
        /// GetPrismaInfo with local blackout, adds game level PrismaMedia.
        /// </summary>
        /// <param name="distributionName">The name of the distribution.</param>
        /// <param name="teamContext">The team context.</param>
        [Theory]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Away)]
        [InlineData(EsniMediaNames.DistributionRSN, EsniMediaNames.Home)]
        [InlineData(EsniMediaNames.DistributionOTA, EsniMediaNames.Home)]
        public void GetBlackoutInfo_WithLocalBlackout(string distributionName, string teamContext)
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = distributionName;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;
            mediaTv.Schedules.Single().TeamContext = teamContext;
            game.Media.Single(x => x.IsNssMedia).Schedules.Single().TeamContext = teamContext;

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.Single(newBlackout);
            Assert.Empty(updateBlackout);
        }

        /// <summary>
        /// GetBlackout with world or regional blackout.
        /// </summary>
        [Fact]
        public void GetBlackout_WithRegionalBlackout()
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.Empty(updateBlackout);
            Assert.Single(newBlackout);
        }

        /// <summary>
        /// GetBlackout with world or regional blackout.
        /// </summary>
        [Fact]
        public void UpdateBlackout_WithRegionalBlackout()
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia();
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;

            var currentBlackout = new Collection<string>()
            {
                "ggameidawayteamabbr0hometeamabbr.us",
            };

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            // Assert
            Assert.Single(updateBlackout);
            Assert.Empty(newBlackout);
        }

        /// <summary>
        /// GetBlackout with world or regional blackout summer league.
        /// </summary>
        [Fact]
        public void GetBlackout_SummerLeague_CorrectExpirationTime()
        {
            // Arrange
            var game = GetGameWithNssAndTvMedia(0, 1, true);
            var esniResourcesCreationOptions = GetEsniResourcesCreationOptions();
            var mediaTv = game.Media.Single(x => x.IsMediaTypeTV);
            mediaTv.Distribution.Name = EsniMediaNames.DistributionRegional;
            mediaTv.Region.Name = EsniMediaNames.RegionUnitedStates;

            var currentBlackout = new Collection<string>();

            // Act
            var (newBlackout, updateBlackout) = NextGenExtensions.GetBlackoutServiceInfo(game, esniResourcesCreationOptions, currentBlackout);

            var expectedDateTime = new DateTimeOffset(new DateTime(2025, 07, 20));

            // Assert
            Assert.Empty(updateBlackout);
            Assert.Equal(expectedDateTime, newBlackout.First().EndTimeUtc.DateTime);
        }

        /// <summary>
        /// Get a teamzips collection.
        /// </summary>
        /// <returns>A new Team zips collection.</returns>
        /// <param name="awayTeamAbbr">the away team Abbr.</param>
        /// <param name="homeTeamAbbr">the home team Abbr.</param>
        private static Collection<GmsTeamZips> GetGmsTeamZipsRsnAndOta(string awayTeamAbbr, string homeTeamAbbr)
        {
            return new Collection<GmsTeamZips>()
            {
                new GmsTeamZips()
                {
                    TeamId = "1234",
                    Abbr = awayTeamAbbr,
                    Markets = new List<Market>()
                    {
                        new Market()
                        {
                            MarketCode = "OTA",
                            Zips = new List<string>()
                            {
                                "98123",
                                "98122",
                                "98121",
                            },
                        },
                        new Market()
                        {
                            MarketCode = "RSN",
                            Zips = new List<string>()
                            {
                                "98123",
                                "98122",
                                "98121",
                            },
                        },
                    },
                },
                new GmsTeamZips()
                {
                    TeamId = "1234",
                    Abbr = homeTeamAbbr,
                    Markets = new List<Market>()
                    {
                        new Market()
                        {
                            MarketCode = "OTA",
                            Zips = new List<string>()
                            {
                                "98123",
                                "98122",
                                "98121",
                            },
                        },
                        new Market()
                        {
                            MarketCode = "RSN",
                            Zips = new List<string>()
                            {
                                "98123",
                                "98122",
                                "98121",
                            },
                        },
                    },
                },
            };
        }

        /// <summary>
        /// Gets an EsniResourcesCreationOptions.
        /// </summary>
        /// <returns>An EsniResourcesCreationOptions.</returns>
        private static EsniResourcesCreationOptions GetEsniResourcesCreationOptions()
        {
            return new EsniResourcesCreationOptions
            {
                LocalPolicyDuration = "PT75H",
                RegionalPolicyDuration = "PT6H",
                WorldPolicyDuration = "PT3H",
                MediaPointEffectiveTimeOffset = new TimeSpan(-1, 0, 0),
                MediaPointExpiresTimeOffset = new TimeSpan(1, 0, 0),
                MediaPointMatchTimeOffset = new TimeSpan(0, -1, 0),
                MediaIdsToIgnore = new[] { 123L },
                NssBlackoutTeamSeparator = ",",
                NssBlackoutTeamOtaKey = NssBlackoutTeamOtaKey,
                NssBlackoutTeamRsnKey = NssBlackoutTeamRsnKey,
                NssMediaMediaPointPrismaPolicy = "/NBA/policy/blackout",
                NssMediaPointGameStartMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/game']",
                NssMediaPointGameEndMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/gameend']]",
                NssMediaPreGameMediaPointPrismaPolicy = "/NBA/policy/pregame",
                NssMediaPointPreGameStartMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/pregame']",
                NssMediaPointPreGameEndMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/pregameend']]",
                NssMediaPostGameMediaPointPrismaPolicy = "/NBA/policy/postgame",
                NssMediaPointPostGameStartMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/postgame']",
                NssMediaPointPostGameEndMatchSignal = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/postgameend']",
                NssMediaPointStartSegmentationTypeId = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]",
                NssMediaPointEndSegmentationTypeId = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]",
                NssMediaPointPreGameStartMatchTimeInMinutes = -15,
                NssMediaPointPreGameEndMatchTimeOffsetInMinutes = -1,
                NssMediaPointPostGameEndMatchTimeInMinutes = 60,
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };
        }

        /// <summary>
        /// Gets an instance populated of <see cref="TvpEventCreationOptions"/>.
        /// </summary>
        /// <returns>An instance populated of <see cref="TvpEventCreationOptions"/>.</returns>
        private static TvpEventCreationOptions GetTvpEventCreationOptions()
        {
            return new TvpEventCreationOptions
            {
                LanguageKey = "LanguageKey",
                NbaTvMediaName = "NbaTvMediaName",
                MediaScheduleKVPToLabelMapping = new List<KeyValuePairToLabelMapping>
                {
                    new KeyValuePairToLabelMapping
                    {
                        KeyValuePairKey = "NssLabelKey",
                        TvpProductionLabelName = "LabelTagKey",
                    },
                    new KeyValuePairToLabelMapping
                    {
                        KeyValuePairKey = "ctype",
                        TvpProductionLabelName = "ctype",
                    },
                    new KeyValuePairToLabelMapping
                    {
                        KeyValuePairKey = "ads",
                        TvpProductionLabelName = "ads",
                    },
                    new KeyValuePairToLabelMapping
                    {
                        KeyValuePairKey = "start",
                        TvpProductionLabelName = "LIVE",
                    },
                    new KeyValuePairToLabelMapping
                    {
                        KeyValuePairKey = "end",
                        TvpProductionLabelName = "END",
                    },
                },
                HasInBandScte35Key = "NSS-SCTE-Available",
                HasInBandScte35DefaultValue = false,
                AdInsertionPlaybackRestriction = "adinsertion",
                GamePackageParentPackagesKey = "NSS-Parent-Packages",
                ParentPackagesSeparator = ",",
                NssGeoAllow = NssGeoAllowKey,
                NssGeoDeny = NssGeoBlockKey,
                GeoPolicyDeliminator = ",",
                TimeSpanToSubstractToStartTime = TimeSpan.Parse("00:01:00", CultureInfo.InvariantCulture),
            };
        }

        /// <summary>
        /// Gets an active NSS media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>An active NSS media with active schedule.</returns>
        private static MediaInfo GetNssMedia(int mediaId = 0)
        {
            return new MediaInfo
            {
                Active = true,
                Id = mediaId,
                MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Operations = new Operation(),
                    },
                },
            };
        }

        /// <summary>
        /// Gets an active TV media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>An active TV media with active schedule.</returns>
        private static MediaInfo GetTvMedia(int mediaId = 0)
        {
            return new MediaInfo
            {
                Active = true,
                Distribution = new Distribution(),
                Id = mediaId,
                MediaType = new MediaType { Name = EsniMediaNames.MediaTV },
                Region = new Region(),
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Operations = new Operation(),
                    },
                },
            };
        }

        /// <summary>
        /// Gets an active TV Canada Region media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>An active TV media with active schedule.</returns>
        private static MediaInfo GetCanadaTvMedia(int mediaId = 0)
        {
            return new MediaInfo
            {
                Active = true,
                Distribution = new Distribution(),
                Id = mediaId,
                MediaType = new MediaType { Name = EsniMediaNames.MediaTV },
                Region = new Region()
                {
                    Id = 2,
                    Name = EsniMediaNames.RegionCanada,
                },
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Operations = new Operation(),
                    },
                },
            };
        }

        /// <summary>
        /// Gets a game relevant for orchestration with an active NSS media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>A game relevant for orchestration with an active NSS media with active schedule.</returns>
        private static GmsGame GetGameWithNssMedia(int mediaId = 0)
        {
            return new GmsGame
            {
                Id = "gameId",
                Active = true,
                DateTime = DateTimeOffset.MinValue.AddYears(2000),
                Location = new Location(),
                HomeTeam = new Team { Abbr = "HomeTeamAbbr" },
                AwayTeam = new Team { Abbr = "AwayTeamAbbr" },
                Media = new List<MediaInfo>
                {
                    GetNssMedia(mediaId),
                },
            };
        }

        /// <summary>
        /// Gets a game relevant for orchestration with an active NSS media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>A game relevant for orchestration with an active NSS media with active schedule.</returns>
        private static GmsGame GetSummerLeagueGameWithNssMedia(int mediaId = 0)
        {
            return new GmsGame
            {
                Id = "1322400001",
                Active = true,
                DateTime = new DateTimeOffset(new DateTime(2024, 07, 11, 13, 30, 00)),
                Location = new Location(),
                HomeTeam = new Team { Abbr = "HomeTeamAbbr" },
                AwayTeam = new Team { Abbr = "AwayTeamAbbr" },
                Media = new List<MediaInfo>
                {
                    GetNssMedia(mediaId),
                },
            };
        }

        /// <summary>
        /// Gets a game relevant for orchestration with two active NSS media with active schedule.
        /// </summary>
        /// <returns>A game relevant for orchestration with two active NSS media with active schedule.</returns>
        private static GmsGame GetGameWithTwoNssMedia()
        {
            var game = GetGameWithNssMedia();
            game.Media = game.Media.Append(GetNssMedia(1));

            return game;
        }

        /// <summary>
        /// Gets a game relevant for orchestration with an active NSS media.
        /// </summary>
        /// <param name="nssMediaId">The NSS media identifier.</param>
        /// <param name="mediaTvId">The tv media identifier.</param>
        /// <param name="summerLeague">The Summer League Id is needed.</param>
        /// <returns>A game relevant for orchestration with an active NSS media.</returns>
        private static GmsGame GetGameWithNssAndTvMedia(int nssMediaId = 0, int mediaTvId = 1, bool summerLeague = false)
        {
            var game = summerLeague ? GetSummerLeagueGameWithNssMedia(nssMediaId) : GetGameWithNssMedia(nssMediaId);

            game.Media = game.Media.Append(GetTvMedia(mediaTvId));

            return game;
        }

        /// <summary>
        /// Gets a game relevant for orchestration with two active NSS media with active schedule and a Tv media.
        /// </summary>
        /// <returns>A game relevant for orchestration with two active NSS media with active schedule and a Tv media.</returns>
        private static GmsGame GetGameWithTwoNssAndTwoTvMedia()
        {
            var game = GetGameWithNssAndTvMedia();
            game.Media = game.Media.Append(GetNssMedia(2)).Append(GetTvMedia(3));

            return game;
        }

        /// <summary>
        /// Gets an event relevant for orchestration with an active NSS media with active schedule.
        /// </summary>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>An event relevant for orchestration an active NSS media with active schedule.</returns>
        private static GmsEvent GetEventWithNssMedia(int mediaId = 0)
        {
            return new GmsEvent
            {
                Id = "eventId",
                IsEvent = true,
                Active = true,
                DateTime = DateTimeOffset.MinValue.AddYears(2000),
                Location = new Location(),
                GameId = "0023000000",
                Media = new List<MediaInfo>
                {
                    GetNssMedia(mediaId),
                },
            };
        }

        /// <summary>
        /// Gets an event relevant for orchestration with a Tv media and an active NSS media with active schedule.
        /// </summary>
        /// <returns>An event relevant for orchestration with a Tv media and an active NSS media with active schedule.</returns>
        private static GmsEvent GetEventWithNssAndTvMedia()
        {
            var gmsEvent = GetEventWithNssMedia();
            gmsEvent.Media = gmsEvent.Media.Append(GetTvMedia(1));

            return gmsEvent;
        }

        /// <summary>
        /// Gets the PostGame mediapoints.
        /// </summary>
        /// <param name="validGmsEvent">A Valid GMS Event.</param>
        /// <param name="esniResourcesCreationOptions">The esniResourcesCreationOptions.</param>
        /// <param name="gameDuration">The Game duration.</param>
        /// <param name="expectedProductionId">The expected productionId.</param>
        /// <returns>The post game media points.</returns>
        private static List<PrismaMediaPoint> GetPostGameMediaPoints(GmsEntity validGmsEvent, EsniResourcesCreationOptions esniResourcesCreationOptions, TimeSpan gameDuration, string expectedProductionId)
        {
            return new List<PrismaMediaPoint>()
            {
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetPostGameStartMediaPointId(expectedProductionId),
                    RelatedProductionId = expectedProductionId,
                    MatchTime = validGmsEvent.DateTime.Value.Add(gameDuration),
                    Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = NextGenExtensions.GetPostGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                    Expires = validGmsEvent.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPostGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetPostGameEndMediaPointId(expectedProductionId),
                    MatchTime = validGmsEvent.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes),
                    Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = NextGenExtensions.GetPostGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                    Expires = validGmsEvent.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    RelatedProductionId = expectedProductionId,
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPostGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
            };
        }

        /// <summary>
        /// Gets the PreGame mediapoints.
        /// </summary>
        /// <param name="validGmsEvent">A Valid GMS Event.</param>
        /// <param name="esniResourcesCreationOptions">The esniResourcesCreationOptions.</param>
        /// <param name="expectedProductionId">The expected productionId.</param>
        /// <returns>The post game media points.</returns>
        private static List<PrismaMediaPoint> GetPreGameMediaPoints(GmsEntity validGmsEvent, EsniResourcesCreationOptions esniResourcesCreationOptions, string expectedProductionId)
        {
            return new List<PrismaMediaPoint>()
            {
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetPreGameStartMediaPointId(expectedProductionId),
                    RelatedProductionId = expectedProductionId,
                    MatchTime = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes),
                    Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = NextGenExtensions.GetPreGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                    Expires = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPreGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetPreGameEndMediaPointId(expectedProductionId),
                    MatchTime = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes),
                    Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = NextGenExtensions.GetPreGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                    Expires = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    RelatedProductionId = expectedProductionId,
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPreGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
            };
        }

        /// <summary>
        /// Gets the Game mediapoints.
        /// </summary>
        /// <param name="validGmsEvent">A Valid GMS Event.</param>
        /// <param name="esniResourcesCreationOptions">The esniResourcesCreationOptions.</param>
        /// <param name="gameDuration">The Game duration.</param>
        /// <param name="expectedProductionId">The expected productionId.</param>
        /// <returns>The post game media points.</returns>
        private static List<PrismaMediaPoint> GetGameMediaPoints(GmsEntity validGmsEvent, EsniResourcesCreationOptions esniResourcesCreationOptions, TimeSpan gameDuration, string expectedProductionId)
        {
            return new List<PrismaMediaPoint>()
            {
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetGameStartMediaPointId(expectedProductionId),
                    RelatedProductionId = expectedProductionId,
                    MatchTime = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                    Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = NextGenExtensions.GetGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                    Expires = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
                new PrismaMediaPoint
                {
                    Id = EsniExtensions.GetGameEndMediaPointId(expectedProductionId),
                    MatchTime = validGmsEvent.DateTime.Value.Add(gameDuration),
                    Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = NextGenExtensions.GetGamePolicy() } },
                    Effective = validGmsEvent.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                    Expires = validGmsEvent.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                    RelatedProductionId = expectedProductionId,
                    MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                },
            };
        }

        /// <summary>
        /// Gets a Schedule with the given workflowId and Actor ID.
        /// </summary>
        /// <param name="workflowId">WorkflowId.</param>
        /// <param name="actorId">ActorId.</param>
        /// <returns>VideoPlatform Schedule.</returns>
        private VideoPlatformSchedule GetMockVideoPlatformSchedule(string workflowId, string actorId)
        {
            return new VideoPlatformSchedule
            {
                WorkflowIntents = new[]
                 {
                    new VideoPlatformWorkflowIntent
                    {
                        WorkflowId = workflowId,
                        VideoPlatformActorSpecificDetails = new VideoPlatformActorSpecificDetail[]
                        {
                            new VideoPlatformActorSpecificDetail
                            {
                                ActorId = actorId,
                            },
                        },
                    },
                 },
            };
        }

        /// <summary>
        /// Get Mock Prisma Media Data.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Prisma Media.</returns>
        private PrismaMedia GetMockPrismaMedia(string id)
        {
            return new PrismaMedia()
            {
                Id = $"/NBA/media/{id}",
                MediaPoint = new List<PrismaMediaPoint>()
                {
                    new PrismaMediaPoint()
                    {
                        Id = $"/NBA/mediapoint/{id}",
                        MatchTime = DateTimeOffset.UtcNow,
                        RelatedProductionId = id,
                        Apply = new List<PrismaPolicyApply>()
                        {
                            new PrismaPolicyApply()
                            {
                                Duration = "PT75H",
                                Policy = new PrismaPolicy()
                                {
                                    Id = $"/NBA/policy/local/{id}",
                                    ViewingPolicy = new List<PrismaViewingPolicy>()
                                    {
                                        new PrismaViewingPolicy()
                                        {
                                            Id = $"/NBA/viewingpolicy/{id}",
                                            ActionContent = EsniMediaNames.BlackoutActionContent,
                                            Audience = new PrismaAudience()
                                            {
                                                Id = $"/NBA/audience/{id}",
                                                Match = PrismaAudienceMatch.ANY,
                                                Audience = new List<PrismaAudience>()
                                                {
                                                    new PrismaAudience()
                                                    {
                                                        Match = PrismaAudienceMatch.ANY,
                                                        XlinkHref = $"/NBA/audience/{id}",
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };
        }

        /// <summary>
        /// Get Mock Gms Game Data.
        /// </summary>
        /// <returns>Gms Game.</returns>
        private GmsGame GetMockGmsGameData()
        {
            // Arrange
            var operationsKeyValue = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = NssPrimaryFeedKey,
                    Value = "true",
                },
                new KeyValuePair
                {
                    Key = "NSS-Production-Manifest-Offset",
                    Value = "-90",
                },
            };

            var schedules0 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 10,
                   Active = false,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
            };

            var schedules1 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 123456,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "1500",
                   },
                },
                new Schedule
                {
                   Id = 1234567,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "1501",
                   },
                },
                new Schedule
                {
                   Id = 1234568,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
            };

            var schedules2 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 1231,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
                new Schedule
                {
                   Id = 1232,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
                new Schedule
                {
                   Id = 1233,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "1200",
                       KeyValuePairs = operationsKeyValue,
                   },
                },
            };
            var schedules3 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 12341,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
                new Schedule
                {
                   Id = 12342,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
                new Schedule
                {
                   Id = 12343,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
            };

            var keyValuePairs = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = "ILP-Akamai Broadcast Code",
                    Value = "sap",
                },
                new KeyValuePair
                {
                   Key = NssGeoAllowKey,
                   Value = "Seattle,Arizona",
                },
                new KeyValuePair
                {
                   Key = NssGeoBlockKey,
                   Value = "Tennessee,Lakers",
                },
                new KeyValuePair
                {
                   Key = NssGeoredundantRegionsKey,
                   Value = "eastus , centralus",
                },
                new KeyValuePair
                {
                   Key = CustomOffsetKeyStart,
                   Value = "-5",
                },
                new KeyValuePair
                {
                   Key = CustomOffsetKeyOver,
                   Value = "30",
                },
            };

            var medias = new List<MediaInfo>
            {
                new MediaInfo
                {
                    Id = 0,
                    Name = "MTV-NextGen-Test0",
                    Schedules = schedules0,
                    Active = true,
                    MediaType = new MediaType { Name = "NotNss" },
                    KeyValuePairs = keyValuePairs,
                },
                new MediaInfo
                {
                    Id = 1,
                    Name = "MTV-NextGen-Test1",
                    Schedules = schedules1,
                    Active = true,
                    MediaType = new MediaType { Name = "nss" },
                    KeyValuePairs = keyValuePairs,
                },
                new MediaInfo
                {
                    Id = 2,
                    Name = "MTV-NextGen-Test2",
                    Schedules = schedules2,
                    Active = true,
                    MediaType = new MediaType { Name = "nss" },
                    KeyValuePairs = keyValuePairs,
                    Resolution = "1080p",
                },
                new MediaInfo
                {
                    Id = 3,
                    Name = "MTV-NextGen-Test3",
                    Schedules = schedules3,
                    Active = true,
                    MediaType = new MediaType { Name = "nss" },
                    KeyValuePairs = keyValuePairs,
                },
            };

            return new GmsGame()
            {
                Id = "Test",
                DateTime = new DateTimeOffset(2021, 06, 24, 21, 00, 00, new TimeSpan(0, 0, 0)),
                Active = true,
                Media = medias,
                HomeTeam = new Team { Abbr = "BOS", Id = 1234, Name = "BOSTON" },
                AwayTeam = new Team { Abbr = "NJW", Id = 4567, Name = "NJW" },
                Location = new Location(),
                TournamentSeasonId = "2020-2021",
            };
        }

        /// <summary>
        /// Get Mock Gms Game Data.
        /// </summary>
        /// <returns>Gms Game.</returns>
        private GmsGame GetMockGmsGameForDynEData()
        {
            // Arrange
            var operationsKeyValue = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = "NSS-Associated-Experiences",
                    Value = "pregame, postgame",
                },
                new KeyValuePair
                {
                    Key = "NSS-Associated-Experiences",
                    Value = "pregame",
                },
            };

            var schedules0 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 10,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "13",
                       KeyValuePairs = operationsKeyValue,
                   },
                },
            };

            var schedules1 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 123456,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "1500",
                   },
                },
                new Schedule
                {
                   Id = 1234567,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "1501",
                   },
                },
                new Schedule
                {
                   Id = 1234568,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
            };

            var medias = new List<MediaInfo>
            {
                new MediaInfo
                {
                    Id = 1000437,
                    Name = "MTV-NextGen-Test0",
                    Schedules = schedules0,
                    Active = true,
                    MediaType = new MediaType { Name = "NotNss" },
                },
                new MediaInfo
                {
                    Id = 1000432,
                    Name = "MTV-NextGen-Test1",
                    Schedules = schedules1,
                    Active = true,
                    MediaType = new MediaType { Name = "nss" },
                },
            };

            return new GmsGame()
            {
                Id = "Test",
                DateTime = new DateTimeOffset(2021, 06, 24, 21, 00, 00, new TimeSpan(0, 0, 0)),
                Active = true,
                Media = medias,
                HomeTeam = new Team { Abbr = "BOS", Id = 1234, Name = "BOSTON" },
                AwayTeam = new Team { Abbr = "NJW", Id = 4567, Name = "NJW" },
                Location = new Location(),
                TournamentSeasonId = "2020-2021",
            };
        }

        /// <summary>
        /// Get Mock Gms Game Data.
        /// </summary>
        /// <returns>Gms Game.</returns>
        private GmsGame GetMockGmsGameForThirdParty()
        {
            // Arrange
            var operationsKeyValue = new List<KeyValuePair>
            {
                new KeyValuePair
                {
                    Key = "NSS-ThirdParty",
                    Value = "true",
                },
            };

            var schedules0 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 10,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "12",
                   },
                },
            };

            var schedules1 = new List<Schedule>
            {
                new Schedule
                {
                   Id = 123456,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
                new Schedule
                {
                   Id = 1234567,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = "ATL-01",
                       KeyValuePairs = operationsKeyValue,
                   },
                },
                new Schedule
                {
                   Id = 1234568,
                   Active = true,
                   Operations = new Operation
                   {
                       Encoder = null,
                   },
                },
            };

            var medias = new List<MediaInfo>
            {
                new MediaInfo
                {
                    Id = 1000437,
                    Name = "MTV-NextGen-Test0",
                    Schedules = schedules0,
                    Active = true,
                    MediaType = new MediaType { Name = "NotNss" },
                },
                new MediaInfo
                {
                    Id = 1000432,
                    Name = "TP-NextGen-Test1",
                    Schedules = schedules1,
                    Active = true,
                    MediaType = new MediaType { Name = "nss" },
                    ThirdPartyStreamUrls = new List<ThirdPartyStreamUrl>()
                    {
                        new ThirdPartyStreamUrl()
                        {
                             Name = "High Resolution",
                             Url = new Uri("https://digitalstream.nba.com/radio/ratl-eng-01/1080p/play"),
                        },
                    },
                },
            };

            return new GmsGame()
            {
                Id = "Test",
                DateTime = new DateTimeOffset(2021, 06, 24, 21, 00, 00, new TimeSpan(0, 0, 0)),
                Active = true,
                Media = medias,
                HomeTeam = new Team { Abbr = "BOS", Id = 1234, Name = "BOSTON" },
                AwayTeam = new Team { Abbr = "NJW", Id = 4567, Name = "NJW" },
                Location = new Location(),
                TournamentSeasonId = "2020-2021",
            };
        }
    }
}
// "//-----------------------------------------------------------------------".
// <copyright file="LocationAddressConverterTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tests.Tvp.Mappers
{
    using System.Linq;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using Xunit;

    /// <summary>
    /// The <see cref="LocationAddressConverter"/> tests.
    /// </summary>
    public class LocationAddressConverterTests
    {
        /// <summary>
        /// Convert should works.
        /// </summary>
        [Fact]
        public void Convert_ShouldWorks()
        {
            // Arrange
            var converter = new LocationAddressConverter();
            var tvpEventLocationInfo = new TvpEventLocationInfo();
            var locationRequest = new LocationRequest() { LocationType = "LocationTypeTest" };

            // Act
            var result = converter.Convert(tvpEventLocationInfo, locationRequest, null);

            // Assert
            Assert.Equal(locationRequest, result);
        }

        /// <summary>
        /// Convert should works.
        /// </summary>
        [Fact]
        public void Convert_WithEmptyLocationRequest_ShouldWorks()
        {
            // Arrange
            var converter = new LocationAddressConverter();
            var tvpEventLocationInfo = new TvpEventLocationInfo
            {
                Name = "NameTest",
                CountryCode = "US",
                City = "CityTest",
                Postal = "PostalTest",
                LocationId = "LocationIdTest",
                StateOrProvince = "StateTest",
                Street = "StreetTest",
            };

            // Act
            var result = converter.Convert(tvpEventLocationInfo, null, null);

            // Assert
            var address = "StreetTest,CityTest,StateTest,PostalTest,US";
            Assert.NotNull(result);
            Assert.Equal(tvpEventLocationInfo.Name, result.Name.First().Value);
            Assert.Equal(tvpEventLocationInfo.Name, result.Description.First().Value);
            Assert.Equal(tvpEventLocationInfo.CountryCode, result.Country.First().Value);
            Assert.Equal(tvpEventLocationInfo.City, result.City.First().Value);
            Assert.Equal(tvpEventLocationInfo.StateOrProvince, result.State.First().Value);
            Assert.Equal(tvpEventLocationInfo.LocationId, result.ExternalId);
            Assert.Equal(address, result.Address.First().Value);
            Assert.Null(result.LocationType);
        }
    }
}

<#
.SYNOPSIS
    Restart all the function apps in the resource group.

.DESCRIPTION
    Restarts all the function apps in the resource group.

.PARAMETER resourceGroupName
    Function APP's Resource Group Name.
#>

param(
   [Parameter(Mandatory = $true)]
   [string]
   $resourceGroupName
)

$fapps = az functionapp list --resource-group $resourceGroupName --query "[*].name" --out tsv
$functionAppsForRestartArr = @($fapps.Split([Environment]::NewLine))

# restarts function apps
foreach ($functionappName in $functionAppsForRestartArr) {
    Write-Host "Stopping function app - $($functionappName) ..."
    az functionapp stop --name $functionappName --resource-group $resourceGroupName
    Write-Host "Waiting for some time ..."
    Start-Sleep -s 15
    Write-Host "Starting function app -  $($functionappName) ..."
    az functionapp start --name $functionappName --resource-group $resourceGroupName
 }
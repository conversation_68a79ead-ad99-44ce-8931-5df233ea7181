<#
.SYNOPSIS
    Updates the Name Value

.DESCRIPTION
    Updates the Name Value

.PARAMETER resourceGroupName
    APIM Resource Group Name.

.PARAMETER apimServiceName
    APIM Service Name

.PARAMETER namedValueId
    Named ID

.PARAMETER value
    Named Value 

#>

param(
    [Parameter(Mandatory=$true)]
    [string]
    $resourceGroupName,

    [Parameter(Mandatory=$true)]
    [string]
    $apimServiceName,

    [Parameter(Mandatory=$true)]
    [string]
    $namedValueId,
 
    [Parameter(Mandatory=$true)]
    [string]
    $value
)

# Gets the APIM context.
$apimContext = New-AzApiManagementContext -ResourceGroupName $resourceGroupName -ServiceName $apimServiceName

# Updates the specified named value.
Set-AzApiManagementNamedValue -Context $apimContext -NamedValueId $namedValueId -Value $value
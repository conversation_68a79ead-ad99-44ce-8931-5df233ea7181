<#
.SYNOPSIS
   Update the Key Vault secret's Date

.DESCRIPTION
   Update the Key Vault secret's if the the secret don't have expiry date it will add expiry date to 85 days.
   If the secret expiry date is less than 15 days it will update the secret expiry date to 85 days.

.PARAMETER keyVaultName
   Key Vault Name

#>

param(
    [string]
    $keyVaultName
)
 
Function GetKeyVaultEntries(
    [string]$keyVaultName
) {

    $InstalledModules = Get-InstalledModule

    if ($InstalledModules.Name -notcontains "Az.KeyVault" ) {
        Write-Host "##[debug] Installing Az.KeyVault"

        Install-Module "Az.KeyVault" -Force -AllowClobber

        Import-Module "Az.KeyVault" -Force
    }

    $keyVaultEntries = Get-AzKeyVaultSecret -VaultName $keyVaultName
    $today = Get-Date
    $expirationDate = $today.AddDays(85)

    foreach ($entry in $keyVaultEntries) {
    
        $secret = Get-AzKeyVaultSecret -VaultName $entry.VaultName -Name $entry.Name
        $secretName = $secret.Name
        $originalDate = $secret.Attributes.Expires
    
        if ($null -eq $originalDate) {
            Write-Host "$secretName did not have an expiration date."
            $originalDate = $today
        }
    
        if ( ($originalDate) -lt $today.AddDays(15)) {
        
            if ("" -eq $secret.value) {
                Write-Host "$secretName does not have a value."
            }
            else {                
                Write-Host "Updating secret..."

                $secret = Set-AzKeyVaultSecret -VaultName $keyVaultName -Name $secret.Name -SecretValue $secret.SecretValue -Expires $expirationDate -ContentType $secret.ContentType -Tags $secret.Tags 
  
                Write-Host "$secretName refreshed."
            }
        }
    }
}

GetKeyVaultEntries $keyVaultName
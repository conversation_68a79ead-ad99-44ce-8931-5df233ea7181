# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- manual

pool:
  vmImage: ubuntu-latest



stages:
# - stage: Sandbox
#   variables:
#     - group: OTT VideoPlatform All
#     - template: variables.yml
#   jobs:
#   - deployment:
#     displayName: Sand Deploy
#     environment: sand-alerts
#     strategy:
#      runOnce:
#        deploy:
#          steps:
#          - checkout: self
#          - bash: |
#              echo "Replacing '*********************:v3/nbadev/DTC/IaC' to 'https://<EMAIL>/nbadev/DTC/_git/IaC' ..."
#              find . -name '*.tf' | xargs sed -i 's/*********************:v3\/nbadev\/DTC\/IaC/https:\/\/$(IacRepoAccessToken)@dev.azure.com\/nbadev\/DTC\/_git\/IaC/g'
#              echo "... Done!"
#            displayName: Setup IaC Repo Access
#            workingDirectory: ${{ variables.Workingdirectory }}
#          - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
#            displayName: "TerraformInstaller"
#            inputs:
#              terraformVersion: '1.0.8'

#          - task: TerraformTaskV2@2
#            displayName: "Terraform init"
#            inputs:
#              provider: 'azurerm'
#              command: 'init'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              backendServiceArm:  ${{ variables.Sandconnection }}
#              backendAzureRmResourceGroupName: 'rg-Terraform'
#              backendAzureRmStorageAccountName: 'stottsandboxterraform'
#              backendAzureRmContainerName: 'sandbox'
#              backendAzureRmKey: 'terraformmonitoring0005.tfstate'

    
#          - task: TerraformTaskV2@2
#            displayName: "Terrafrom plan"
#            inputs:
#              provider: 'azurerm'
#              command: 'plan'
#              commandOptions: '-var "environment=sand"'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              environmentServiceNameAzureRM: ${{ variables.Sandconnection }}

#          - task: TerraformTaskV2@2
#            displayName: Terraform apply
#            inputs:
#              provider: 'azurerm'
#              command: 'apply'
#              commandOptions: '-var "environment=sand"'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              environmentServiceNameAzureRM:  ${{ variables.Sandconnection }}

# - stage: Dev
#   variables:
#     - group: OTT VideoPlatform All
#     - template: variables.yml
#   jobs:
#   - deployment:
#     displayName: Dev Deploy
#     environment: devint-alerts
#     strategy:
#      runOnce:
#        deploy:
#          steps:
#          - checkout: self
#          - bash: |
#              echo "Replacing '*********************:v3/nbadev/DTC/IaC' to 'https://<EMAIL>/nbadev/DTC/_git/IaC' ..."
#              find . -name '*.tf' | xargs sed -i 's/*********************:v3\/nbadev\/DTC\/IaC/https:\/\/$(IacRepoAccessToken)@dev.azure.com\/nbadev\/DTC\/_git\/IaC/g'
#              echo "... Done!"
#            displayName: Setup IaC Repo Access
#            workingDirectory: ${{ variables.Workingdirectory }}
#          - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
#            displayName: "TerraformInstaller"
#            inputs:
#              terraformVersion: '1.0.8'

#          - task: TerraformTaskV2@2
#            displayName: "Terraform init"
#            inputs:
#              provider: 'azurerm'
#              command: 'init'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              backendServiceArm:  ${{ variables.Devconnection }}
#              backendAzureRmResourceGroupName: 'rg-Terraform'
#              backendAzureRmStorageAccountName: 'stottdevterraform'
#              backendAzureRmContainerName: 'devint'
#              backendAzureRmKey: 'terraformmonitoring0005.tfstate'

    
#          - task: TerraformTaskV2@2
#            displayName: "Terrafrom plan"
#            inputs:
#              provider: 'azurerm'
#              command: 'plan'
#              commandOptions: '-var "environment=devint"'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              environmentServiceNameAzureRM: ${{ variables.Devconnection }}

#          - task: TerraformTaskV2@2
#            displayName: Terraform apply
#            inputs:
#              provider: 'azurerm'
#              command: 'apply'
#              commandOptions: '-var "environment=devint"'
#              workingDirectory: ${{ variables.Workingdirectory }}
#              environmentServiceNameAzureRM:  ${{ variables.Devconnection }}


- stage: IaC Security
  variables:
    - template: ../../templates/variables/infra.qa.var.yml
  jobs:
    - template: ../../templates/infra.ci.template.yml
      parameters:
        scan_agent: ${{ variables.private_agent_pool_name_scan }}
        scan_key_vault_name: ${{ variables.deploy_keyvault_name }}
        service_connection: ${{ variables.service_connection }}
        terraform_action_arguments: ${{ variables.terraform_action_arguments_variables }}
        terraform_artifact_name: ${{ variables.terraform_artifact_name }}
        terraform_backend_config_resource_group: ${{ variables.tf_state_resource_group }}
        terraform_backend_config_storage_account: ${{ variables.tf_state_storage_account }}
        terraform_backend_config_container: ${{ variables.tf_state_container }}
        terraform_backend_config_state_file: ${{ variables.tf_state_file}}
        terraform_custom_modules_path: ${{ variables.custom_modules_path }}
        terraform_deployment_path_relative_path: ${{ variables.deployment_relative_path }}
        terraform_version: ${{variables.terraform_version}}
- stage: QA
  variables:
    - group: OTT VideoPlatform All
    - template: variables.yml
  jobs:  
  - deployment:
    displayName: QA Deploy
    environment: qa-alerts
    strategy:
     runOnce:
       deploy:
         steps:
         - checkout: self
         - bash: |
             echo "Replacing '*********************:v3/nbadev/DTC/IaC' to 'https://<EMAIL>/nbadev/DTC/_git/IaC' ..."
             find . -name '*.tf' | xargs sed -i 's/*********************:v3\/nbadev\/DTC\/IaC/https:\/\/$(IacRepoAccessToken)@dev.azure.com\/nbadev\/DTC\/_git\/IaC/g'
             echo "... Done!"
           displayName: Setup IaC Repo Access
           workingDirectory: ${{ variables.Workingdirectory }}
         - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
           displayName: "TerraformInstaller"
           inputs:
             terraformVersion: '1.0.8'

         - task: TerraformTaskV2@2
           displayName: "Terraform init"
           inputs:
             provider: 'azurerm'
             command: 'init'
             workingDirectory: ${{ variables.Workingdirectory }}
             backendServiceArm:  ${{ variables.QAconnection }}
             backendAzureRmResourceGroupName: 'rg-Terraform'
             backendAzureRmStorageAccountName: 'stottqaterraform'
             backendAzureRmContainerName: 'qual'
             backendAzureRmKey: 'terraformmonitoring0005.tfstate'

    
         - task: TerraformTaskV2@2
           displayName: "Terrafrom plan"
           inputs:
             provider: 'azurerm'
             command: 'plan'
             commandOptions: '-var "environment=qa"'
             workingDirectory: ${{ variables.Workingdirectory }}
             environmentServiceNameAzureRM: ${{ variables.QAconnection }}

         - task: TerraformTaskV2@2
           displayName: Terraform apply
           inputs:
             provider: 'azurerm'
             command: 'apply'
             commandOptions: '-var "environment=qa"'
             workingDirectory: ${{ variables.Workingdirectory }}
             environmentServiceNameAzureRM:  ${{ variables.QAconnection }}

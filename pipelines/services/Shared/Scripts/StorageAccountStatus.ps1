<#
.SYNOPSIS
    Azure Storage Account Failover Status.

.DESCRIPTION
    To Check Azure Storage Account Failover Status.

.PARAMETER ResourceGroups
    List of storage account Resource Group Name's

#>
param(
    [string[]] $ResourceGroups = (throw new Exception("The List of Resource Groups is mandatory"))
) & {
    foreach ($resourceGroup in $ResourceGroups) {

        Write-Host "Checking Storage accounts in $resourceGroup..." -ForegroundColor DarkYellow
        $storages = (az storage account list -g $resourceGroup | ConvertFrom-Json)

        foreach ($storage in $storages) {
            $storageName = $storage.name
            Write-Host "Checking $storageName status..." -ForegroundColor DarkGreen

            $failover = ($storage.failoverInProgress -eq $true)
            Write-Host "Failover in Progress: $failover"
            Write-Host "Primary location: $($storage.primaryLocation) ($($storage.statusOfPrimary))"

            if ($storage.secondaryLocation -ne $null) {
                Write-Host "Secondary location: $($storage.secondaryLocation) ($($storage.statusOfSecondary))"

                $status = (az storage account show --name $storageName --expand geoReplicationStats | ConvertFrom-Json)
                Write-Host "Can Failover: $($status.geoReplicationStats.canFailover)"
                Write-Host "Last Sync Time: $($status.geoReplicationStats.lastSyncTime) `r`n"
            }
            else {
                Write-Host "Account type is $($storage.sku.name), failover is not supported. `r`n" -ForegroundColor DarkMagenta
            }
        }
    }
}
param(
    [string] $environment = ""
)


$prodEnvironments = @("prod");

$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"FUNCTIONS_EXTENSION_VERSION=~4",
"FUNCTIONS_WORKER_RUNTIME=dotnet",
"FUNCTIONS_INPROC_NET8_ENABLED=1",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"WEBSITE_RUN_FROM_PACKAGE=1",
"HealthTriggerCron=0 */10 * * * *",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigSettings__ServiceName=TvpActor",
"ConfigRefreshTimeoutCron=0 */5 * * * *",
"InfrastructureStateChangeRequestMfTvpQueueName=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_infrastructure_state_change_request_tvp_queuename)",
"TvpStateNotifierQueueName=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_notify_ecms_tvp_queuename)",
"TvpDeleteDummyProductionQueue=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_delete_dummy_production_tvp_queuename)",
'@

$environmentConfig = $null

if ($prodEnvironments.Contains($environment)) {
    $environmentConfig = @'
    "TvpActorOptions__ListOfSingleGameValidPackages=1001041-LAL,1001040-UTA"
'@
}
else {
    $environmentConfig = @'
    "TvpActorOptions__ListOfSingleGameValidPackages=1000574-LAL,1000572-UTA"
'@
}

$config = $config + $environmentConfig;

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"
trigger: none

parameters:
  - name: env
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      - devint
      - qa

  - name: include_env_name_in_secret_name_prefix
    displayName: Include Env Name In Secret Name Prefix ?
    type: boolean
    default: true

variables:
  - group: Test-Automation-Common-Variables
  - template: $(System.DefaultWorkingDirectory)/pipelines/templates/variables/private_agent.nonprod.yml

  - name: mkhub_accountId
    ${{ if eq(parameters.env, 'qa') }}:
      value: "$(MKHubAccountId-ProdB)"
    ${{ if ne(parameters.env, 'qa') }}:
      value: "$(MKHubAccountId-ProdC)"

  - name: subscription
    ${{ if in(parameters.env, 'dev', 'devint') }}:
      value: 'dev'
    ${{ if eq(parameters.env, 'qa') }}:
      value: 'qa'

  - name: secret_name_prefix
    ${{ if eq(parameters.include_env_name_in_secret_name_prefix, true) }}:
      value: "VideoPlatform-${{ parameters.env }}"
    ${{ if ne(parameters.include_env_name_in_secret_name_prefix, true) }}:
      value: "VideoPlatform"

jobs:
  - job: "CreateSecrets"
    displayName: "Create Secrets In Test Key vault"
    pool:
      name: $(private_agent_pool_name_linux)
    steps:
      - task: AzureCLI@2
        enabled: true
        displayName: Create Test Key vault secrets
        inputs:
          azureSubscription: $(service-connection-ott-${{ variables.subscription }})
          scriptType: pscore
          scriptLocation: scriptPath
          scriptPath: '$(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Set-SecretsInTestKeyVault.ps1'
          arguments:
            -primaryResourceGroupName "$(rg-videoplatform-${{ parameters.env }})" `
            -openResourceGroupName "$(rg-videoplatform-${{ parameters.env }}-open)" `
            -testKeyVaultName "$(test-kv-name-ott-${{ variables.subscription }})" `
            -secretNamePrefix "$(secret_name_prefix)" `
            -mkHubAccountId "$(mkhub_accountId)" `
            -oldSandboxQCStorageConnectionString "$(OldSandboxQCStorageConnectionString)"

      - task: AzurePowerShell@4
        enabled: true
        displayName: Create Test Key vault secrets - 2
        inputs:
          azureSubscription: $(service-connection-ott-${{ variables.subscription }})
          ScriptPath: '$(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Set-SecretsInTestKeyVault2.ps1'
          ScriptArguments: '-primaryResourceGroupName "$(rg-videoplatform-${{ parameters.env }})" -testKeyVaultName "$(test-kv-name-ott-${{ variables.subscription }})" -secretNamePrefix "$(secret_name_prefix)"'
          azurePowerShellVersion: LatestVersion

      - task: AzureCLI@2
        enabled: true
        name: FetchSharedAppInsightsInfo
        displayName: Fetch Shared App Insights Info
        inputs:
          azureSubscription: $(service-connection-ott-shared)
          scriptType: pscore
          scriptLocation: inlineScript
          inlineScript: |
            az extension add --name application-insights
            $appInsightsName = az monitor app-insights component show -g "$(rg-shared-${{ variables.subscription }})" --query "[0].name"
            $appInsightsAppId = az monitor app-insights component show -g "$(rg-shared-${{ variables.subscription }})" --query "[0].appId"
            az monitor app-insights api-key delete --app $appInsightsName -g "$(rg-shared-${{ variables.subscription }})" --api-key "test_automation"
            $appInsightsApikey = az monitor app-insights api-key create --api-key "test_automation" --read-properties ReadTelemetry --write-properties '""' -g "$(rg-shared-${{ variables.subscription }})" --app $appInsightsName --query "apiKey"
            az extension remove --name application-insights
            Write-Host "##vso[task.setvariable variable=AppInsightsAppId;isoutput=true;issecret=true]$appInsightsAppId"
            Write-Host "##vso[task.setvariable variable=AppInsightsApikey;isoutput=true;issecret=true]$appInsightsApikey"

      - task: AzureCLI@2
        enabled: true
        displayName: Create Shared services secrets in Test Key vault
        inputs:
          azureSubscription: $(service-connection-ott-${{ variables.subscription }})
          scriptType: pscore
          scriptLocation: inlineScript
          inlineScript: |
            $firstDayMonth = Get-Date -Format "yyyy-MM-01T00:00:00Z"
            $expirationDate = (Get-Date $firstDayMonth).AddHours(2400).ToString("yyyy-MM-ddT00:00:00Z")
            $output = az keyvault secret set --name "$(secret_name_prefix)-AppInsightsAppId" --vault-name "$(test-kv-name-ott-${{ variables.subscription }})" --value $(FetchSharedAppInsightsInfo.AppInsightsAppId) --expires $expirationDate
            $output = az keyvault secret set --name "$(secret_name_prefix)-AppInsightsApiValue" --vault-name "$(test-kv-name-ott-${{ variables.subscription }})" --value $(FetchSharedAppInsightsInfo.AppInsightsApikey) --expires $expirationDate

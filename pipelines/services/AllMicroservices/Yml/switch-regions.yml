trigger: none

parameters: 
  - name: selected_environment
    displayName: Environment
    type: string
    default: uat
    values:
      - uat
      - prod

  - name: active_region
    displayName: Choose desired active region
    type: string
    default: eastus2
    values:
      - eastus2
      - centralus

pool:
  vmImage: 'ubuntu-latest'

variables:
  - name: Primary_Region_Functions_Enabled
    ${{if eq(parameters.active_region, 'eastus2')}}:
      value: true
    ${{if ne(parameters.active_region, 'eastus2')}}:
      value: false
  
  - name: Secondary_Region_Functions_Enabled
    ${{if eq(parameters.active_region, 'centralus')}}:
      value: true
    ${{if ne(parameters.active_region, 'centralus')}}:
      value: false
  
  - name: Primary_State
    ${{if eq(variables['Primary_Region_Functions_Enabled'], 'true')}}:
      value: "Enabling"
    ${{if eq(variables['Primary_Region_Functions_Enabled'], 'false')}}:
      value: "Disabling"
  
  - name: Secondary_State
    ${{if eq(variables['Secondary_Region_Functions_Enabled'], 'true')}}:
      value: "Enabling"
    ${{if eq(variables['Secondary_Region_Functions_Enabled'], 'false')}}:
      value: "Disabling"
  
stages:
  
  - stage: ${{parameters.selected_environment}}
    
    jobs:
      
      - template: ../../../templates/jobs/switch-function-region.yml
        parameters:
          jobName: Functions
          Primary_Region_Functions_Enabled: ${{ variables.Primary_Region_Functions_Enabled }}
          Secondary_Region_Functions_Enabled: ${{ variables.Secondary_Region_Functions_Enabled }}
          Primary_State: ${{ variables.Primary_State }}
          Secondary_State: ${{ variables.Secondary_State }}
          environment: ${{ parameters.selected_environment }}
# Adding a role assignment that allows the function app host identity to retrieve from app configuration
module "rbac_appconfig" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = compact([local.primary.resources.appconfig.id, try(local.secondary.resources.appconfig.id, null)])
  role_definition_name = "App Configuration Data Reader"
  principal_ids        = local.appservice_ids
}

# Adding a role assignment that allows the function app host identity to write into Cosmos DataBase
module "rbac_cosmos" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = [local.primary.resources.cosmos.id]
  role_definition_name = "DocumentDB Account Contributor"
  principal_ids        = local.appservice_ids
}

# Adding a role assignment that allows the function app host identity to publish to the shared event grid
module "rbac_eventgrid" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = compact([local.primary.resources.eventgrid.id, try(local.secondary.resources.eventgrid.id, null)])
  role_definition_name = "EventGrid Data Sender"
  principal_ids        = local.appservice_ids
}

# Adding a role assignment that allows the function app host identity to publish to the shared service bus

module "rbac_servicebus" {
  source = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes = compact([
    local.primary.resources.servicebus_queues.iscr_aquila_channels.id,
    local.primary.resources.servicebus_queues.scr.id,
    local.primary.resources.servicebus_queues.wr.id,
    try(local.secondary.resources.servicebus_queues.iscr_aquila_channels.id, null),
    try(local.secondary.resources.servicebus_queues.scr.id, null),
    try(local.secondary.resources.servicebus_queues.wr.id, null)
  ])
  role_definition_name = "Azure Service Bus Data Sender"
  principal_ids        = local.appservice_ids
  providers = {
    azurerm = azurerm.SharedServicesProvider
  }
}

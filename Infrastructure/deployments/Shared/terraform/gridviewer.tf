resource "azurerm_app_service" "eventgridviewer" {
  name                = module.setup.primary.adapted_names.event_viewer.name
  location            = module.setup.primary.location
  resource_group_name = module.setup.primary.adapted_names.event_viewer.resource_group
  app_service_plan_id = module.appserviceplan-0000.id
  https_only          = true
  tags                = module.setup.tags
  depends_on = [
    module.resourcegroup,
    module.resourcegroup_open
  ]

  logs {
    detailed_error_messages_enabled = true
    failed_request_tracing_enabled  = true

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 100
      }
    }
  }

  lifecycle {
    ignore_changes = [
      site_config.0.ip_restriction, # Ignoring Ip Changes. Evaluate an automatic pipeline to clean up once in a while.
      tags
    ]
  }

  identity {
    type = "SystemAssigned"
  }

  site_config {
    dotnet_framework_version = "v5.0"
    linux_fx_version         = "DOTNETCORE|5.0"
    ftps_state               = "Disabled"
    http2_enabled            = true
    websockets_enabled       = false
    ip_restriction = [
      {
        name                      = "Event Grid"
        priority                  = 1
        service_tag               = "AzureEventGrid"
        action                    = "Allow"
        ip_address                = null
        virtual_network_subnet_id = null
        headers                   = []
      }
    ]
  }

  app_settings = {
    "PROJECT"               = "src/Blazor.EventGridViewer.ServerApp/Blazor.EventGridViewer.ServerApp.csproj"
    "EnableAuth"            = false
    "AzureAd__TenantId"     = ""
    "AzureAd__Instance"     = ""
    "AzureAd__CallbackPath" = ""
    "AzureAd__Domain"       = ""
    "AzureAd__ClientId"     = ""
  }
}

resource "azurerm_resource_group_template_deployment" "source-control" {
  name                = "sourcecontrolviewer${substr(local.force_vnet_redeploy, 0, 10)}"
  resource_group_name = azurerm_app_service.eventgridviewer.resource_group_name
  depends_on = [
    azurerm_app_service.eventgridviewer
  ]
  deployment_mode = "Incremental"
  # jsonencode then decode catches formatting issues
  # early rather than requiring the deployment to be submitted and fail
  # (which takes a while) to find out you missed a comma
  template_content = jsondecode(jsonencode(<<JSON
  {
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "1.0.0.0",
    "resources": [
      {
          "apiVersion": "2018-11-01",
          "name": "${azurerm_app_service.eventgridviewer.name}/web",
          "type": "Microsoft.Web/sites/sourcecontrols",
          "properties": {
            "RepoUrl": "https://github.com/Azure-Samples/eventgrid-viewer-blazor.git",
            "branch": "main",
            "IsManualIntegration": true
          }
        }
    ]
  } 
  JSON
  ))
}

# Necessary for a from-scratch deployment, please do not remove.
resource "time_sleep" "wait_for_viewer_deployment" {
  create_duration = "180s"
  depends_on = [
    azurerm_resource_group_template_deployment.source-control
  ]
}


resource "azurerm_eventgrid_event_subscription" "topicsubscription" {
  name  = "eventgridviewerSubscription"
  scope = module.eventgrid.id
  webhook_endpoint {
    url                               = "https://${module.setup.primary.adapted_names.event_viewer.name}.azurewebsites.net/api/eventgrid"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }
  depends_on = [
    time_sleep.wait_for_viewer_deployment,
    module.eventgrid
  ]
}

resource "azurerm_eventgrid_event_subscription" "healthtopicsubscription" {
  name  = "EventGridViewerHealthTopicSubscription"
  scope = module.eventgrid_health.id
  webhook_endpoint {
    url                               = "https://${module.setup.primary.adapted_names.event_viewer.name}.azurewebsites.net/api/eventgrid"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }
  depends_on = [
    time_sleep.wait_for_viewer_deployment,
    module.eventgrid_health
  ]
}

resource "azurerm_eventgrid_event_subscription" "topicsubscription_secondary" {
  count = module.setup.is_high_available ? 1 : 0
  name  = "eventgridviewerSubscription"
  scope = local.secondary_eventgrid.id
  webhook_endpoint {
    url                               = "https://${module.setup.primary.adapted_names.event_viewer.name}.azurewebsites.net/api/eventgrid"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }
  depends_on = [
    time_sleep.wait_for_viewer_deployment,
    local.secondary_eventgrid
  ]
}

resource "azurerm_eventgrid_event_subscription" "healthtopicsubscription_secondary" {
  count = module.setup.is_high_available ? 1 : 0
  name  = "EventGridViewerHealthTopicSubscription"
  scope = local.secondary_eventgrid_health.id
  webhook_endpoint {
    url                               = "https://${module.setup.primary.adapted_names.event_viewer.name}.azurewebsites.net/api/eventgrid"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }
  depends_on = [
    time_sleep.wait_for_viewer_deployment,
    local.secondary_eventgrid_health
  ]
}

resource "azurerm_app_service_virtual_network_swift_connection" "eventgridviewer" {
  app_service_id = azurerm_app_service.eventgridviewer.id
  subnet_id      = module.setup.primary.resources.subnet_app.id

  depends_on = [
    azurerm_eventgrid_event_subscription.topicsubscription,
    azurerm_eventgrid_event_subscription.healthtopicsubscription,
    azurerm_eventgrid_event_subscription.topicsubscription_secondary,
    azurerm_eventgrid_event_subscription.healthtopicsubscription_secondary
  ]
}

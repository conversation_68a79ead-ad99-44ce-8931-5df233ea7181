module "logger" {
  source                                   = "git::*********************:v3/nbadev/TerraformModules/APIManagementLogger?ref=v0.0.0"
  name                                     = "videoplatform-logger-001"
  api_management_name                      = data.azurerm_api_management.api_management.name
  resource_group_name                      = data.azurerm_api_management.api_management.resource_group_name
  application_insights_id                  = module.setup.primary.resources.application_insights.id
  application_insights_instrumentation_key = module.setup.primary.resources.application_insights.instrumentation_key
}
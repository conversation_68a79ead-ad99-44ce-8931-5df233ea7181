variable "environment" {
  description = "deployment environment (dev, qa, prod)"
  type        = string
}

variable "vpc_id" {
  description = "id of the existing vpc"
  type        = string
}

variable "subnet_ids" {
  description = "list of subnet ids within the vpc for the documentdb cluster"
  type        = list(string)
}

variable "security_group_ids" {
  description = "list of security group ids to associate with the documentdb cluster"
  type        = list(string)
}

variable "docdb_instance_class" {
  description = "instance class for documentdb instances"
  type        = string
}

variable "docdb_instance_count" {
  description = "number of instances in the documentdb cluster"
  type        = number
}

variable "master_username" {
  description = "username for the master db user"
  type        = string
}

variable "master_password" {
  description = "password for the master db user"
  type        = string
  default     = ""
  sensitive   = true
}

variable "aws_region" {
  type    = string
  default = ""
}

variable "aws_backup_region" {
  type    = string
  default = "us-east-2"
}

variable "dmm_endpoint" {
  type    = string
  default = ""
}

variable "gmswatchdog_endpoint" {
  type    = string
  default = ""
}

variable "scheduler_endpoint" {
  type    = string
  default = ""
}

variable "tvp_endpoint" {
  type    = string
  default = ""
}

variable "aquila_endpoint" {
  type    = string
  default = ""
}

variable "quortex_endpoint" {
  type    = string
  default = ""
}

variable "prismaworker_endpoint" {
  type    = string
  default = ""
}

variable "prismamanager_endpoint" {
  type    = string
  default = ""
}

variable "aquila_settings_accountid" {
  type    = string
  default = ""
}
variable "aquila_settings_accountname" {
  type    = string
  default = ""
}
variable "aquila_settings_useremail" {
  type    = string
  default = ""
}
variable "aquila_settings_authendpoint" {
  type    = string
  default = ""
}
variable "tvp_settings_authendpoint" {
  type    = string
  default = ""
}
variable "dmm_settings_accountid" {
  type    = string
  default = ""
}

variable "quortex_pool_uuid" {
  type    = string
  default = ""
}

variable "quortex_pool_uuid_dr" {
  type    = string
  default = ""
}

variable "quortex_pool_uuid_radio" {
  type    = string
  default = ""
}

variable "storageaccount_snapshot" {
  type    = string
  default = ""
}

variable "prisma_settings_managerid" {
  type    = string
  default = ""
}

variable "quortex_settings_accountid" {
  type    = string
  default = ""
}

variable "quortex_settings_token_endpoint" {
  type    = string
  default = ""
}

variable "azure_subscription_id" {
  type    = string
  default = ""
}

variable "azure_vidplat_cosmosdb_rg" {
  type    = string
  default = ""
}

variable "azure_vidplat_cosmosdb_name" {
  type    = string
  default = ""
}

variable "azure_vidplat_cosmosdb_endpoint" {
  type    = string
  default = ""
}

variable "aws_vidplat_mongodb_name" {
  type    = string
  default = ""
}

variable "azure_tenant_id" {
  type    = string
  default = ""
}
variable "gms_endpoint" {
  type = string
}

variable "ecr_names" {
  description = "List of all repository names"
  type        = list(string)
}

variable "eks_cluster_id" {
  type    = string
  default = ""
}

variable "secrets" {
  type    = map(string)
  default = {}
}

variable "share_secret_policy" {
  type    = string
  default = "nba-dvgbl-shared-secrets-policy001"
}

variable "ecms_base_url" {
  type    = string
  default = ""
}

variable "eventgrid_eg1" {
  type    = string
  default = ""
}

variable "eventgrid_eg2" {
  type    = string
  default = ""
}
resource "aws_sns_topic" "vidplat_events_noti" {
  name                        = "vp-${var.environment}-event_topic_01.fifo"
  fifo_topic                  = true
  content_based_deduplication = true
  tags                        = local.tags
}

resource "aws_sns_topic" "vidplat_events_noti_secondary" {
  name                        = "vp-${var.environment}-event_topic_02.fifo"
  fifo_topic                  = true
  content_based_deduplication = true
  tags                        = local.tags
}

resource "aws_sns_topic" "vidplat_events_noti_health" {
  name                        = "vp-${var.environment}-health-event_topic_01.fifo"
  fifo_topic                  = true
  content_based_deduplication = true
  tags                        = local.tags
}

resource "aws_sns_topic" "vidplat_events_noti_health_secondary" {
  name                        = "vp-${var.environment}-health-event_topic_02.fifo"
  fifo_topic                  = true
  content_based_deduplication = true
  tags                        = local.tags
}



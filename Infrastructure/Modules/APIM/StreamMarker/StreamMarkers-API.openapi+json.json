{"openapi": "3.0.1", "info": {"title": "StreamMarkers-API", "description": "An aquila API to get the SCTE35 stream markers", "version": "1.0"}, "servers": [{"url": "http://ott-diue2-intcall-apim001.azure-api.net/StreamMarkersUpdate"}, {"url": "https://ott-diue2-intcall-apim001.azure-api.net/StreamMarkersUpdate"}], "paths": {"/accounts/channels/{channelId}/{instanceId}": {"post": {"summary": "PostStreamMarkerResponse", "operationId": "PostStreamMarkerResponse", "parameters": [{"name": "channelId", "in": "path", "description": "The channel identifier", "required": true, "schema": {"type": "string"}}, {"name": "instanceId", "in": "path", "description": "The instance identifier", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/streamMarkerNotification"}, "example": {"version": "string", "events": [{"counter": 0, "spliceTime": "string", "acquisitionTime": "string", "acquisitionPointIdentity": "string", "zoneIdentity": "string", "signal": {"spliceInfoSection": {"ptsAdjustment": 0, "protocolVersion": 0, "tier": 0, "spliceSchedule": {"spliceEventId": "string", "spliceEventCancelIndicator": true, "outOfNetworkIndicator": true, "spliceImmediateFlag": true, "uniqueProgramId": 0, "availNum": 0, "availsExpected": 0, "program": {"utcSpliceTime": "string", "spliceTime": {"ptsTime": 0}}, "component": [{"componentTag": 0}], "breakDuration": {"autoReturn": true, "duration": 0}}, "spliceInsert": {"spliceEventId": "string", "spliceEventCancelIndicator": true, "outOfNetworkIndicator": true, "spliceImmediateFlag": true, "uniqueProgramId": 0, "availNum": 0, "availsExpected": 0, "program": {"utcSpliceTime": "string", "spliceTime": {"ptsTime": 0}}, "component": [{"componentTag": 0}], "breakDuration": {"autoReturn": true, "duration": 0}}, "timeSignal": {"spliceTime": {"ptsTime": 0}}, "privateCommand": {"identifier": 0, "privateBytes": "string"}, "availDescriptor": [{"providerAvailId": 0}], "dtmfDescriptor": [{"preroll": 0, "chars": "string"}], "segmentationDescriptor": [{"segmentationEventId": "string", "segmentationEventCancelIndicator": true, "segmentationDuration": 0, "segmentationTypeId": 0, "segmentNum": 0, "segmentsExpected": 0, "subSegmentNum": 0, "subSegmentsExpected": 0, "deliveryRestrictions": {"webDeliveryAllowedFlag": true, "noRegionalBlackoutFlag": true, "archiveAllowedFlag": true, "deviceRestrictions": 0}, "segmentationUpid": [{"segmentationUpidType": 0, "formatIdentifier": 0, "segmentationUpidFormat": "string", "content": "string"}], "component": [{"ptsOffset": 0, "componentTag": 0}]}], "timeDescriptor": [{"taiSeconds": 0, "taiNs": 0, "utcOffset": 0}]}}, "forceScteProcess": true}]}}}}, "responses": {"204": {"description": "No Content.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountsChannelsChannelIdInstanceIdPost204ApplicationJsonResponse"}, "example": "string"}}}, "400": {"description": "Bad Request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountsChannelsChannelIdInstanceIdPost400ApplicationJsonResponse"}, "example": "string"}}}, "500": {"description": "Internal Server Error.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountsChannelsChannelIdInstanceIdPost500ApplicationJsonResponse"}, "example": "string"}}}}}}, "/readiness": {"get": {"summary": "Readiness", "description": "Returns readiness of the service", "operationId": "Readiness", "responses": {"200": {"description": null}}}}, "/liveness": {"get": {"summary": "Liveness", "description": "Returns liveness of the service", "operationId": "Liveness", "responses": {"200": {"description": null}}}}, "/orchestrator/{productionId}/streamMarkers": {"get": {"summary": "Stream Markers", "description": "Return a list with the Stream Markers for given Production Id", "operationId": "StreamMarker", "parameters": [{"name": "productionId", "in": "path", "description": "The production identifier", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Ok.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/streamMarkerEvent"}, "example": "string"}}}, "400": {"description": "Bad Request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/streamMarkerEvent"}, "example": "string"}}}, "404": {"description": "Not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/streamMarkerEvent"}, "example": "string"}}}}}}, "/channels/{channelId}/listeningWindow": {"get": {"summary": "ListeningWindow", "description": "Returns if the SCTE35 listening window for channel is open or not", "operationId": "ListeningWindow", "parameters": [{"name": "channelId", "in": "path", "description": "The channel identifier", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Ok.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowGet200ApplicationJsonResponse"}, "example": "string"}}}, "400": {"description": "Bad Request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowGet400ApplicationJsonResponse"}, "example": "string"}}}, "404": {"description": "Not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowGet404ApplicationJsonResponse"}, "example": "string"}}}}}}, "/channels/{channelId}/listeningWindow/enable": {"put": {"summary": "EnableListeningWindow", "description": "Enable the SCTE35 listening window of a channel", "operationId": "EnableListeningWindow", "parameters": [{"name": "channelId", "in": "path", "description": "The channel identifier", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No content.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowEnablePut204ApplicationJsonResponse"}, "example": "string"}}}, "400": {"description": "Bad Request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowEnablePut400ApplicationJsonResponse"}, "example": "string"}}}, "404": {"description": "Not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowEnablePut404ApplicationJsonResponse"}, "example": "string"}}}}}}, "/channels/{channelId}/listeningWindow/disable": {"put": {"summary": "DisableListeningWindow", "description": "Disable the SCTE35 listening window of a channel", "operationId": "DisableListeningWindow", "parameters": [{"name": "channelId", "in": "path", "description": "The channel identifier", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No content.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowDisablePut204ApplicationJsonResponse"}, "example": "string"}}}, "400": {"description": "Bad Request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowDisablePut400ApplicationJsonResponse"}, "example": "string"}}}, "404": {"description": "Not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChannelsChannelIdListeningWindowDisablePut404ApplicationJsonResponse"}, "example": "string"}}}}}}}, "components": {"schemas": {"breakDuration": {"type": "object", "properties": {"autoReturn": {"type": "boolean"}, "duration": {"type": "integer", "format": "int64"}}}, "component": {"type": "object", "properties": {"componentTag": {"type": "integer", "format": "int64"}}}, "deliveryRestrictions": {"type": "object", "properties": {"webDeliveryAllowedFlag": {"type": "boolean"}, "noRegionalBlackoutFlag": {"type": "boolean"}, "archiveAllowedFlag": {"type": "boolean"}, "deviceRestrictions": {"type": "integer", "format": "int64"}}}, "privateCommand": {"type": "object", "properties": {"identifier": {"type": "integer", "format": "int64"}, "privateBytes": {"type": "string"}}}, "program": {"type": "object", "properties": {"utcSpliceTime": {"type": "string"}, "spliceTime": {"$ref": "#/components/schemas/spliceTime"}}}, "segmentationComponent": {"type": "object", "properties": {"ptsOffset": {"type": "integer", "format": "int64"}, "componentTag": {"type": "integer", "format": "int64"}}}, "splice": {"type": "object", "properties": {"spliceEventId": {"type": "string"}, "spliceEventCancelIndicator": {"type": "boolean"}, "outOfNetworkIndicator": {"type": "boolean"}, "spliceImmediateFlag": {"type": "boolean"}, "uniqueProgramId": {"type": "integer", "format": "int64"}, "availNum": {"type": "integer", "format": "int64"}, "availsExpected": {"type": "integer", "format": "int64"}, "program": {"$ref": "#/components/schemas/program"}, "component": {"type": "array", "items": {"$ref": "#/components/schemas/component"}}, "breakDuration": {"$ref": "#/components/schemas/breakDuration"}}}, "spliceAvailDescriptor": {"type": "object", "properties": {"providerAvailId": {"type": "integer", "format": "int64"}}}, "spliceDtmfDescriptor": {"type": "object", "properties": {"preroll": {"type": "integer", "format": "int64"}, "chars": {"type": "string"}}}, "spliceInfoSection": {"type": "object", "properties": {"ptsAdjustment": {"type": "integer", "format": "int64"}, "protocolVersion": {"type": "integer", "format": "int64"}, "tier": {"type": "integer", "format": "int64"}, "spliceSchedule": {"$ref": "#/components/schemas/splice"}, "spliceInsert": {"$ref": "#/components/schemas/splice"}, "timeSignal": {"$ref": "#/components/schemas/timeSignal"}, "privateCommand": {"$ref": "#/components/schemas/privateCommand"}, "availDescriptor": {"type": "array", "items": {"$ref": "#/components/schemas/spliceAvailDescriptor"}}, "dtmfDescriptor": {"type": "array", "items": {"$ref": "#/components/schemas/spliceDtmfDescriptor"}}, "segmentationDescriptor": {"type": "array", "items": {"$ref": "#/components/schemas/spliceSegmentationDescriptor"}}, "timeDescriptor": {"type": "array", "items": {"$ref": "#/components/schemas/spliceTimeDescriptor"}}}}, "spliceSegmentationDescriptor": {"type": "object", "properties": {"segmentationEventId": {"type": "string"}, "segmentationEventCancelIndicator": {"type": "boolean"}, "segmentationDuration": {"type": "integer", "format": "int64"}, "segmentationTypeId": {"type": "integer", "format": "int32"}, "segmentNum": {"type": "integer", "format": "int64"}, "segmentsExpected": {"type": "integer", "format": "int64"}, "subSegmentNum": {"type": "integer", "format": "int64"}, "subSegmentsExpected": {"type": "integer", "format": "int64"}, "deliveryRestrictions": {"$ref": "#/components/schemas/deliveryRestrictions"}, "segmentationUpid": {"type": "array", "items": {"$ref": "#/components/schemas/spliceSegmentationUpid"}}, "component": {"type": "array", "items": {"$ref": "#/components/schemas/segmentationComponent"}}}}, "spliceSegmentationUpid": {"type": "object", "properties": {"segmentationUpidType": {"type": "integer", "format": "int64"}, "formatIdentifier": {"type": "integer", "format": "int64"}, "segmentationUpidFormat": {"type": "string"}, "content": {"type": "string"}}}, "spliceTime": {"type": "object", "properties": {"ptsTime": {"type": "integer", "format": "int64"}}}, "spliceTimeDescriptor": {"type": "object", "properties": {"taiSeconds": {"type": "integer", "format": "int64"}, "taiNs": {"type": "integer", "format": "int64"}, "utcOffset": {"type": "integer", "format": "int64"}}}, "streamMarkerEvent": {"type": "object", "properties": {"counter": {"type": "integer", "format": "int32"}, "spliceTime": {"type": "string", "format": "date-time"}, "acquisitionTime": {"type": "string", "format": "date-time"}, "acquisitionPointIdentity": {"type": "string"}, "zoneIdentity": {"type": "string"}, "signal": {"$ref": "#/components/schemas/streamMarkerSignal"}, "forceScteProcess": {"type": "boolean"}}}, "streamMarkerNotification": {"type": "object", "properties": {"version": {"type": "string"}, "events": {"type": "array", "items": {"$ref": "#/components/schemas/streamMarkerEvent"}}}}, "streamMarkerSignal": {"type": "object", "properties": {"spliceInfoSection": {"$ref": "#/components/schemas/spliceInfoSection"}}}, "timeSignal": {"type": "object", "properties": {"spliceTime": {"$ref": "#/components/schemas/spliceTime"}}}, "AccountsChannelsChannelIdInstanceIdPost204ApplicationJsonResponse": {"type": "string"}, "AccountsChannelsChannelIdInstanceIdPost500ApplicationJsonResponse": {"type": "string"}, "AccountsChannelsChannelIdInstanceIdPost400ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowGet200ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowGet404ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowGet400ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowEnablePut204ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowEnablePut404ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowEnablePut400ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowDisablePut204ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowDisablePut404ApplicationJsonResponse": {"type": "string"}, "ChannelsChannelIdListeningWindowDisablePut400ApplicationJsonResponse": {"type": "string"}}, "securitySchemes": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}]}
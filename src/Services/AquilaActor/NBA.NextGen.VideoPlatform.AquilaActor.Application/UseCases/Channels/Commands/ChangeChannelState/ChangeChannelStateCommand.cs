// "//-----------------------------------------------------------------------".
// <copyright file="ChangeChannelStateCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to change state.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class ChangeChannelStateCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the channel identifier.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the channel instance identifier.
        /// </summary>
        public string InstanceId { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the state of the desired channel.
        /// </summary>
        public AquilaChannelState DesiredChannelState { get; set; }
    }
}

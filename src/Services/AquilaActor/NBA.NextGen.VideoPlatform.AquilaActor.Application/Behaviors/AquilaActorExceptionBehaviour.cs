// "//-----------------------------------------------------------------------".
// <copyright file="AquilaActorExceptionBehaviour.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Behaviors
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Reflection;
    using MediatR.Pipeline;
    using Microsoft.ApplicationInsights.DataContracts;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;

    /// <summary>
    /// VideoPlatform Exception Behaviour.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request.</typeparam>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <typeparam name="TException">The type of the exception.</typeparam>
    /// <seealso cref="MediatR.Pipeline.RequestExceptionHandler&lt;TRequest, TResponse, TException&gt;" />
    [ExcludeFromCodeCoverage]
    public class AquilaActorExceptionBehaviour<TRequest, TResponse, TException> : RequestExceptionHandler<TRequest, TResponse, TException>
        where TException : Exception
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<TRequest> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaActorExceptionBehaviour{TRequest, TResponse, TException}"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        public AquilaActorExceptionBehaviour(ILogger<TRequest> logger, ITelemetryService telemetryService)
        {
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Override in a derived class for the handler logic.
        /// </summary>
        /// <param name="request">Failed request.</param>
        /// <param name="exception">The thrown exception.</param>
        /// <param name="state">The current state of handling the exception.</param>
        /// <exception cref="System.NotSupportedException">Exception.</exception>
        protected override void Handle([NotNull] TRequest request, [NotNull] TException exception, RequestExceptionHandlerState<TResponse> state)
        {
            exception.Required(nameof(exception));
            var exceptionType = exception?.GetType();
            List<PropertyInfo> properties = request?.GetType().GetProperties().ToList<PropertyInfo>();
            PropertyInfo eventIdData = properties?.Find(x => x.Name == "EventId");
            string eventId = eventIdData?.GetValue(request, null)?.ToString();
            if (!string.IsNullOrEmpty(eventId))
            {
                this.telemetryService.TrackException(new ExceptionTelemetry(exception), eventId);
            }

            switch (exceptionType?.Name)
            {
                case nameof(ArgumentNullException):
                    this.logger.LogError(exception, "Argument Null Exception : {message} for Request {Name} {Request}", exception?.Message, typeof(TRequest).Name, request);
                    break;
                case nameof(NullReferenceException):
                    this.logger.LogError(exception, "Null Reference Exception : {message} for Request {Name} {Request}", exception?.Message, typeof(TRequest).Name, request);
                    break;
                default:
                    this.logger.LogError(exception, "{exceptionName} : {message}, for {Name} {Request}", exceptionType?.Name, exception?.Message, typeof(TRequest).Name, request);
                    break;
            }
        }
    }
}

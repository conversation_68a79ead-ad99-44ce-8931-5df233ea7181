// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowTrigger.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function
{
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
    using Microsoft.Extensions.Logging;
    using Microsoft.OpenApi.Models;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Commands.TriggerWorkflow;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;

    /// <summary>
    /// WorkflowTrigger.
    /// </summary>
    public class WorkflowTrigger
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<WorkflowTrigger> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowTrigger"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        public WorkflowTrigger(
            IMediator mediator,
            ILogger<WorkflowTrigger> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Trigger a Workflow for a given LiveEvent and Schedule.
        /// </summary>
        /// <param name="httpRequest">The HTTP request.</param>
        /// <param name="liveEventType">Type of the live event.</param>
        /// <param name="liveEventId">The live event identifier.</param>
        /// <param name="scheduleId">The schedule identifier.</param>
        /// <param name="workflowId">The workflow identifier.</param>
        /// <returns>The task.</returns>
        [FunctionName(nameof(TriggerWorkflowAsync))]
#pragma warning disable CA1825 // Avoid zero-length array allocations
        [OpenApiOperation(operationId: "TriggerWorkflow", Description = "Triggers the execution of a workflow for a given GameID/EventID and ScheduleID", Summary = "Trigger a Workflow for a given LiveEvent and Schedule")]
#pragma warning restore CA1825 // Avoid zero-length array allocations
        [OpenApiParameter(name: "liveEventType", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The live event type")]
        [OpenApiParameter(name: "liveEventId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The live event identifier")]
        [OpenApiParameter(name: "workflowId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The workflow identifier")]
        [OpenApiParameter(name: "scheduleId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The schedule identifier")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Accepted, contentType: "application/json", bodyType: typeof(WorkflowTriggerResponse), Description = "Accepted.")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(string), Description = "Bad Request.")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.NotFound, contentType: "application/json", bodyType: typeof(string), Description = "Not Found.")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.InternalServerError, contentType: "application/json", bodyType: typeof(string), Description = "Internal Server Error.")]
        public async Task<IActionResult> TriggerWorkflowAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "put",
                Route = "orchestrator/{liveEventType}/{liveEventId}/videoplatformschedules/{scheduleId}/workflow/{workflowId}")]
            HttpRequest httpRequest,
            string liveEventType,
            string liveEventId,
            string scheduleId,
            string workflowId)
        {
            this.logger.LogInformation(
                nameof(this.TriggerWorkflowAsync) + " function triggered for workflow {workflowId} of {liveEventType} {liveEventId} with schedule {scheduleId}",
                workflowId,
                liveEventType,
                liveEventId,
                scheduleId);

            var triggerWorkflowCommand = new TriggerWorkflowCommand { LiveEventType = liveEventType, LiveEventId = liveEventId, ScheduleId = scheduleId, WorkflowId = workflowId };

            try
            {
                var requestId = await this.mediator.Send(triggerWorkflowCommand).ConfigureAwait(false);

                return new AcceptedResult(string.Empty, new WorkflowTriggerResponse { RequestId = requestId });
            }
            catch (NotFoundException e)
            {
                return new NotFoundObjectResult(new { ErrorMessage = e.Message });
            }
            catch (ValidationException e)
            {
                var errorMessage = string.Concat(e.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();

                return new BadRequestObjectResult(new { ErrorMessage = errorMessage });
            }
        }
    }
}

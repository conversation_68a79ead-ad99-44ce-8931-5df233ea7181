// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformSchedulesGetter.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
    using Microsoft.Extensions.Logging;
    using Microsoft.OpenApi.Models;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetVideoPlatformSchedules;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    /// The VideoPlatformSchedules Getter.
    /// </summary>
    public class VideoPlatformSchedulesGetter
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<VideoPlatformSchedulesGetter> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="VideoPlatformSchedulesGetter"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        public VideoPlatformSchedulesGetter(
            IMediator mediator,
            ILogger<VideoPlatformSchedulesGetter> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Gets the VideoPlatformSchedule.
        /// </summary>
        /// <param name="httpRequest">The HTTP request.</param>
        /// <param name="liveEventType">Type of the live event.</param>
        /// <param name="liveEventId">The live event identifier.</param>
        /// <returns>The task.</returns>
        [FunctionName(nameof(GetVideoPlatformSchedulesAsync))]
#pragma warning disable CA1825 // Avoid zero-length array allocations
        [OpenApiOperation(operationId: "GetVideoPlatformSchedulesByLiveEventId", Description = "Returns the list of VideoPlatformSchedules for a given Live Event (Game/Event in GMS)", Summary = "Get VideoPlatformSchedules for a given LiveEvent")]
#pragma warning restore CA1825 // Avoid zero-length array allocations
        [OpenApiParameter(name: "liveEventType", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The live event type")]
        [OpenApiParameter(name: "liveEventId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "The live event identifier")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(IEnumerable<VideoPlatformSchedule>), Description = "The OK response")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.InternalServerError, contentType: "application/json", bodyType: typeof(string), Description = "Internal Server Error.")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(string), Description = "Bad Request.")]
        public async Task<IActionResult> GetVideoPlatformSchedulesAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "get",
                Route = "orchestrator/{liveEventType}/{liveEventId}/videoplatformschedules")]
            HttpRequest httpRequest,
            string liveEventType,
            string liveEventId)
        {
            this.logger.LogInformation(nameof(this.GetVideoPlatformSchedulesAsync) + " function triggered for {liveEventType} {liveEventId}", liveEventType, liveEventId);

            var getVideoPlatformSchedulesQuery = new GetVideoPlatformSchedulesQuery { LiveEventType = liveEventType, LiveEventId = liveEventId };

            try
            {
                var videoPlatformSchedules = await this.mediator.Send(getVideoPlatformSchedulesQuery).ConfigureAwait(false);

                return new OkObjectResult(videoPlatformSchedules);
            }
            catch (ValidationException e)
            {
                var errorMessage = string.Concat(e.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();

                return new BadRequestObjectResult(new { ErrorMessage = errorMessage });
            }
        }
    }
}

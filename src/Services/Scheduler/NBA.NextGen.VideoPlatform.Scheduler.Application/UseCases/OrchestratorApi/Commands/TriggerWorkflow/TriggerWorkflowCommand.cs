// "//-----------------------------------------------------------------------".
// <copyright file="TriggerWorkflowCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Commands.TriggerWorkflow
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// The TriggerWorkflow Command.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TriggerWorkflowCommand : IRequest<string>
    {
        /// <summary>
        /// Gets or sets the type of the live event.
        /// </summary>
        /// <value>
        /// The type of the live event.
        /// </value>
        public string LiveEventType { get; set; }

        /// <summary>
        /// Gets or sets the live event identifier.
        /// </summary>
        /// <value>
        /// The live event identifier.
        /// </value>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the schedule identifier.
        /// </summary>
        /// <value>
        /// The schedule identifier.
        /// </value>
        public string ScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the workflow identifier.
        /// </summary>
        /// <value>
        /// The workflow identifier.
        /// </value>
        public string WorkflowId { get; set; }
    }
}

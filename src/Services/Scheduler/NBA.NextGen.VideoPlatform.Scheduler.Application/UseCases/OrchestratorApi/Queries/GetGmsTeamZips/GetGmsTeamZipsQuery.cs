// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsTeamZipsQuery.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsTeamZips
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using Shared.Domain.GMS.Entities;

    /// <summary>
    /// The GetVideoPlatformSchedule by identifier query.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class GetGmsTeamZipsQuery : IRequest<IEnumerable<GmsTeamZips>>
    {
        /// <summary>
        /// Gets or sets the Teams Abbr.
        /// </summary>
        /// <value>
        /// The Teams Abbr.
        /// </value>
        public ICollection<string> TeamsAbbr { get; set; }
    }
}

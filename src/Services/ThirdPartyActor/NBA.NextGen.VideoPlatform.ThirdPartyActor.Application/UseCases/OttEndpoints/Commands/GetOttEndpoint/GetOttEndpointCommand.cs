// <copyright file="GetOttEndpointCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.GetOttEndpoint
{
    using MediatR;
    using NBA.NextGen.Vendor.Api.Quortex;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;

    /// <summary>
    /// Get the channel by id.
    /// </summary>
    public class GetOttEndpointCommand : CorrelatedMessage, IRequest<OttEndpointUpdate>
    {
        /// <summary>
        /// Gets or sets the PoolUuid.
        /// </summary>
        /// <value>
        /// The Pool Uuid.
        /// </value>
        public string PoolUuid { get; set; }

        /// <summary>
        /// Gets or sets the Uuid.
        /// </summary>
        /// <value>
        /// The Uuid.
        /// </value>
        public string Uuid { get; set; }
    }
}

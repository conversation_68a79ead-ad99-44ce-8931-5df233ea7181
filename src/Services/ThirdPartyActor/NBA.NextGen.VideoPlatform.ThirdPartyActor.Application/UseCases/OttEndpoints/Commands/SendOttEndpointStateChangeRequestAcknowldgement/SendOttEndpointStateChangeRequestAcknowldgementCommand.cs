// "//-----------------------------------------------------------------------".
// <copyright file="SendOttEndpointStateChangeRequestAcknowldgementCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to send request acknowledged event.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class SendOttEndpointStateChangeRequestAcknowldgementCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier being acknowledged.
        /// </summary>
        /// <value>
        /// The RequesteId sent in the original request.
        /// </value>
        public string RequestIdAcknowledged { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="RequestBase.RequestorActorId"/> being acknowledged.
        /// </summary>
        /// <value>
        /// The RequestorActorId sent in the original request.
        /// </value>
        public string RequestorActorIdAcknowledged { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public InfrastructureState State { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string WorkflowId { get; set; }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="CreateOttEndpointCommandHandler.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.CreateOttEndpoint
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Vendor.Api.Quortex;
    using NBA.NextGen.VideoPlatform.Shared.Application.ThirdParty.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;

    /// <summary>
    /// Handles getting the channel by id.
    /// </summary>
    public class CreateOttEndpointCommandHandler : IRequestHandler<CreateOttEndpointCommand, Unit>
    {
        /// <summary>
        /// The thirdParty client service.
        /// </summary>
        private readonly IQuortexClientService quortexClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<CreateOttEndpointCommandHandler> logger;

        private readonly IMessageSender<ThirdPartyUpdatedEvent> eventNotifier;

        /// <summary>
        /// Initializes a new instance of the <see cref="CreateOttEndpointCommandHandler" /> class.
        /// </summary>
        /// <param name="quortexClientService">The thirdParty client service.</param>
        /// <param name="logger">The logger.</param>
        public CreateOttEndpointCommandHandler(IQuortexClientService quortexClientService, ILogger<CreateOttEndpointCommandHandler> logger, [NotNull] IMessageSenderFactory eventNotifierProvider)
        {
            this.quortexClientService = quortexClientService;
            this.logger = logger;
            this.eventNotifier = eventNotifierProvider.Resolve<ThirdPartyUpdatedEvent>();
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        [ExcludeFromCodeCoverage]
        public async Task<Unit> Handle([NotNull] CreateOttEndpointCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            var tasks = new List<Task>();
            foreach (var ottEndpoint in request.OttEndpoints)
            {
                var poolUuid = ottEndpoint.PoolUuid;
                var inputUuid = await this.ValidateInputAsync(poolUuid, ottEndpoint.InputUuid);
                var processingUuid = await this.ValidateProcessingAsync(poolUuid, ottEndpoint.ProcessingUuid);
                var targetUuid = await this.ValidateTargetAsync(poolUuid, ottEndpoint.TargetUuid);

                if (inputUuid == null || processingUuid == null || targetUuid == null)
                {
                    this.logger.LogError($"One of the following fields is null when it shouldn't be. Received input value is {inputUuid}, received processing value is {processingUuid}, received target value is {targetUuid}");
                }
                else
                {
                    OttEndpointRequest ottEndpointRequest = new OttEndpointRequest()
                        {
                            Custom_path = ottEndpoint.CustomPath,
                            Enabled = ottEndpoint.Enabled,
                            Start_times = ottEndpoint.StartTime,
                            Input_uuid = inputUuid,
                            Target_uuid = targetUuid,
                            Processing_uuid = processingUuid,
                        };

                    this.logger.LogInformation($"Creating OTT endpoint pool {poolUuid} Custom path {ottEndpoint.CustomPath} Input_Uuid {ottEndpoint.InputUuid} Target uuid {ottEndpoint.TargetUuid} Processing {ottEndpoint.ProcessingUuid}");
                    tasks.Add(this.CreateOttEndpointAsync(poolUuid, ottEndpointRequest, ottEndpoint.ChannelId));
                }
            }

            await Task.WhenAll(tasks.ToArray());
            this.logger.LogInformation($"Succesfully OttEndpoints created EventId {request.OttEndpoints.FirstOrDefault().EventId}");

            return await Unit.Task;
        }

        private async Task<string> ValidateInputAsync(string poolUuid, string input)
        {
            var inputUuidList = await this.GetInputsListAsync(poolUuid);
            var inputUuid = inputUuidList?.Results
                .Where(x => x.Identifier.Equals(input, StringComparison.OrdinalIgnoreCase))
                .Select(x => x.Uuid).FirstOrDefault();
            return inputUuid;
        }

        private async Task<string> ValidateTargetAsync(string poolUuid, string target)
        {
            var targetUuidList = await this.GetTargetsListAsync(poolUuid);
            var inputUuid = targetUuidList?.Results
                .Where(x => x.Identifier.Equals(target, StringComparison.OrdinalIgnoreCase))
                .Select(x => x.Uuid).FirstOrDefault();
            return inputUuid;
        }

        private async Task<string> ValidateProcessingAsync(string poolUuid, string processing)
        {
            var processingUuidList = await this.GetProcessingListAsync(poolUuid);
            var inputUuid = processingUuidList?.Results
                .Where(x => x.Identifier.Equals(processing, StringComparison.OrdinalIgnoreCase))
                .Select(x => x.Uuid).FirstOrDefault();
            return inputUuid;
        }

        private async Task CreateOttEndpointAsync(string poolUuid, OttEndpointRequest ottEndpointRequest, string channelId)
        {
            var thirdPartyUpdatedEvent = new ThirdPartyUpdatedEvent()
            {
                Id = channelId,
            };
            try
            {
                await this.quortexClientService.CreateOttEndpointAsync(poolUuid, ottEndpointRequest);
                thirdPartyUpdatedEvent.State =  ThirdPartyChannelState.Active.ToString();
                await this.eventNotifier.SendAsync(thirdPartyUpdatedEvent);
            }
            catch (Exception e)
            {
                this.logger.LogError($"Error creating endpoint {e.Message}");

                thirdPartyUpdatedEvent.State = ThirdPartyChannelState.Warning.ToString();

                await this.eventNotifier.SendAsync(thirdPartyUpdatedEvent);
            }
        }

        private async Task<PaginatedProcessingList> GetProcessingListAsync(string poolUuid)
        {
            return await this.quortexClientService.GetProcessingListAsync(poolUuid);
        }

        private async Task<PaginatedTargetList> GetTargetsListAsync(string poolUuid)
        {
            return await this.quortexClientService.GetTargetsListAsync(poolUuid);
        }

        private async Task<PaginatedInputList> GetInputsListAsync(string poolUuid)
        {
            return await this.quortexClientService.GetInputsListAsync(poolUuid);
        }
    }
}

<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AzureFunctionsVersion>v4</AzureFunctionsVersion>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <LangVersion>default</LangVersion>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="MediatR" Version="9.0.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
        <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.DurableTask" Version="2.13.4" />
        <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.EventGrid" Version="3.0.0-beta.3" />
        <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.13.5" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
        <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.0" />
        <PackageReference Include="NBA.NextGen.Shared.Domain" Version="2.0.0" />
    </ItemGroup>
    <ItemGroup>
        <None Update="host.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="local.settings.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
      <ProjectReference Include="..\NBA.NextGen.VideoPlatform.ThirdPartyActor.Application\NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.csproj" />
      <ProjectReference Include="..\NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain\NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.csproj" />
    </ItemGroup>
</Project>

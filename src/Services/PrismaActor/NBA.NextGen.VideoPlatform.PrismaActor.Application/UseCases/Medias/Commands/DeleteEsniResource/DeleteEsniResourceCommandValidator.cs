// "//-----------------------------------------------------------------------".
// <copyright file="DeleteEsniResourceCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations
{
    using System.Linq;
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;

    /// <summary>
    /// DeleteEsniResourceCommand Validation.
    /// </summary>
    public class DeleteEsniResourceCommandValidator : AbstractValidator<DeleteEsniResourceCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteEsniResourceCommandValidator" /> class.
        /// </summary>
        public DeleteEsniResourceCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LongRunningOperationId).NotEmpty().WithMessage("LongRunningOperationId cannot be null");
            this.RuleFor(x => x.EsniResourceIds.Any(x => string.IsNullOrEmpty(x))).Equal(false).WithMessage("EsniResourceIds cannot be null");
        }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="PublishPrismaActorHealthCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UserCases.Health.Commands
{
    using NBA.NextGen.VideoPlatform.Shared.Application.UserCases.Health.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// The publish GMS Watchdog service health command.
    /// </summary>
    public class PublishPrismaActorHealthCommand : PublishServiceHealthCommand
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PublishPrismaActorHealthCommand"/> class.
        /// </summary>
        public PublishPrismaActorHealthCommand()
            : base(ServiceNames.PrismaActor)
        {
        }
    }
}

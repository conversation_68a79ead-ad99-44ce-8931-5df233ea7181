using MST.Common.Azure.Extensions;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.Services;
using NBA.NextGen.VideoPlatform.PrismaActor.Application;
using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using Microsoft.Extensions.Options;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
using MST.Common.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using Amazon.SQS;
using MST.Common.AWS.Extensions;
using Azure.Messaging.EventGrid;
using Azure;
using MST.Common.MongoDB.Extensions;
using MongoDB.Driver;

namespace PrismaActor.Processor;

public static class ApplicationExtensions
{
    public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddEventGrid(configuration);
        services.AddApplication(configuration);
        services.AddSharedInfrastructure();
        services.AddVideoPlatformSharedInfrastructure(configuration);
        services.AddHttpClient();
        services.AddBlobClient(configuration);
        services.AddAquilaSharedInfrastructure(configuration);
        services.AddTvpSharedInfrastructure(configuration);
        services.AddPrismaSharedInfrastructure(configuration);
        services.AddRefitClients(configuration);
        configuration["QueueSettings:UseWebSockets"] = "true";
        services.AddServiceHandlers(configuration);
        services.AddTransient<IPrismaClientService, PrismaClientService>();
    }

    public static IServiceCollection AddServiceHandlers(this IServiceCollection services, IConfiguration configuration)
    {

        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var queueName = configuration.GetSection("SQS")["InfrastructureStateChangeRequestPrismaMedias"] ?? throw new NullReferenceException();
        var queueName_dl = configuration.GetSection("SQS")["InfrastructureStateChangeRequestPrismaMedias_DL"] ?? throw new NullReferenceException();
        var eventQueue = configuration.GetSection("SQS")["EventGridEventsOrchestratorQueue"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<InfrastructureStateChangedEventSQS>(eventQueue, _ => Guid.NewGuid().ToString());
        services.RegisterSQSConsumer<MessageReceiverHandler>(queueName, queueName_dl);

        services.AddMSTInfrastructure();

        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.AddHttpClient<CosmosDbHelper>();
        services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
        // Add Event Grid Notifier for VideoPlatform Topic
        var provider = services.BuildServiceProvider();

        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

        var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
        if (vpNotifierSettings is null)
        {
            throw new NullReferenceException();
        }

        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var egClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey);
        services.AddKeyedSingleton(TopicNames.VideoPlatform, egClient);
        services.RegisterEventGridSender<ServiceHealthChangedEvent>(nameof(ServiceHealthChangedEvent), x => nameof(ServiceHealthChangedEvent), keyedServiceName: TopicNames.VideoPlatform);
        services.RegisterEventGridSender<InfrastructureStateChangedEvent>(EventTypes.InfrastructureStateChanged, x => EventTypes.InfrastructureStateChanged, keyedServiceName: TopicNames.VideoPlatform);
        services.RegisterEventGridSender<RequestAcknowledgementEvent>(EventTypes.RequestAcknowledgement, x => EventTypes.RequestAcknowledgement, keyedServiceName: TopicNames.VideoPlatform);

        var dataProviders = provider.GetService<IOptions<DataProviders>>();
        var cosmosDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
        var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");
        if (cosmosDataProvider is null)
        {
            throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
        }

        if (mongoDataProvider is null)
        {
            throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
        }

        var key = configuration["cosmos_connection_string"];

        var cosmosDbClient = new CosmosClient(key, new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ?? 
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(cosmosDbClient);
        services.AddSingleton(mongoDbClient);

        services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
        services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride: $"mongo_{typeof(GmsGame).Name}");
        services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");
        
        services.RegisterCosmosContainer<EsniAudience>(cosmosDataProvider.Database, "EsniAudience", g => g.Id, _ => typeof(EsniAudience).Name, keyOverride: $"cosmos_{typeof(EsniAudience).Name}");
        services.RegisterMongoDBRepository<EsniAudience>(mongoDataProvider.Database, "EsniAudience", g => g.Id, keyOverride: $"mongo_{typeof(EsniAudience).Name}");
        services.RegisterDualWriteRepository<EsniAudience>($"mongo_{typeof(EsniAudience).Name}", $"cosmos_{typeof(EsniAudience).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

        services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformSource).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

        return services;
    }
}

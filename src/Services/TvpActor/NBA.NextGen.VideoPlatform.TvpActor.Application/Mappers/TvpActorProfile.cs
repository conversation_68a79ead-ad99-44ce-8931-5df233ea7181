// "//-----------------------------------------------------------------------".
// <copyright file="TvpActorProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers
{
    using System.Collections.Generic;
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.DeleteEvents;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.DeleteEventSchedules;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.NotifyState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionFromSubscriptions;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionPackage;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsContentState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateContentState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualStartTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatus;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatusOnly;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertLocation;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries;

    /// <summary>
    /// The tvp profile.
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class TvpActorProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TvpActorProfile"/> class.
        /// </summary>
        public TvpActorProfile()
        {
            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCleanupInfo>, RemoveEntitlementsFromProductionCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<TvpEventDeleteInfo, TvpEventCleanupInfo>();

            this.CreateMap<ActorSpecificDetail<TvpEventDeleteInfo>, ActorSpecificDetail<TvpEventCleanupInfo>>();

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventDeleteInfo>, RemoveEntitlementsFromProductionCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventDeleteInfo>, DeleteEventSchedulesCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventDeleteInfo>, DeleteEventCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, AddEntitlementsToProductionCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventUpdateInfo>, UpdateAllProductionsStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ExternalId))
                .ForMember(x => x.ExternalId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.ExternalId))
                .ForMember(x => x.State, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, UpdateAllProductionsStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.ExternalId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.State, opts => opts.Ignore());

            this.CreateMap<UpdateAllProductionsContentStateCommand, TvpProductionContentStates>()
                .ForMember(x => x.EventPartNames, y => y.MapFrom(src => src.EventPartNames));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, UpdateAllProductionsContentStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.ExternalId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.CorrelationId, y => y.MapFrom(src => src.CorrelationId))
                .ForMember(x => x.EventPartNames, y => y.Ignore())
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(src => src.LongRunningOperationId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCleanupInfo>, UpdateAllProductionsStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.ExternalId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.State, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionUpdateStatusInfo>, UpdateProductionStateCommand>()
                .ForMember(x => x.ProductionId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.State, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.State))
                .ForMember(x => x.EventId, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.ValidPreviousState, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data.ValidPreviousState));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, UpsertTeamsCommand>()
                .ForMember(x => x.EnableUpdate, y => y.MapFrom(src => true))
                .ForMember(x => x.TvpEventTeamCreationInfos, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventTeamRoles));

            this.CreateMap<InfrastructureStateChangeRequest<TvpUpdateEventStatus>, UpdateEventStatusCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ExternalId))
                .ForMember(x => x.Data, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, UpdateEventStatusCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.Data, opts => opts.MapFrom(src => src.ActorSpecificDetail.Data));

            this.CreateMap<TvpEventCreationInfo, TvpUpdateEventStatus>()
                .ForMember(x => x.EventStatus, y => y.MapFrom(src => src.EventStatus))
                .ForMember(x => x.ExternalId, opts => opts.MapFrom(src => src.EventExternalId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCleanupInfo>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameStartMarkerModel>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessBroadcastStartMarkerModel>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventDeleteInfo>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpUpdateEventStatus>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ExternalId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionUpdateStatusInfo>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<IList<TvpProductionUpdateStatusInfo>>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.Ignore())
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionPackage>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameStartMarkerModel>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameEndMarkerModel>, SendChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.RequestIdAcknowledged, opts => opts.MapFrom(src => src.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, opts => opts.MapFrom(src => src.RequestorActorId));

            this.CreateMap<SendChangeRequestAcknowldgementCommand, RequestAcknowledgementEvent>()
               .ForMember(x => x.AcknowledgedByActorId, y => y.MapFrom(z => ActorIds.TvpActor));

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCleanupInfo>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameStartMarkerModel>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessBroadcastStartMarkerModel>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventDeleteInfo>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpUpdateEventStatus>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ExternalId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionUpdateStatusInfo>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<IList<TvpProductionUpdateStatusInfo>>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.Ignore())
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionPackage>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameStartMarkerModel>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameEndMarkerModel>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<GmsTeamZips>, SendChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.TeamId))
                .ForMember(x => x.State, y => y.MapFrom(z => z.DesiredState))
                .ForMember(x => x.ErrorMessage, y => y.Ignore());

            this.CreateMap<SendChangeRequestCompletedCommand, InfrastructureStateChangedEvent>()
                .ForMember(x => x.ActorId, y => y.MapFrom(z => ActorIds.TvpActor))
                .ForMember(x => x.Data, y =>
                  {
                      y.PreCondition(src => (!string.IsNullOrEmpty(src.ErrorMessage)));
                      y.MapFrom(z => new Dictionary<string, object> { { "Error", z.ErrorMessage } });
                  });

            this.CreateMap<SendChangeRequestCompletedCommand, InfrastructureStateChangedEventSQS>()
            .ForMember(x => x.ActorId, y => y.MapFrom(z => ActorIds.TvpActor))
            .ForMember(x => x.Data, y =>
                {
                    y.PreCondition(src => (!string.IsNullOrEmpty(src.ErrorMessage)));
                    y.MapFrom(z => new Dictionary<string, object> { { "Error", z.ErrorMessage } });
                });

            this.CreateMap<UpdateAllProductionsStateCommand, TvpProductionUpdateEvent>()
                .ForMember(x => x.State, y => y.MapFrom(src => src.State))
                .ForMember(x => x.IsPrimaryFeed, opts => opts.Ignore())
                .ForMember(x => x.LiveToOnDemand, opts => opts.Ignore())
                .ForMember(x => x.ProductionId, opts => opts.Ignore());

            this.CreateMap<UpdateProductionStateCommand, TvpProductionUpdateEvent>()
                .ForMember(x => x.State, y => y.MapFrom(src => src.State))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ProductionId))
                .ForMember(x => x.LiveToOnDemand, opts => opts.Ignore())
                .ForMember(x => x.IsPrimaryFeed, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest, GetOrchestratorQuery>()
                .ForMember(x => x.WorkflowId, y => y.MapFrom(src => src.WorkflowId));

            this.CreateMap<UpdateProductionStatusCommand, TvpProductionUpdateEvent>()
                .ForMember(x => x.State, y => y.MapFrom(src => src.ProductionStatus))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ProductionId))
                .ForMember(x => x.LiveToOnDemand, opts => opts.Ignore())
                .ForMember(x => x.IsPrimaryFeed, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpEventCreationInfo>, UpsertLocationCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventExternalId));

            this.CreateMap<TvpProductionUpdateStatusInfo, UpdateProductionStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.EventId))
                .ForMember(x => x.LongRunningOperationId, opts => opts.Ignore())
                .ForMember(x => x.CorrelationId, opts => opts.Ignore())
                .ForMember(x => x.ValidPreviousState, opts => opts.Ignore());

            this.CreateMap<NotifyTvpStateChangeCommand, TvpEcmsNotificationInfo>();
            this.CreateMap<TvpEcmsNotificationInfo, NotifyTvpStateChangeCommand>()
                .ForMember(x => x.CorrelationId, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, UpdateProductionStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.State, y => y.MapFrom(_ => TvpProductionStatus.Over))
                .ForMember(x => x.ValidPreviousState, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameEndMarkerModel>, UpdateProductionStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.State, y => y.MapFrom(_ => TvpProductionStatus.Over))
                .ForMember(x => x.ValidPreviousState, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, UpdateEventActualEndTimeCommand>()
                .ForMember(x => x.LiveEventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.ActualEndTime, y => y.MapFrom(src => src.ActorSpecificDetail.Data.MarkerAcquisitionTime));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, UpdateEventStatusOnlyCommand>()
                .ForMember(x => x.LiveEventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.EventStatus, y => y.MapFrom(_ => TvpEventStatus.Final));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>, UpdateEventEndTimeCommand>()
                .ForMember(x => x.LiveEventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.EndTime, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessGameStartMarkerModel>, UpdateProductionStateCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.State, y => y.MapFrom(_ => TvpProductionStatus.Verified))
                .ForMember(x => x.ValidPreviousState, opts => opts.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessBroadcastStartMarkerModel>, UpdateEventActualStartTimeCommand>()
                .ForMember(x => x.LiveEventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.LiveEventId))
                .ForMember(x => x.ActualStartTime, y => y.MapFrom(src => src.ActorSpecificDetail.Data.MarkerAcquisitionTime));

            this.CreateMap<TvpProcessBroadcastStartMarkerDetail, UpdateProductionStateCommand>()
                .ForMember(x => x.State, y => y.MapFrom(_ => TvpProductionStatus.Broadcast))
                .ForMember(x => x.EventId, y => y.Ignore())
                .ForMember(x => x.LongRunningOperationId, opts => opts.Ignore())
                .ForMember(x => x.CorrelationId, opts => opts.Ignore())
                .ForMember(x => x.ValidPreviousState, opts => opts.Ignore());

            this.CreateMap<TvpProcessBroadcastStartMarkerDetail, UpdateContentStateCommand>()
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ProductionId))
                .ForMember(x => x.EventPartNames, y => y.MapFrom(_ => new List<string>() { TvpEventPartNamesProductionContentStates.Game }))
                .ForMember(x => x.CorrelationId, y => y.Ignore())
                .ForMember(x => x.LongRunningOperationId, y => y.Ignore());

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionPackage>, RemoveProductionPackageCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.PackageId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.PackageId));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProductionPackage>, RemoveProductionFromSubscriptionsCommand>()
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId));

            this.CreateMap<UpdateContentStateCommand, TvpProductionContentStates>()
                .ForMember(x => x.EventPartNames, y => y.MapFrom(src => src.EventPartNames));

            this.CreateMap<InfrastructureStateChangeRequest<TvpProcessPostGameStartMarkerModel>, UpdateContentStateCommand>()
                .ForMember(x => x.ProductionId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.ProductionId))
                .ForMember(x => x.EventPartNames, y => y.MapFrom(_ => new List<string>() { TvpEventPartNamesProductionContentStates.Postgame }))
                .ForMember(x => x.CorrelationId, y => y.MapFrom(src => src.CorrelationId))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(src => src.LongRunningOperationId));

            this.CreateMap<InfrastructureStateChangeRequest<GmsTeamZips>, UpdateGeoAreaCommand>()
                .ForMember(x => x.ExternalId, y => y.MapFrom(src => "geoArea" + src.ActorSpecificDetail.Data.Abbr))
                .ForMember(x => x.Abbr, y => y.MapFrom(src => src.ActorSpecificDetail.Data.Abbr))
                .ForMember(x => x.TeamId, y => y.MapFrom(src => src.ActorSpecificDetail.Data.TeamId))
                .ForMember(x => x.Code, y => y.MapFrom(src => src.ActorSpecificDetail.Data.Code))
                .ForMember(x => x.Name, y => y.MapFrom(src => src.ActorSpecificDetail.Data.Name))
                .ForMember(x => x.City, y => y.MapFrom(src => src.ActorSpecificDetail.Data.City))
                .ForMember(x => x.Updated, y => y.MapFrom(src => src.ActorSpecificDetail.Data.Updated))
                .ForMember(x => x.Markets, y => y.MapFrom(src => src.ActorSpecificDetail.Data.Markets))
                .ForMember(x => x.NotificationState, y => y.MapFrom(src => src.ActorSpecificDetail.Data.NotificationState));
        }
    }
}
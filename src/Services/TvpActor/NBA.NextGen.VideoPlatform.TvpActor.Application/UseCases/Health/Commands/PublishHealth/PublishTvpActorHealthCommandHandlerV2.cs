using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MST.Common.Messaging;
using NBA.NextGen.Shared.Application.Services;
using NBA.NextGen.VideoPlatform.Shared.Application.Interfaces.Handlers;
using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
using NBA.NextGen.VideoPlatform.Shared.Application.UserCases.Health.Commands;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Health.Commands.PublishHealth;

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UserCases.Health.Commands;

public class PublishTvpActorHealthCommandHandlerV2 : PublishServiceHealthCommandHandler<PublishTvpActorHealthCommandV2>, IPublishTvpActorHealthCommandHandlerV2
{
    private readonly ITvpClientService tvpClientService;

    public PublishTvpActorHealthCommandHandlerV2(ITvpClientService tvpClientService, IMessageSenderFactory provider, IDateTime dateTimeService, ILogger<PublishTvpActorHealthCommandHandlerV2> logger)
        : base(provider, dateTimeService, logger)
    {
        this.tvpClientService = tvpClientService;
    }

    protected override async Task<bool> GetServiceStatusAsync(PublishTvpActorHealthCommandV2 request)
    {
        await this.tvpClientService.GetAllTeamsAsync(1).ConfigureAwait(false);
        return true;
    }

    Task<bool> IPublishTvpActorHealthCommandHandlerV2.GetServiceStatusAsync(PublishTvpActorHealthCommandV2 request)
    {
        return GetServiceStatusAsync(request);
    }
}

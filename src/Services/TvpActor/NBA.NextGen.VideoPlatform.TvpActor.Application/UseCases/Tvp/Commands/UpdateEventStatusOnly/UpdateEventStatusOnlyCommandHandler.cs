// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventStatusOnlyCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatusOnly
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Handler to update the event status.
    /// </summary>
    public class UpdateEventStatusOnlyCommandHandler : IRequestHandler<UpdateEventStatusOnlyCommand, Unit>
    {
        /// <summary>
        /// The TVP client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateEventStatusOnlyCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventStatusOnlyCommandHandler" /> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        public UpdateEventStatusOnlyCommandHandler(
            ITvpClientService tvpClientService,
            IMapper mapper,
            ILogger<UpdateEventStatusOnlyCommandHandler> logger,
            ITelemetryService telemetryService)
        {
            this.tvpClientService = tvpClientService;
            this.mapper = mapper;
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Command handler logic.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] UpdateEventStatusOnlyCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Getting TVP Event with id {LiveEventId}", request.LiveEventId);
            var liveEvent = await this.tvpClientService.GetEventByIdAsync(request.LiveEventId, true).ConfigureAwait(false);

            if (liveEvent == null)
            {
                this.logger.LogError("Unable to update the EventStatus for Live Event {LiveEventId} because the TVP Event could not be found.", request.LiveEventId);
                throw new TvpEntityNotFoundException<TvpEvent>(request.LiveEventId);
            }

            liveEvent.EventStatus = request.EventStatus;

            var tvpEventUpdateInfo = this.mapper.Map<TvpEventUpdateInfo>(liveEvent);

            this.logger.LogInformation("Updating TVP Event with id {LiveEventId}, Status = {EventStatus}", request.LiveEventId, request.EventStatus);
            try
            {
                await this.tvpClientService.UpdateEventAsync(request.LiveEventId, tvpEventUpdateInfo).ConfigureAwait(false);
                this.logger.LogInformation("Updated TVP Event with id {LiveEventId}, Status = {EventStatus}", request.LiveEventId, request.EventStatus);

                var eventProperties = new Dictionary<string, string>
                {
                    { EventData.CorrelationTag, request.CorrelationId },
                    { EventData.DetailTag, request.EventStatus.ToString() },
                };
                this.telemetryService.TrackEvent(request.LiveEventId, EventTypes.TvpEventStatusUpdated, eventProperties);
            }
            catch (MKTvpClientException ex)
            {
                this.logger.LogError(ex, "Failed to update TVP Event with id {LiveEventId}, Status = {EventStatus}", request.LiveEventId, request.EventStatus);
                throw;
            }

            return Unit.Value;
        }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="GetOrchestratorQueryHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;

    /// <summary>
    /// Returns the name of the Orchestration based on the workflow.
    /// </summary>
    public class GetOrchestratorQueryHandler : IRequestHandler<GetOrchestratorQuery, string>
    {
        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public Task<string> Handle([NotNull] GetOrchestratorQuery request, CancellationToken cancellationToken) => Task.FromResult(request.WorkflowId switch
        {
            NbaWorkflowIds.EventMetadataSetup => OrchestrationNames.SetupTVPWorkflow,
            NbaWorkflowIds.AudienceSetup => OrchestrationNames.UpdateTeamRsnZip,
            NbaWorkflowIds.EventMetadataStart => OrchestrationNames.StartTVPWorkflow,
            NbaWorkflowIds.EventMetadataEnd => OrchestrationNames.EndTVPWorkflow,
            NbaWorkflowIds.EventProductionRemovePackages => OrchestrationNames.RemoveSingleProductionFromPackage,
            NbaWorkflowIds.EventMetadataCleanup => OrchestrationNames.CleanupTVPWorkflow,
            NbaWorkflowIds.EventInfrastructureSetup => OrchestrationNames.UpdateMultipleProductionsStatusOrchestration,
            NbaWorkflowIds.EventInfrastructureStart => OrchestrationNames.UpdateProductionStatusOrchestration,
            NbaWorkflowIds.EventInfrastructureEnd => OrchestrationNames.UpdateProductionStatusOrchestration,
            NbaWorkflowIds.EventReachedTipoffTime => OrchestrationNames.UpdateProductionStatusOrchestration,
            NbaWorkflowIds.EventMetadataDelete => OrchestrationNames.DeleteTVPWorkflow,
            NbaWorkflowIds.ProcessGameStartMarker => OrchestrationNames.ProcessGameStartMarker,
            NbaWorkflowIds.ProcessGameEndMarker => OrchestrationNames.ProcessGameEndMarker,
            NbaWorkflowIds.ProcessBroadcastStartMarker => OrchestrationNames.ProcessBroadcastStartMarker,
            NbaWorkflowIds.ProductionRemovePreGamePackage => OrchestrationNames.RemoveProductionFromPackage,
            NbaWorkflowIds.ProductionRemovePostGamePackage => OrchestrationNames.RemoveProductionFromPackage,
            NbaWorkflowIds.ProcessPostGameStartMarker => OrchestrationNames.ProcessPostGameStartMarker,
            NbaWorkflowIds.ProcessPostGameEndMarker => OrchestrationNames.ProcessPostGameEndMarker,
            _ => throw new NotSupportedException()
        });
    }
}

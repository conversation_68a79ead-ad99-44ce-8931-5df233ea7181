namespace NBA.NextGen.VideoPlatform.TvpActor.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;

    /// <summary>
    /// Receive messages.
    /// </summary>
    public class MessageReceiver : DurableFunctionBase
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<MessageReceiver> logger;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly IDurableClient durableClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageReceiver"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="durableClientFactory">The durable client factory.</param>
        /// <param name="factory">The factory.</param>
        public MessageReceiver(
            IMediator mediator,
            ILogger<MessageReceiver> logger,
            IMapper mapper,
            IDurableClientFactory durableClientFactory,
            IVideoPlatformCorrelationProviderFactory factory)
            : base(durableClientFactory, mediator, logger, mapper, factory)
        {
            this.logger = logger;
            this.durableClient = this.CreateDurableClient(ConnectionNames.TaskHubConnectionName, TaskHubNames.TvpActorHubName);
        }

        /// <summary>
        /// Runs the asynchronous from queue <see cref="ServiceBusOptions.InfrastructureStateChangeRequestMfTvp" />.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>The task.</returns>
        [FunctionName("ReceiveStateChangeRequestMessage")]
        public async Task RunAsync(
            [ServiceBusTrigger("%InfrastructureStateChangeRequestMfTvpQueueName%", Connection = "IntegrationServiceBusConnectionString", IsSessionsEnabled = false)]
            [NotNull] InfrastructureStateChangeRequest message)
        {
            message.Required(nameof(message));
            this.logger.LogInformation(
                "ReceiveMessage function triggered for {actorId} with external system id {Id}",
                message.ActorId,
                message.ExternalSystemInfrastructureId);
            var instanceId = $"{OrchestrationNames.TvpSubscriptionWorkflowInstancePrepend}-{message.RequestId}-{message.InfrastructureId}";

            var instanceStatus = await this.durableClient.GetStatusAsync(instanceId).ConfigureAwait(false);
            this.logger.LogInformation("ReceiveMessage function client created with instance id: {id}", instanceId);

            var oldInput = instanceStatus?.Input.ToObject<InfrastructureStateChangeRequest>();

            if (instanceStatus?.RuntimeStatus == OrchestrationRuntimeStatus.Running && oldInput?.DesiredState == message.DesiredState)
            {
                this.logger.LogError(
                    "There is an Orchestration Instance with Id {InstanceId} already running for ResourceId {PrismaResourceId} with desired state {DesiredState}",
                    instanceId,
                    message.ExternalSystemInfrastructureId,
                    oldInput.DesiredState);
                return;
            }

            this.logger.LogInformation(
                "Initiating workflow: {workflowId} for InstanceId: {instanceId} and external system id {externalId}",
                message.WorkflowId,
                instanceId,
                message.ExternalSystemInfrastructureId);

            var orchestrationName = await this.ProcessAsync<InfrastructureStateChangeRequest, GetOrchestratorQuery, string>(message).ConfigureAwait(false);

            await this.durableClient.StartNewAsync(orchestrationName, instanceId, message).ConfigureAwait(false);

            this.logger.LogInformation(
                "ReceiveMessage function executed for {actorId} with external system id {Id}",
                message.ActorId,
                message.ExternalSystemInfrastructureId);
        }
    }
}

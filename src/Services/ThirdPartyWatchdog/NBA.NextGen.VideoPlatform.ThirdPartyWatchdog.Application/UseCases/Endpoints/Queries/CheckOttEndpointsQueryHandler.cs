// <copyright file="CheckOttEndpointsQueryHandler.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>

namespace NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.UseCases.Endpoints.Queries
{
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.ThirdParty.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;

    /// <summary>
    /// The handler.
    /// </summary>
    public class CheckOttEndpointsQueryHandler : IRequestHandler<CheckOttEndpointsQuery, Unit>
    {
        /// <summary>
        /// The client service.
        /// </summary>
        private readonly IQuortexClientService service;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<CheckOttEndpointsQueryHandler> logger;

        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IObjectRepository<OttEndpoint> repository;

        /// <summary>
        /// The Quortex settings.
        /// </summary>
        private readonly QuortexSettings quortexSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="CheckOttEndpointsQueryHandler"/> class.
        /// </summary>
        /// <param name="service">The quortex service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="options">Options.</param>
        public CheckOttEndpointsQueryHandler(IQuortexClientService service, ILogger<CheckOttEndpointsQueryHandler> logger, IObjectRepositoryFactory repositoryFactory, IOptions<QuortexSettings> options)
        {
            this.service = service;
            this.logger = logger;
            this.repository = repositoryFactory.Resolve<OttEndpoint>();;
            this.quortexSettings = options?.Value;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle(CheckOttEndpointsQuery request, CancellationToken cancellationToken)
        {
            var endpointList = await this.service.ListAllEndpointsAsync(this.quortexSettings.PoolUuid).ConfigureAwait(false);
            foreach (var entry in endpointList)
            {
                var existingEndpoint = await repository.GetItemAsync(entry.Id);
                if (existingEndpoint.Status != entry.Status)
                {
                    await repository.UpdateItemAsync(entry);
                }
            }

            return Unit.Value;
        }
    }
}
// "//-----------------------------------------------------------------------".
// <copyright file="UpdateListeningWindowCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.UpdateListeningWindow
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;

    /// <summary>
    /// Handles sending <see cref="UpdateListeningWindowCommand"/>.
    /// </summary>
    /// <seealso cref="IRequestHandler{StreamMarkerNotifierCommand, Unit}" />
    public class UpdateListeningWindowCommandHandler : IRequestHandler<UpdateListeningWindowCommand, Unit>
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateListeningWindowCommandHandler> logger;

        /// <summary>
        /// Gets the video platform channel repository.
        /// </summary>
        private readonly IObjectRepository<VideoPlatformChannel> videoPlatformChannelRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateListeningWindowCommandHandler" /> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="providerFactory">The provider factory.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        public UpdateListeningWindowCommandHandler(
            ILogger<UpdateListeningWindowCommandHandler> logger,
            [NotNull] IObjectRepositoryFactory repositoryFactory)
        {
            this.logger = logger;
            this.videoPlatformChannelRepository = repositoryFactory.Resolve<VideoPlatformChannel>();
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] UpdateListeningWindowCommand request, CancellationToken cancellationToken)
        {
            this.logger.LogInformation($"Updating the {nameof(VideoPlatformChannel.Scte35ListeningWindowEnabled)} value of Channel {{ChannelId}} to {{Enable}}", request.ChannelId, request.Enable);
            var videoPlatformChannel = await this.videoPlatformChannelRepository.GetItemAsync(request.ChannelId).ConfigureAwait(false);
            if (videoPlatformChannel == null)
            {
                this.logger.LogError($"Could not update {nameof(VideoPlatformChannel.Scte35ListeningWindowEnabled)} because the Channel {{ChannelId}} could not be found", request.ChannelId);
                throw new KeyNotFoundException($"{nameof(VideoPlatformChannel)} not found by id {request.ChannelId}");
            }

            videoPlatformChannel.Scte35ListeningWindowEnabled = request.Enable;
            await this.videoPlatformChannelRepository.UpdateItemAsync(videoPlatformChannel);
            this.logger.LogInformation($"Updated the {nameof(VideoPlatformChannel.Scte35ListeningWindowEnabled)} value of Channel {{ChannelId}} to {{Enable}}", request.ChannelId, request.Enable);

            return Unit.Value;
        }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="IStreamMarkerStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces
{
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;

    /// <summary>
    /// The strategy to process Stream marker interface.
    /// </summary>
    public interface IStreamMarkerStrategy
    {
        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType"/> segmentation type identifier.
        /// </summary>
        public long SegmentationTypeId { get; }

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType"/> segmentation UPID type.
        /// </summary>
        public long SegmentationUpidType { get; }

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType"/> segmentation UPID content.
        /// </summary>
        public string SegmentationUpidContent { get; }

        /// <summary>
        /// Gets the Stream marker segmentation type.
        /// </summary>
        public StreamMarkerSegmentationType StreamMarkerType { get; }

        /// <summary>
        /// Returns if the SCTE marker can be processed by the strategy.
        /// </summary>
        /// <param name="streamMarkerEvent">The Stream marker event.</param>
        /// <returns>A value indicating whether the strategy can process the SCTE marker.</returns>
        public bool CanProcess(StreamMarkerEvent streamMarkerEvent);

        /// <summary>
        /// Requests the change.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="correlationId">The correlation identifier.</param>
        /// <param name="streamMarkerEvent">The Stream marker event.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public Task ProcessStreamMarkerRequestAsync(string channelId, string instanceId, string correlationId, StreamMarkerEvent streamMarkerEvent);
    }
}

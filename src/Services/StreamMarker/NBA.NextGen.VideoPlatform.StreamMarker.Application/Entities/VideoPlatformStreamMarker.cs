// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformStreamMarker.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// The class defining the StreamMarkers received bythe StreamMarkerNotification Service.
    /// </summary>
    /// <seealso cref="VideoPlatformEntity{T}" />
    public class VideoPlatformStreamMarker : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the DateTime when the marker was received.
        /// </summary>
        public DateTimeOffset DateTime { get; set; }

        /// <summary>
        /// Gets or sets the Marker Segmentation Type.
        /// </summary>
        public string MarkerSegmentationType { get; set; }

        /// <summary>
        /// Gets or sets the Marker Segmentation Upid Type.
        /// </summary>
        public string MarkerSegmentationUpidType { get; set; }

        /// <summary>
        /// Gets or sets the Marker Segmentation Upid Content.
        /// </summary>
        public string MarkerSegmentationUpidContent { get; set; }

        /// <summary>
        /// Gets or sets the Marker Type.
        /// </summary>
        public string MarkerType { get; set; }

        /// <summary>
        /// Gets or sets the ChannelId.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the Region InstanceId.
        /// </summary>
        public string InstanceId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a StreamMarker has been Processed or not.
        /// </summary>
        public bool Processed { get; set; }
    }
}

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh | sh

ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS="{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/nbadev/DTC/_packaging/Shared/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Services/StreamMarker/StreamMarker.Api/StreamMarker.Api.csproj", "Services/StreamMarker/StreamMarker.Api/"]
COPY ["Services/StreamMarker/NBA.NextGen.VideoPlatform.StreamMarker.Application/NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.csproj", "Services/StreamMarker/NBA.NextGen.VideoPlatform.StreamMarker.Application/"]

COPY ["Shared/NBA.NextGen.VideoPlatform.Shared.Application/NBA.NextGen.VideoPlatform.Shared.Application.csproj", "Shared/NBA.NextGen.VideoPlatform.Shared.Application/"]
COPY ["Shared/NBA.NextGen.VideoPlatform.Shared.Domain/NBA.NextGen.VideoPlatform.Shared.Domain.csproj", "Shared/NBA.NextGen.VideoPlatform.Shared.Domain/"]
COPY ["Shared/NBA.NextGen.VideoPlatform.Shared.Infrastructure/NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj", "Shared/NBA.NextGen.VideoPlatform.Shared.Infrastructure/"] 
COPY ["Services/Nuget.config", "."]

RUN dotnet restore --configfile ./Nuget.config "Services/StreamMarker/StreamMarker.Api/StreamMarker.Api.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "Services/StreamMarker/StreamMarker.Api/StreamMarker.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Services/StreamMarker/StreamMarker.Api/StreamMarker.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
 && curl -o /usr/local/share/ca-certificates/aws-rds.crt https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem \
 && update-ca-certificates
ENTRYPOINT ["dotnet", "StreamMarker.Api.dll"]
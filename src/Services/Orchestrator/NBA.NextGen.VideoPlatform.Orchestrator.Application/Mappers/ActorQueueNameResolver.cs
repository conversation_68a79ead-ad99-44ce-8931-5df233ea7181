namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    public static class ActorQueueNameResolver
    {
        public static string Resolve(string actorId, [NotNull] ServiceBusOptions serviceBusOptions)
        {
            serviceBusOptions.Required(nameof(serviceBusOptions));
            return actorId switch
            {
                ActorIds.AquilaChannels => serviceBusOptions.InfrastructureStateChangeRequestAquilaChannels,
                ActorIds.TvpActor => serviceBusOptions.InfrastructureStateChangeRequestMfTvp,
                ActorIds.PrismaMedias => serviceBusOptions.InfrastructureStateChangeRequestPrismaMedias,
                ActorIds.Playout => serviceBusOptions.InfrastructureStateChangeRequestPlayouts,
                ActorIds.ThirdPartyActor => serviceBusOptions.InfrastructureStateChangeRequestThirdPartyChannels,
                ActorIds.DmmActor => serviceBusOptions.DmmStateChangeRequest,
                _ => throw new NotSupportedException(),
            };
        }

        public static string Resolve(string actorId, [NotNull] SQSOptions sqoOptions)
        {
            sqoOptions.Required(nameof(sqoOptions));
            return actorId switch
            {
                ActorIds.AquilaChannels => sqoOptions.InfrastructureStateChangeRequestAquilaChannels,
                ActorIds.TvpActor => sqoOptions.InfrastructureStateChangeRequestMfTvp,
                ActorIds.PrismaMedias => sqoOptions.InfrastructureStateChangeRequestPrismaMedias,
                ActorIds.Playout => sqoOptions.InfrastructureStateChangeRequestPlayouts,
                ActorIds.ThirdPartyActor => sqoOptions.InfrastructureStateChangeRequestThirdPartyChannels,
                ActorIds.DmmActor => sqoOptions.DmmStateChangeRequest,
                _ => throw new NotSupportedException(),
            };
        }
    }
}

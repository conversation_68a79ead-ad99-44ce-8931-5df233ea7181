// "//-----------------------------------------------------------------------".
// <copyright file="ReprocessLiveEventsResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Models
{
    using System.Collections.Generic;

    /// <summary>
    /// The model for the <see cref="ReprocessLiveEventsResponse"/> endpoint.
    /// </summary>
    public class ReprocessLiveEventsResponse
    {
        /// <summary>
        /// Gets or sets the <see cref="EntitiesBeingReprocessed"/>.
        /// </summary>
        public IEnumerable<string> EntitiesBeingReprocessed { get; set; }
    }
}

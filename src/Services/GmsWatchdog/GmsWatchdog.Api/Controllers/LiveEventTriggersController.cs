using System.Web.Http;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReingestLiveEvent;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReprocessLiveEvents;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Models;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

namespace GmsWatchdog.Api;

[ApiController]

public class LiveEventTriggersController : ControllerBase
{
    private readonly IMediator _mediator;

    private readonly ILogger<LiveEventTriggersController> _logger;

    public LiveEventTriggersController(IMediator mediator, ILogger<LiveEventTriggersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpPut]
    [Route("orchestrator/{liveEventsType}/reprocess")]
    public async Task<IActionResult> ReprocessLiveEventAsync(string liveEventsType)
    {

        _logger.LogInformation($"{nameof(this.ReprocessLiveEventAsync)} function triggered for {liveEventsType}");

        var reprocessLiveEventsCommand = new ReprocessLiveEventsCommand { LiveEventsType = liveEventsType };

        try
        {
            var reprocessingIds = await _mediator.Send(reprocessLiveEventsCommand).ConfigureAwait(false);
            _logger.LogInformation($"{nameof(this.ReprocessLiveEventAsync)} function completed for {liveEventsType}");

            return new AcceptedResult(string.Empty, new ReprocessLiveEventsResponse { EntitiesBeingReprocessed = reprocessingIds });
        }
        catch (ValidationException ex)
        {
            var errorMessage = string.Concat(ex.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();
            _logger.LogError($"{nameof(this.ReprocessLiveEventAsync)} function failed for {liveEventsType} due to {errorMessage}");

            return new BadRequestObjectResult(new BadRequestResponse { ErrorMessage = errorMessage });
        }
    }

    [HttpPut]
    [Route("orchestrator/{liveEventType}/{liveEventId}/reingest")]
    public async Task<IActionResult> ReingestLiveEventAsync([FromUri] string? leagueId, [FromUri] string? season, string liveEventType, string liveEventId)
    {
        _logger.LogInformation(nameof(this.ReingestLiveEventAsync) + " function triggered for {liveEventType} {liveEventId}", liveEventType, liveEventId);

        ReingestLiveEventCommand reingestLiveEventCommand = new() {
            LiveEventType = liveEventType,
            LiveEventId = liveEventId,
            LeagueId = leagueId,
            Season = season,
        };

        try
        {
            await _mediator.Send(reingestLiveEventCommand).ConfigureAwait(false);

            return new AcceptedResult();
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(new NotFoundResponse { ErrorMessage = e.Message });
        }
        catch (ValidationException e)
        {
            return new BadRequestObjectResult(new BadRequestResponse { ErrorMessage = e.ParseValidationErrors() });
        }
    }
}

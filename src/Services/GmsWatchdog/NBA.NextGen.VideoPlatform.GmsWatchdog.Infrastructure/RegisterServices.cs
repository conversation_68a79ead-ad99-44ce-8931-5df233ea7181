using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.Vendor.Api.Gms;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Mappers;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Services.Context;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure.Services;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using MST.Common.Azure.Extensions;
using MST.Common.Extensions;
using Microsoft.Extensions.Options;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using System.Linq;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model;
using MST.Common.AWS.Extensions;
using Amazon.SQS;
using Azure;
using Azure.Messaging.EventGrid;
using MongoDB.Driver;
using MST.Common.MongoDB.Extensions;
using System.Security.Cryptography.X509Certificates;

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure;

[ExcludeFromCodeCoverage]
public static class RegisterServices
{
    public static IServiceCollection AddPublishers(this IServiceCollection services, IConfiguration configuration)
    {
        // Add Service Bus
        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();

        var gmsWatchdogQueue = configuration.GetSection("SQS")["GmsWatchdogEventing"] ?? throw new NullReferenceException();
        var eventQueue = configuration.GetSection("SQS")["EventGridEventsGmsInterpreterQueue"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<PublishGmsUpdatedMessage>(gmsWatchdogQueue, _ => Guid.NewGuid().ToString());
        services.RegisterSQSSender<GmsUpdatedEventSQS>(eventQueue, _ => Guid.NewGuid().ToString());

        // Add Event Grid Notifier for VideoPlatform Topic
        var provider = services.BuildServiceProvider();
        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

        var gmsNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.GmsWatchdogTopic);
        if (gmsNotifierSettings is null)
        {
            throw new NullReferenceException();
        }
        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var egClient = new EventGridPublisherClient(gmsNotifierSettings.EndpointUri, eventKey);
        services.AddKeyedSingleton(TopicNames.GmsWatchdogTopic, egClient);
        services.RegisterEventGridSender<GmsUpdatedEvent>(x => x.Type.ToNextGenEventType(), x => x.Type.ToNextGenEventType(), keyedServiceName: TopicNames.GmsWatchdogTopic);

        return services;
    }

    public static IServiceCollection AddData(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.AddHttpClient<CosmosDbHelper>();
        var provider = services.BuildServiceProvider();

        var cosmosHelper = provider.GetService<CosmosDbHelper>();
        var dataProviders = provider.GetService<IOptions<DataProviders>>();
        var cosmosDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
        var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");
        if (cosmosDataProvider is null)
        {
            throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
        }

        if (mongoDataProvider is null)
        {
            throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
        }

        var cosmosDbClient = new CosmosClient(configuration["cosmos_connection_string"], new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ?? 
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(cosmosDbClient);
        services.AddSingleton(mongoDbClient);

        services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
        services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride: $"mongo_{typeof(GmsGame).Name}");
        services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");

        services.RegisterCosmosContainer<GmsEvent>(cosmosDataProvider.Database, "GmsEvent", g => g.Id, _ => typeof(GmsEvent).Name, keyOverride: $"cosmos_{typeof(GmsEvent).Name}");
        services.RegisterMongoDBRepository<GmsEvent>(mongoDataProvider.Database, "GmsEvent", g => g.Id, keyOverride: $"mongo_{typeof(GmsEvent).Name}");
        services.RegisterDualWriteRepository<GmsEvent>($"mongo_{typeof(GmsEvent).Name}", $"cosmos_{typeof(GmsEvent).Name}");

        services.RegisterCosmosContainer<GmsTeamZips>(cosmosDataProvider.Database, "GmsTeamZips", g => g.Id, _ => typeof(GmsTeamZips).Name, keyOverride: $"cosmos_{typeof(GmsTeamZips).Name}");
        services.RegisterMongoDBRepository<GmsTeamZips>(mongoDataProvider.Database, "GmsTeamZips", g => g.Id, keyOverride: $"mongo_{typeof(GmsTeamZips).Name}");
        services.RegisterDualWriteRepository<GmsTeamZips>($"mongo_{typeof(GmsTeamZips).Name}", $"cosmos_{typeof(GmsTeamZips).Name}");

        services.RegisterCosmosContainer<GmsWatchdogContext>(cosmosDataProvider.Database, "GmsWatchdogContext", g => g.Id, _ => typeof(GmsWatchdogContext).Name, keyOverride: $"cosmos_{typeof(GmsWatchdogContext).Name}");
        services.RegisterMongoDBRepository<GmsWatchdogContext>(mongoDataProvider.Database, "GmsWatchdogContext", g => g.Id, keyOverride: $"mongo_{typeof(GmsWatchdogContext).Name}");
        services.RegisterDualWriteRepository<GmsWatchdogContext>($"mongo_{typeof(GmsWatchdogContext).Name}", $"cosmos_{typeof(GmsWatchdogContext).Name}");

        services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformSource).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

        services.RegisterCosmosContainer<EsniAudience>(cosmosDataProvider.Database, "EsniAudience", g => g.Id, _ => typeof(EsniAudience).Name, keyOverride: $"cosmos_{typeof(EsniAudience).Name}");
        services.RegisterMongoDBRepository<EsniAudience>(mongoDataProvider.Database, "EsniAudience", g => g.Id, keyOverride: $"mongo_{typeof(EsniAudience).Name}");
        services.RegisterDualWriteRepository<EsniAudience>($"mongo_{typeof(EsniAudience).Name}", $"cosmos_{typeof(EsniAudience).Name}");

        services.RegisterCosmosContainer<GmsEntitlementRules>(cosmosDataProvider.Database, "GmsEntitlementRules", g => g.Id, _ => typeof(GmsEntitlementRules).Name, keyOverride: $"cosmos_{typeof(GmsEntitlementRules).Name}");
        services.RegisterMongoDBRepository<GmsEntitlementRules>(mongoDataProvider.Database, "GmsEntitlementRules", g => g.Id, keyOverride: $"mongo_{typeof(GmsEntitlementRules).Name}");
        services.RegisterDualWriteRepository<GmsEntitlementRules>($"mongo_{typeof(GmsEntitlementRules).Name}", $"cosmos_{typeof(GmsEntitlementRules).Name}");

        services.RegisterCosmosContainer<VideoPlatformWorkflow>(cosmosDataProvider.Database, "VideoPlatformWorkflow", g => g.Id, _ => typeof(VideoPlatformWorkflow).Name, keyOverride: $"cosmos_{typeof(VideoPlatformWorkflow).Name}");
        services.RegisterMongoDBRepository<VideoPlatformWorkflow>(mongoDataProvider.Database, "VideoPlatformWorkflow", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformWorkflow).Name}");
        services.RegisterDualWriteRepository<VideoPlatformWorkflow>($"mongo_{typeof(VideoPlatformWorkflow).Name}", $"cosmos_{typeof(VideoPlatformWorkflow).Name}");

        services.RegisterCosmosContainer<GmsEntitlement>(cosmosDataProvider.Database, "GmsEntitlement", g => g.Id, _ => typeof(GmsEntitlement).Name, keyOverride: $"cosmos_{typeof(GmsEntitlement).Name}");
        services.RegisterMongoDBRepository<GmsEntitlement>(mongoDataProvider.Database, "GmsEntitlement", g => g.Id, keyOverride: $"mongo_{typeof(GmsEntitlement).Name}");
        services.RegisterDualWriteRepository<GmsEntitlement>($"mongo_{typeof(GmsEntitlement).Name}", $"cosmos_{typeof(GmsEntitlement).Name}");

        services.RegisterCosmosContainer<VideoPlatformSchedule>(cosmosDataProvider.Database, "VideoPlatformSchedule", g => g.Id, _ => typeof(VideoPlatformSchedule).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSchedule).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSchedule>(mongoDataProvider.Database, "VideoPlatformSchedule", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformSchedule).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSchedule>($"mongo_{typeof(VideoPlatformSchedule).Name}", $"cosmos_{typeof(VideoPlatformSchedule).Name}");

        services.RegisterCosmosContainer<VideoPlatformBlackout>(cosmosDataProvider.Database, "VideoPlatformBlackout", g => g.Id, _ => typeof(VideoPlatformBlackout).Name, keyOverride: $"cosmos_{typeof(VideoPlatformBlackout).Name}");
        services.RegisterMongoDBRepository<VideoPlatformBlackout>(mongoDataProvider.Database, "VideoPlatformBlackout", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformBlackout).Name}");
        services.RegisterDualWriteRepository<VideoPlatformBlackout>($"mongo_{typeof(VideoPlatformBlackout).Name}", $"cosmos_{typeof(VideoPlatformBlackout).Name}");

        
        return services;
    }

    /// <summary>
    /// Adds the infrastructure.
    /// </summary>
    /// <param name="serviceCollection">The service collection.</param>
    /// <param name="configuration">The configuration.</param>
    public static void AddInfrastructure(this IServiceCollection serviceCollection, [NotNull] IConfiguration configuration)
    {
        configuration.Required(nameof(configuration));
        serviceCollection.AddTransient<IGmsClientService, GmsClientService>();
        serviceCollection.AddTransient<IGameSeasonService, GameSeasonService>();
        serviceCollection.AddTransient<IGmsWatchdogContextService, GmsWatchdogContextService>();

        serviceCollection.Configure<GmsWatchdogOptions>(configuration.GetSection("GmsWatchdog"));

        serviceCollection.AddSharedInfrastructure();
        serviceCollection.AddVideoPlatformSharedInfrastructure(configuration);
        serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new GmsWatchdogProfile()));
        serviceCollection.AddGmsClient(configuration);
        serviceCollection.AddEventGrid(configuration);
        serviceCollection.AddBlobClient(configuration);
        serviceCollection.AddHttpClient();

        // Add MST Common Infra and publishers...
        serviceCollection.AddMSTInfrastructure();
        serviceCollection.AddPublishers(configuration);
        serviceCollection.AddData(configuration);

    }
}

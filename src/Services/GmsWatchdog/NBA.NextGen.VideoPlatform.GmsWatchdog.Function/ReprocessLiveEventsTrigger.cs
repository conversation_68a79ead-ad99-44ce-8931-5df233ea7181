// "//-----------------------------------------------------------------------".
// <copyright file="ReprocessLiveEventsTrigger.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function
{
    using System.Linq;
    using System.Net;
    using System.Net.Mime;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
    using Microsoft.Extensions.Logging;
    using Microsoft.OpenApi.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReprocessLiveEvents;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using EventType = Shared.Domain.GMS.Enums.EventType;

    /// <summary>
    /// The <see cref="ReprocessLiveEventsTrigger"/>.
    /// </summary>
    public class ReprocessLiveEventsTrigger
    {
        /// <summary>
        /// The <see cref="IMediator"/>.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The <see cref="ILogger"/>.
        /// </summary>
        private readonly ILogger<ReprocessLiveEventsTrigger> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReprocessLiveEventsTrigger"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        public ReprocessLiveEventsTrigger(
            IMediator mediator,
            ILogger<ReprocessLiveEventsTrigger> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Triggers the Orchestrator operation to reprocess all the <see cref="GmsGame"/>s or all the <see cref="GmsEvent"/>s with DateTime after now.
        /// </summary>
        /// <param name="httpRequest">The <see cref="HttpRequest"/>.</param>
        /// <param name="liveEventsType">String for <see cref="EventType.Game"/> or <see cref="EventType.Event"/>.</param>
        /// <returns>The task.</returns>
        [FunctionName(nameof(ReprocessLiveEventsAsync))]
#pragma warning disable CA1825
        [OpenApiOperation(operationId: "ReprocessLiveEvents", Summary = "Reprocess live events", Description = "Triggers the Orchestrator operation to reprocess all the games or all the events with DateTime after now")]
#pragma warning restore CA1825
        [OpenApiParameter(name: "liveEventsType", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "Game or Event")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Accepted, contentType: MediaTypeNames.Application.Json, bodyType: typeof(ReprocessLiveEventsResponse), Description = "List of live event identifiers that are being reprocessed")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: MediaTypeNames.Application.Json, bodyType: typeof(BadRequestResponse))]
        public async Task<IActionResult> ReprocessLiveEventsAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "put",
                Route = "orchestrator/{liveEventsType}/reprocess")]
            HttpRequest httpRequest,
            string liveEventsType)
        {
            this.logger.LogInformation($"{nameof(this.ReprocessLiveEventsAsync)} function triggered for {liveEventsType}");

            var reprocessLiveEventsCommand = new ReprocessLiveEventsCommand { LiveEventsType = liveEventsType };

            try
            {
                var reprocessingIds = await this.mediator.Send(reprocessLiveEventsCommand).ConfigureAwait(false);
                this.logger.LogInformation($"{nameof(this.ReprocessLiveEventsAsync)} function completed for {liveEventsType}");

                return new AcceptedResult(string.Empty, new ReprocessLiveEventsResponse { EntitiesBeingReprocessed = reprocessingIds });
            }
            catch (ValidationException ex)
            {
                var errorMessage = string.Concat(ex.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();
                this.logger.LogError($"{nameof(this.ReprocessLiveEventsAsync)} function failed for {liveEventsType} due to {errorMessage}");

                return new BadRequestObjectResult(new BadRequestResponse { ErrorMessage = errorMessage });
            }
        }
    }
}

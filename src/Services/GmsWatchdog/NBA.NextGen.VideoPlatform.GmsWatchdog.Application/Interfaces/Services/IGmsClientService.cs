// "//-----------------------------------------------------------------------".
// <copyright file="IGmsClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using NBA.NextGen.Shared.Application.HealthDetails;
    using NBA.NextGen.Vendor.Api.Gms;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The game service.
    /// </summary>
    public interface IGmsClientService : IHealthStatus
    {
        /// <summary>
        /// Gets a game.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="leagueId">The <see cref="LeagueID"/>.</param>
        /// <param name="season">The season.</param>
        /// <returns>The game.</returns>
        Task<GmsGame> GetGameAsync([NotNull] string id, LeagueID? leagueId = null, string season = null);

        /// <summary>
        /// Gets an event.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="leagueId">The <see cref="LeagueID"/>.</param>
        /// <param name="season">The season.</param>
        /// <returns>The event.</returns>
        Task<GmsEvent> GetEventAsync(string id, LeagueID? leagueId = null, string season = null);

        /// <summary>
        /// Gets the games since the last update.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The games since the last update.</returns>
        Task<IList<GmsGame>> GetGamesAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts);

        /// <summary>
        /// Gets the events since the last update.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The events since the last update.</returns>
        Task<IList<GmsEvent>> GetEventsAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts);

        /// <summary>
        /// Gets the TeamBlackoutZips since the last update.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>
        /// The TeamBlackoutZips since the last update.
        /// </returns>
        Task<IList<GmsTeamZips>> GetTeamBlackoutZipsAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts);

        /// <summary>
        /// Gets the games for today. Can be also used, for example, to probe if the GMS API is working.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The games for today.</returns>
        Task<IList<GmsGame>> GetGamesForTodayAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts);

        /// <summary>
        /// Gets the events for today. Can be also used, for example, to probe if the GMS API is working.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The events for today.</returns>
        Task<IList<GmsEvent>> GetEventsForTodayAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts);

        /// <summary>
        /// Gets the league season games snapshot asynchronous.
        /// </summary>
        /// <param name="leagueSeason">The league season.</param>
        /// <returns>The game snapshot.</returns>
        Task<GameSnapshot> GetLeagueSeasonGamesSnapshotAsync(LeagueSeason leagueSeason);
    }
}

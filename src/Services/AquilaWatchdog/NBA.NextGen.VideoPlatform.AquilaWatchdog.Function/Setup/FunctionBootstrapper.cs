// "//-----------------------------------------------------------------------".
// <copyright file="FunctionBootstrapper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Function.Setup
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using Azure.Identity;
    using Azure.Messaging.ServiceBus;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Options;
    using MST.Common.Azure.Extensions;
    using MST.Common.Extensions;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Infrastructure;
    using NBA.NextGen.Shared.Infrastructure.EventGrid;
    using NBA.NextGen.Vendor.Api.MkAquila;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.Shared.Application;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using MST.Common.Azure.Data;
    using Microsoft.Azure.Cosmos;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
    using NBA.NextGen.Shared.Infrastructure.Data;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;

    /// <summary>
    /// The transform bootstrap service.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class FunctionBootstrapper
    {
        /// <summary>
        /// The services.
        /// </summary>
        private readonly IServiceCollection services;

        /// <summary>
        /// The configuration.
        /// </summary>
        private readonly IConfiguration configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="FunctionBootstrapper"/> class.
        /// </summary>
        /// <param name="services">the services.</param>
        /// <param name="configuration">the configuration.</param>
        public FunctionBootstrapper(IServiceCollection services, IConfiguration configuration)
        {
            this.services = services;
            this.configuration = configuration;
        }

        /// <summary>
        /// Registers the services.
        /// </summary>
        public void RegisterServices()
        {
            this.services.Required(nameof(this.services));
            this.services.AddAzureAppConfiguration();
            this.services.AddApplication();
            this.AddInfrastructure(this.configuration);
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
        }

        /// <summary>
        /// Adds the infrastructure.
        /// </summary>
        /// <param name="configuration">The configuration.</param>
        private void AddInfrastructure([NotNull] IConfiguration configuration)
        {
            configuration.Required(nameof(configuration));

            this.services.AddHttpClient();

            // Add shared services.
            this.services.AddAquilaClient(configuration);
            this.services.AddQueueClient(configuration);

            this.services.AddEventGrid(configuration);
            this.services.AddSharedInfrastructure();
            this.services.AddVideoPlatformSharedInfrastructure(configuration);
            this.services.AddAquilaSharedInfrastructure(configuration);

            this.services.AddMSTInfrastructure();

            services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
            services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
            services.AddHttpClient<CosmosDbHelper>();
            var provider = services.BuildServiceProvider();

            var queueFQDN = configuration.GetSection("QueueSettings")["Namespace"];
            queueFQDN += ".servicebus.windows.net";
            var sbClient = new ServiceBusClient(queueFQDN, new DefaultAzureCredential(), new ServiceBusClientOptions
            {
                TransportType = ServiceBusTransportType.AmqpWebSockets,
            });

            services.AddSingleton(sbClient);

            var aquilaQueue = configuration.GetSection("ServiceBus")["AquilaWatchdogEventing"] ?? throw new NullReferenceException();
            services.RegisterServiceBusQueueSender<PublishAquilaUpdatedMessage>(aquilaQueue);

            // Add Event Grid Notifier for VideoPlatform Topic
            var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

            var aqNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.AquilaWatchdogTopic);
            if (aqNotifierSettings is null)
            {
                throw new NullReferenceException();
            }

            var aqEGClient = new Azure.Messaging.EventGrid.EventGridPublisherClient(aqNotifierSettings.EndpointUri, new DefaultAzureCredential());
            services.AddKeyedSingleton(TopicNames.AquilaWatchdogTopic, aqEGClient);
            services.RegisterEventGridSender<AquilaUpdatedEvent>(x => x.Type.ToNextGenEventType(), x => EventTypes.AquilaChannelUpdated, keyedServiceName: TopicNames.AquilaWatchdogTopic);

            var cosmosHelper = provider.GetService<CosmosDbHelper>();
            var dataProviders = provider.GetService<IOptions<DataProviders>>();
            var dataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
            if (dataProvider is null)
            {
                throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
            }
            var key = configuration["cosmos_connection_string"];

            var dbClient = new CosmosClient(key, new CosmosClientOptions
            {
                Serializer = new CosmosDataSerializer(),
                ConnectionMode = ConnectionMode.Gateway
            });

            services.AddSingleton(dbClient);
            services.RegisterCosmosContainer<Channel>(dataProvider.Database, "Channel", g => g.Id, _ => typeof(Channel).Name);
            services.RegisterCosmosContainer<Source>(dataProvider.Database, "Source", g => g.Id, _ => typeof(Source).Name);
            services.RegisterCosmosContainer<Whitelist>(dataProvider.Database, "Whitelist", g => g.Id, _ => typeof(Whitelist).Name);
            services.RegisterCosmosContainer<GmsGame>(dataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name);
            services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(dataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name);
            services.RegisterCosmosContainer<VideoPlatformTemplate>(dataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name);
            services.RegisterCosmosContainer<VideoPlatformSource>(dataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name);
        }
    }
}

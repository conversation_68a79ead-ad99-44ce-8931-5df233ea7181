// "//-----------------------------------------------------------------------".
// <copyright file="GmsInterpreterService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Services
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Reflection;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Azure.Cosmos;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configurations;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Playout;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Constants;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;

    /// <summary>
    /// GMS Interpreter service.
    /// </summary>
    ///
#pragma warning disable CA1506
    public class GmsInterpreterService : IGmsInterpreterService
    {
        /// <summary>
        /// The default encoder identifier.
        /// </summary>
        private const int DefaultEncoderId = 10000;

        /// <summary>
        /// Repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<GmsInterpreterService> logger;

        /// <summary>
        /// mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The thirdParty Endpoint  creation options.
        /// </summary>
        private readonly IOptions<ThirdPartyEndpointCreationOptions> thirdPartyEndpointCreationOptions;

        /// <summary>
        /// The aquila channel creation options.
        /// </summary>
        private readonly IOptions<AquilaChannelCreationOptions> aquilaChannelCreationOptions;

        /// <summary>
        /// The TVP event creation options.
        /// </summary>
        private readonly IOptions<TvpEventCreationOptions> tvpEventCreationOptions;

        /// <summary>
        /// The TVP event creation options.
        /// </summary>
        private readonly IOptions<DmmCreationOptions> dmmCreationOptions;

        /// <summary>
        /// The ESNI Resources creation options.
        /// </summary>
        private readonly IOptions<EsniResourcesCreationOptions> esniResourcesCreationOptions;

        /// <summary>
        /// <see cref="CustomWorkflowOffsetOptions"/>.
        /// </summary>
        private readonly CustomWorkflowOffsetOptions customOffsetOptions;

        /// <summary>
        /// <see cref="WorkflowOffsetSettings"/>.
        /// </summary>
        private readonly WorkflowOffsetSettings workflowOffsetSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="GmsInterpreterService" /> class.
        /// </summary>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="thirdPartyEndpointCreationOptions">The ThirdParty Endpoint creation options.</param>
        /// <param name="aquilaChannelCreationOptions">The aquila channel creation options.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <param name="esniResourcesCreationOptions">The ESNI Resources creation options.</param>
        /// <param name="dmmCreationOptions">The Dmm creation options.</param>
        /// <param name="customOffsetOptions"><see cref="CustomWorkflowOffsetOptions"/>.</param>
        /// <param name="workflowOffsetSettings">the workflowOffsetSettings.</param>
        public GmsInterpreterService(
            IQueryableRepositoryFactory repositoryFactory,
            ILogger<GmsInterpreterService> logger,
            IMapper mapper,
            IDateTime dateTimeService,
            IOptions<AquilaChannelCreationOptions> aquilaChannelCreationOptions,
            IOptions<ThirdPartyEndpointCreationOptions> thirdPartyEndpointCreationOptions,
            IOptions<TvpEventCreationOptions> tvpEventCreationOptions,
            IOptions<EsniResourcesCreationOptions> esniResourcesCreationOptions,
            IOptions<DmmCreationOptions> dmmCreationOptions,
            [NotNull] IOptions<CustomWorkflowOffsetOptions> customOffsetOptions,
            [NotNull] IOptions<WorkflowOffsetSettings> workflowOffsetSettings)
        {
            customOffsetOptions.Required(nameof(customOffsetOptions));
            this.repositoryFactory = repositoryFactory;
            this.logger = logger;
            this.mapper = mapper;
            this.dateTimeService = dateTimeService;
            this.aquilaChannelCreationOptions = aquilaChannelCreationOptions;
            this.thirdPartyEndpointCreationOptions = thirdPartyEndpointCreationOptions;
            this.tvpEventCreationOptions = tvpEventCreationOptions;
            this.esniResourcesCreationOptions = esniResourcesCreationOptions;
            this.dmmCreationOptions = dmmCreationOptions;
            this.customOffsetOptions = customOffsetOptions.Value;
            this.workflowOffsetSettings = workflowOffsetSettings.Value;
        }

        /// <summary>
        /// Gets a <see cref="ScheduleChangeRequest"/> for a GmsEntity.
        /// </summary>
        /// <param name="gmsEntity">Gms Entity.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="correlationId">correlation Id.</param>
        /// <returns>ScheduleChangeRequest message.</returns>
        public async Task<ScheduleChangeRequest> GetMetadataWorkflowScheduleRequestAsync([NotNull] GmsEntity gmsEntity, string requestorEventType, string correlationId)
        {
            this.logger.LogInformation($"Getting Metadata Workflow schedule {gmsEntity.Id}");
            gmsEntity.Required(nameof(gmsEntity));
            var leagueId = gmsEntity.Id.Substring(0, 2);
            var existingVideoPlatformSchedule = await this.GetVideoPlatformScheduleAsync(gmsEntity.Id, VideoPlatformScheduleLevel.Game).ConfigureAwait(false);

            await this.UpsertEntitlementsForMediasAsync(gmsEntity).ConfigureAwait(false);

            await this.UpsertVideoPlatformBlackoutAsync(gmsEntity).ConfigureAwait(false);

            var scheduleChangeRequest = this.mapper.Map<GmsEntity, ScheduleChangeRequest>(gmsEntity, opts =>
            {
                opts.Items[nameof(ScheduleChangeRequest.RequestorEventType)] = requestorEventType;
                opts.Items[nameof(ScheduleChangeRequest.RequestorLiveEventScheduleId)] = VideoPlatformScheduleLevel.Game;
                opts.Items[nameof(ScheduleChangeRequest.ExistingScheduleId)] = existingVideoPlatformSchedule?.Id;
                opts.Items[nameof(ScheduleChangeRequest.CorrelationId)] = correlationId;
            });

            if (!this.dmmCreationOptions.Value.NonNextGenLeague.Contains(leagueId, StringComparison.Ordinal))
            {
                this.AddWorkflowIntentToScheduleChangeRequest(await this.GetMetadataSetupWorkflowIntentAsync(gmsEntity, existingVideoPlatformSchedule).ConfigureAwait(false), scheduleChangeRequest);

                if (gmsEntity.HasActiveNssMediasWithActiveSchedules && (gmsEntity.IsEvent || gmsEntity.HasScheduleCodeOk))
                {
                    this.AddWorkflowIntentToScheduleChangeRequest(this.GetMetadataStartWorkflowIntent(gmsEntity), scheduleChangeRequest);
                    this.AddWorkflowIntentToScheduleChangeRequest(this.GetMetadataEndWorkflowIntent(gmsEntity), scheduleChangeRequest);
                    this.AddWorkflowIntentToScheduleChangeRequest(await this.GetMetadataCleanupWorkflowIntentAsync(gmsEntity).ConfigureAwait(false), scheduleChangeRequest);

                    // We are adding infra setup here because it is specific to the game as it is in bulk.
                    this.AddWorkflowIntentToScheduleChangeRequest(this.GetInfrastructureSetupWorkflowIntent(gmsEntity, null), scheduleChangeRequest);
                }
            }

            this.AddMultipleWorkflowIntentToScheduleChangeRequest(this.GetContentProtectionWorkflowsIntent(gmsEntity), scheduleChangeRequest);
            return scheduleChangeRequest;
        }

        /// <summary>
        /// Upsert the Dmm workflows.
        /// </summary>
        /// <param name="gmsEntity">The GmsEndity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public ICollection<WorkflowIntent> GetContentProtectionWorkflowsIntent([NotNull] GmsEntity gmsEntity)
        {
            gmsEntity.Required(nameof(gmsEntity));
            if (gmsEntity.IsEvent)
            {
                return new List<WorkflowIntent>();
            }

            var gmsGame = gmsEntity as GmsGame;
            var workFlowIntents = new List<WorkflowIntent>();

            if (gmsGame.ContentProtectionUrls != null && gmsGame.ContentProtectionUrls.Any())
            {
                ContentProtection contentProtection = new ContentProtection() { GameId = gmsGame.Id };

                List<ClientContentProtection> clients = new List<ClientContentProtection>();

                foreach (var contentProtectionUrl in gmsGame.ContentProtectionUrls?.Where(url => url.Input != null))
                {
                    ClientContentProtection clientProtection = clients.Find(client => client.Name == contentProtectionUrl.Name);
                    ClientContentProtection clientContentProtection = clientProtection ?? new ClientContentProtection() { Name = contentProtectionUrl.Name, Streams = new List<StreamContentProtection>() };
                    StreamContentProtection stream = this.mapper.Map<StreamContentProtection>(contentProtectionUrl);
                    clientContentProtection.Streams.Add(stream);
                    if (clientProtection == null)
                    {
                        clients.Add(clientContentProtection);
                    }
                }

                contentProtection.Clients = clients;

                var offsets = gmsEntity.GetIntentMetaDatas(this.customOffsetOptions, this.esniResourcesCreationOptions.Value, this.aquilaChannelCreationOptions.Value, this.thirdPartyEndpointCreationOptions.Value);

                workFlowIntents.Add(new WorkflowIntent
                {
                    WorkflowId = NbaWorkflowIds.EventContentProtectionStart,
                    LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                    ChannelId = gmsEntity.Id,
                    ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForDmmActor(contentProtection),
                    },
                    WorkflowOffset = SetWorkflowOffset(offsets, NbaWorkflowIds.EventContentProtectionStart)
                });

                workFlowIntents.Add(new WorkflowIntent
                {
                    WorkflowId = NbaWorkflowIds.EventContentProtectionStop,
                    LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                    ChannelId = gmsEntity.Id,
                    ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForDmmActor(contentProtection),
                    },
                    WorkflowOffset = SetWorkflowOffset(offsets, NbaWorkflowIds.EventContentProtectionStop)
                });
            }

            return workFlowIntents;
        }

        /// <summary>
        /// Upsert the blackout service information.
        /// </summary>
        /// <param name="gmsEntity">The gmsEntity.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        public async Task UpsertVideoPlatformBlackoutAsync([NotNull] GmsEntity gmsEntity)
        {
            var blackoutRepository = this.repositoryFactory.Resolve<VideoPlatformBlackout>();
            var currentBlackouts = await blackoutRepository.GetItemsAsync(x => x.Id.Contains(gmsEntity.Id)).ConfigureAwait(false);

            var currentBlackoutsIds = new Collection<string>();
            if (currentBlackouts != null && currentBlackouts.Any())
            {
                currentBlackouts.ForEach(x => currentBlackoutsIds.Add(x.Id));
            }

            this.logger.LogInformation($"{currentBlackouts?.Count()} - {currentBlackoutsIds.Count} found blackouts for {gmsEntity.Id}");

            var blackouts = new Collection<VideoPlatformBlackout>();
            var blackoutsToUpdate = new Collection<VideoPlatformBlackout>();

            (blackouts, blackoutsToUpdate) = gmsEntity.GetBlackoutServiceInfo(this.esniResourcesCreationOptions.Value, currentBlackoutsIds);

            this.logger.LogInformation($"Creating {blackouts.Count}, updating {blackoutsToUpdate.Count} blackouts for {gmsEntity.Id}");

            try
            {
                if (blackouts.Any())
                {
                    foreach (var blackout in blackouts)
                    {
                        await blackoutRepository.CreateItemAsync(blackout).ConfigureAwait(false);
                    }

                }

                if (blackoutsToUpdate.Any())
                {
                    foreach (var blackout in blackoutsToUpdate)
                    {
                        await blackoutRepository.UpdateItemAsync(blackout).ConfigureAwait(false);
                    }

                }
            }
            catch (NullReferenceException e)
            {
                this.logger.LogError($"Null Reference UpsertingVideoPlatformBlackout: {e.Message}");
            }
            catch (CosmosException e)
            {
                this.logger.LogError($"Cosmos Exception: {e.Message}");
            }
        }

        /// <summary>
        /// Gets a <see cref="ScheduleChangeRequest"/> for a GmsEntity that needs to be rolled back.
        /// </summary>
        /// <param name="gmsEntity">Gms Entity.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="correlationId">correlation Id.</param>
        /// <returns>ScheduleChangeRequest message.</returns>
        public async Task<ScheduleChangeRequest> GetRollbackScheduleChangeRequestAsync([NotNull] GmsEntity gmsEntity, string requestorEventType, string correlationId)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var videoPlatformSchedule = await this.GetVideoPlatformScheduleAsync(gmsEntity.Id).ConfigureAwait(false);
            var videoPlatformScheduleId = videoPlatformSchedule.Id;
            var scheduleChangeRequest = this.mapper.Map<GmsEntity, ScheduleChangeRequest>(gmsEntity, opts =>
            {
                opts.Items[nameof(ScheduleChangeRequest.RequestorEventType)] = requestorEventType;
                opts.Items[nameof(ScheduleChangeRequest.RequestorLiveEventScheduleId)] = null;
                opts.Items[nameof(ScheduleChangeRequest.ExistingScheduleId)] = videoPlatformScheduleId;
            });

            this.AddWorkflowIntentToScheduleChangeRequest(this.GetMetadataDeleteWorkflowIntent(gmsEntity, videoPlatformSchedule), scheduleChangeRequest);

            scheduleChangeRequest.CorrelationId = correlationId;
            return scheduleChangeRequest;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ScheduleChangeRequest>> GetScheduleChangeRequestsToDeleteAsync([NotNull] GmsEntity gmsEntity, IEnumerable<ScheduleChangeRequest> newScheduleChangeRequests, string requestorEventType, string correlationId)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var scheduleDeleteRequests = new List<ScheduleChangeRequest>();

            var scheduleChangeRequestsForExistingVideoPlatformSchedules = newScheduleChangeRequests.Where(x => !string.IsNullOrEmpty(x.ExistingScheduleId));

            var existingVideoPlatformSchedules = await this.GetMediaLevelVideoPlatformSchedulesForLiveEventAsync(gmsEntity.Id, requestorEventType).ConfigureAwait(false);

            var videoPlatformSchedulesToBeDeleted = existingVideoPlatformSchedules.Where(existingVideoPlatformSchedule =>
                    !scheduleChangeRequestsForExistingVideoPlatformSchedules.Any(
                        scheduleChangeRequestRequired => scheduleChangeRequestRequired.ExistingScheduleId == existingVideoPlatformSchedule.Id));

            foreach (var videoPlatformScheduleToBeDeleted in videoPlatformSchedulesToBeDeleted)
            {
                var scheduleChangeRequest = this.mapper.Map<GmsEntity, ScheduleChangeRequest>(gmsEntity, opts =>
                {
                    opts.Items[nameof(ScheduleChangeRequest.DeleteVideoPlatformSchedule)] = true;
                    opts.Items[nameof(ScheduleChangeRequest.RequestorEventType)] = requestorEventType;
                    opts.Items[nameof(ScheduleChangeRequest.RequestorLiveEventScheduleId)] = videoPlatformScheduleToBeDeleted.RequestorLiveEventScheduleId;
                    opts.Items[nameof(ScheduleChangeRequest.ExistingScheduleId)] = videoPlatformScheduleToBeDeleted.Id;
                });
                scheduleChangeRequest.CorrelationId = correlationId;

                scheduleDeleteRequests.Add(scheduleChangeRequest);
            }

            return scheduleDeleteRequests;
        }

        /// <summary>
        /// Get ScheduleChangeRequest.
        /// </summary>
        /// <param name="gmsEntity">The GMS game.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="correlationId">correlation Id.</param>
        /// <returns>
        /// ScheduleChangeRequest message.
        /// </returns>
        public async Task<IList<ScheduleChangeRequest>> GetInfrastructureWorkflowScheduleRequestAsync([NotNull] GmsEntity gmsEntity, string requestorEventType, string correlationId)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var intentMetadatas = gmsEntity.GetIntentMetaDatas(this.customOffsetOptions, this.esniResourcesCreationOptions.Value, this.aquilaChannelCreationOptions.Value, this.thirdPartyEndpointCreationOptions.Value);
            var scheduleChangeRequests = new List<ScheduleChangeRequest>();

            foreach (var intentMetadata in intentMetadatas)
            {
                var videoPlatformScheduleId = await this.GetVideoPlatformScheduleIdAsync(gmsEntity.Id, intentMetadata.ScheduleId).ConfigureAwait(false);

                var scheduleChangeRequest = this.mapper.Map<GmsEntity, ScheduleChangeRequest>(gmsEntity, opts =>
                {
                    opts.Items[nameof(ScheduleChangeRequest.RequestorEventType)] = requestorEventType;
                    opts.Items[nameof(ScheduleChangeRequest.RequestorLiveEventScheduleId)] = intentMetadata.ScheduleId;
                    opts.Items[nameof(ScheduleChangeRequest.ExistingScheduleId)] = videoPlatformScheduleId;
                });

                var leagueId = gmsEntity.Id.Substring(0, 2);

                if (!this.dmmCreationOptions.Value.NonNextGenLeague.Contains(leagueId, StringComparison.Ordinal))
                {
                    if (intentMetadata.CustomOffsets.ContainsKey(NbaWorkflowIds.EventInfrastructureStart))
                    {
                        this.AddWorkflowIntentToScheduleChangeRequest(
                            this.GetInfrastructureSetupWorkflowIntent(gmsEntity, intentMetadata),
                            scheduleChangeRequest);
                    }

                    this.AddWorkflowIntentToScheduleChangeRequest(
                        this.GetInfrastructureStartWorkflowIntent(gmsEntity, intentMetadata), scheduleChangeRequest);

                    if (!gmsEntity.MediaHasScte35Available(this.tvpEventCreationOptions.Value, intentMetadata.GmsMediaId))
                    {
                        this.AddWorkflowIntentToScheduleChangeRequest(
                            this.GetReachedTipoffTimeWorkflowIntent(gmsEntity, intentMetadata), scheduleChangeRequest);
                    }

                    if (intentMetadata.HasPreGameExperience)
                    {
                        this.AddWorkflowIntentToScheduleChangeRequest(
                            this.GetProductionRemovePreGamePackageWorkflowIntent(gmsEntity, intentMetadata),
                            scheduleChangeRequest);
                    }

                    if (intentMetadata.HasPostGameExperience)
                    {
                        this.AddWorkflowIntentToScheduleChangeRequest(
                            this.GetProductionRemovePostGamePackageWorkflowIntent(gmsEntity, intentMetadata),
                            scheduleChangeRequest);
                    }

                    this.AddWorkflowIntentToScheduleChangeRequest(
                        this.GetInfrastructureEndWorkflowIntent(gmsEntity, intentMetadata), scheduleChangeRequest);

                    this.AddWorkflowIntentToScheduleChangeRequest(
                        this.GetInfrastructureCleanupWorkflowIntent(gmsEntity, intentMetadata), scheduleChangeRequest);

                    this.AddWorkflowIntentToScheduleChangeRequest(
                        this.GetMetadataReassignmentWorkflowIntent(gmsEntity, intentMetadata), scheduleChangeRequest);
                    scheduleChangeRequest.CorrelationId = correlationId;
                }

                this.AddMultipleWorkflowIntentToScheduleChangeRequest(this.GetLiveProductionServicesWorkflowsIntent(intentMetadata, gmsEntity), scheduleChangeRequest);
                scheduleChangeRequests.Add(scheduleChangeRequest);
            }

            return scheduleChangeRequests;
        }

        /// <summary>
        /// Upsert the Dmm workflows.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <param name="gmsEntity">The gameId.</param>
        /// <returns>Workflowintent list.</returns>
        public ICollection<WorkflowIntent> GetLiveProductionServicesWorkflowsIntent([NotNull] IntentMetaData media, [NotNull] GmsEntity gmsEntity)
        {
            if (media.LiveProductionServicesUrls != null && media.LiveProductionServicesUrls.Any())
            {
                var productionId = media.ChannelId;
                var liveProductionServices = media.LiveProductionServicesUrls;
                var liveProductionDetails = new LiveProductionServicesDetails()
                {
                    GameId = gmsEntity.Id,
                    MediaId = productionId,
                    Clients = new List<ClientLiveProduction>(),
                };

                var workFlowIntentsList = new List<WorkflowIntent>();

                if (liveProductionServices != null)
                {
                    List<ClientLiveProduction> liveProductionClients = new List<ClientLiveProduction>();
                    foreach (var liveProductionService in liveProductionServices)
                    {
                        var clientService =
                            liveProductionClients.Find(client => client.Name == liveProductionService.Name);
                        var clientLiveService = clientService ?? new ClientLiveProduction()
                        { Name = liveProductionService.Name, Streams = new List<StreamLiveProduction>() };
                        var stream = this.mapper.Map<StreamLiveProduction>(liveProductionService);
                        clientLiveService.Streams.Add(stream);
                        if (clientService == null)
                        {
                            liveProductionClients.Add(clientLiveService);
                        }
                    }

                    liveProductionDetails.Clients = liveProductionClients;

                    workFlowIntentsList.Add(new WorkflowIntent
                    {
                        WorkflowId = NbaWorkflowIds.EventLiveProductionServicesStart,
                        LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                        ChannelId = gmsEntity.Id,
                        ActorSpecificDetails = new List<ActorSpecificDetail>
                        {
                            ActorSpecificDetail.CreateForDmmActor(liveProductionDetails),
                        },
                        WorkflowOffset = SetWorkflowOffset(media, NbaWorkflowIds.EventLiveProductionServicesStart)
                    });

                    workFlowIntentsList.Add(new WorkflowIntent
                    {
                        WorkflowId = NbaWorkflowIds.EventLiveProductionServicesStop,
                        LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                        ChannelId = gmsEntity.Id,
                        ActorSpecificDetails = new List<ActorSpecificDetail>
                        {
                            ActorSpecificDetail.CreateForDmmActor(liveProductionDetails),
                        },
                        WorkflowOffset = SetWorkflowOffset(media, NbaWorkflowIds.EventLiveProductionServicesStop)
                    });
                }

                return workFlowIntentsList;
            }
            else
            {
                return new List<WorkflowIntent>();
            }
        }

        /// <summary>
        /// Gets the schedule change request asynchronous.
        /// </summary>
        /// <param name="gmsTeamZips">The GMS team zips.</param>
        /// <param name="audienceList">The esni audience list.</param>
        /// <returns>
        /// ScheduleChangeRequest message.
        /// </returns>
        public async Task<ScheduleChangeRequest> GetTeamZipsScheduleChangeRequestAsync([NotNull] GmsTeamZips gmsTeamZips, IEnumerable<EsniAudience> audienceList)
        {
            gmsTeamZips.Required(nameof(gmsTeamZips));
            var videoPlatformScheduleId = await this.GetVideoPlatformScheduleIdAsync(gmsTeamZips.TeamId).ConfigureAwait(false);

            var scheduleChangeRequest = this.mapper.Map<GmsTeamZips, ScheduleChangeRequest>(gmsTeamZips, opts =>
            {
                opts.Items[nameof(ScheduleChangeRequest.ExistingScheduleId)] = videoPlatformScheduleId;
            });

            this.AddWorkflowIntentToScheduleChangeRequest(gmsTeamZips.GetTeamZipUpdateWorkflowIntent(audienceList, this.dateTimeService), scheduleChangeRequest);
            return scheduleChangeRequest;
        }

        /// <inheritdoc/>
        public ScheduleChangeRequest GetVideoPlatformChannelScheduleRequest([NotNull] GmsEntity gmsEntity, string correlationId)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var videoPlatformChannelCreationInfos = new List<VideoPlatformChannelCreationInfo>();
            var scheduleChangeRequest = new ScheduleChangeRequest
            {
                RequestorLiveEventId = gmsEntity.Id,
                RequestorEventType = gmsEntity.IsEvent ? nameof(GmsEvent) : nameof(GmsGame),
                RequestorId = ActorIds.GmsInterpreter,
                CorrelationId = correlationId,
            };

            if (gmsEntity.HasActiveNssMediasWithActiveSchedules)
            {
                foreach (var mediaInfo in gmsEntity.Media.Where(m => m.IsActiveNssMediaWithActiveSchedules))
                {
                    var channelId = gmsEntity.GetChannelId(mediaInfo);
                    videoPlatformChannelCreationInfos.Add(new VideoPlatformChannelCreationInfo
                    {
                        Id = channelId,
                        LiveEventId = gmsEntity.Id,
                        PrimaryFeed = gmsEntity.IsChannelPrimary(this.aquilaChannelCreationOptions.Value.NSSPrimaryFeedKey, channelId),
                        LiveToOnDemand = gmsEntity.IsMediaLiveToOnDemand(this.tvpEventCreationOptions.Value, mediaInfo.Id),
                        HasInBandScte35 = gmsEntity.MediaHasScte35Available(this.tvpEventCreationOptions.Value, mediaInfo.Id),
                    });
                }
            }
            else
            {
                videoPlatformChannelCreationInfos.Add(new VideoPlatformChannelCreationInfo
                {
                    Id = gmsEntity.GetDummyMediaName(),
                    LiveEventId = gmsEntity.Id,
                    PrimaryFeed = true,
                    LiveToOnDemand = false,
                    HasInBandScte35 = false,
                });
            }

            scheduleChangeRequest.VideoPlatformChannelCreationInfos = videoPlatformChannelCreationInfos;

            return scheduleChangeRequest;
        }

        /// <summary>
        /// Upserts the entitlements for media asynchronous.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task UpsertEntitlementsForMediasAsync([NotNull] GmsEntity gmsEntity)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var entitlementRepository = this.repositoryFactory.Resolve<GmsEntitlement>();

            var gmsEntitlement = new GmsEntitlement
            {
                Id = gmsEntity.Id,
                DateOfEvent = gmsEntity.DateTime,
            };

            if (gmsEntity.HasActiveNssMediasWithActiveSchedules)
            {
                var ruleRepository = this.repositoryFactory.Resolve<GmsEntitlementRules>();

                var nssMedias = gmsEntity.Media.Where(m => m.IsActiveNssMediaWithActiveSchedules).ToList();

                var allRules = await ruleRepository.GetItemsAsync(x => true).ConfigureAwait(false);

                if (gmsEntity.IsEvent)
                {
                    this.ApplyEventMediaEntitlements(gmsEntity, gmsEntitlement, nssMedias, allRules);
                }
                else
                {
                    this.ApplyGameMediaEntitlements(gmsEntity as GmsGame, gmsEntitlement, nssMedias, allRules);
                }
            }
            else
            {
                AddMediaEntitlementsForDummyProduction(gmsEntity, gmsEntitlement);
            }

            await entitlementRepository.UpdateItemAsync(gmsEntitlement).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public async Task<bool> CheckAnyVideoPlatformSchedulesExistAsync(string requestorLiveEventId, string requestorType)
        {
            var videoPlatformSchedules = await this.GetVideoPlatformSchedulesForLiveEventAsync(requestorLiveEventId, requestorType).ConfigureAwait(false);
            return videoPlatformSchedules.Any();
        }

        /// <summary>
        /// Create the query to get all teamZips for local blackouts.
        /// </summary>
        /// <param name="query">The current query string.</param>
        /// <param name="team">The team to query.</param>
        /// <returns>The quersy string.</returns>
        private static string GetTeamZipQueryString(string query, string team)
        {
            if (query.IsNullOrEmpty())
            {
                return $"SELECT * FROM c WHERE c.Abbr = '{team}'";
            }
            else
            {
                return query + $" or c.Abbr = '{team}'";
            }
        }

        /// <summary>
        /// Gets the default team entitlements.
        /// </summary>
        /// <param name="game">The game.</param>
        /// <returns>The default team entitlements.</returns>
        private static List<string> GetDefaultTeamEntitlements(GmsGame game)
        {
            var homeTeamPackage = string.Concat(GmsGameTransformationConstants.DomesticTeamChoicePrefix, game.HomeTeam?.Abbr);
            var awayTeamPackage = string.Concat(GmsGameTransformationConstants.DomesticTeamChoicePrefix, game.AwayTeam?.Abbr);

            var defaultTeamEntitlements = new List<string>
                {
                    homeTeamPackage,
                    awayTeamPackage,
                };

            return defaultTeamEntitlements;
        }

        /// <summary>
        /// Creates the media entitlement for the dummy production. This is intended for games without NSS medias.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="gmsEntitlement">The existing GmsEntitlement.</param>
        private static void AddMediaEntitlementsForDummyProduction(GmsEntity gmsEntity, GmsEntitlement gmsEntitlement)
        {
            gmsEntitlement.MediaEntitlements = new List<MediaEntitlement>()
                {
                    new MediaEntitlement
                    {
                        MediaId = gmsEntity.GetDummyMediaName(),
                        Entitlements = new List<string>()
                        {
                            GmsGameTransformationConstants.DummyProductionPackageName,
                        },
                        OverrideDefaultPackages = true,
                    },
                };
        }

        /// <summary>
        /// Converts to type.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="type">The type.</param>
        /// <returns>object.</returns>
        private static dynamic ConvertToType(object value, string type)
        {
            return Convert.ChangeType(value, Type.GetType(type), CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Applies the event MediaEntitlements.
        /// </summary>
        /// <param name="gmsEntity">The GmsEntity.</param>
        /// <param name="gmsEntitlement">The GmsEntitlement to apply MediaEntitlements to.</param>
        /// <param name="medias">The NSS medias related to the event.</param>
        /// <param name="allRules">The GmsEntitlementRules.</param>
        private void ApplyEventMediaEntitlements(GmsEntity gmsEntity, GmsEntitlement gmsEntitlement, List<MediaInfo> medias, IEnumerable<GmsEntitlementRules> allRules)
        {
            var eventRules = allRules.Where(rule => rule.IsEventRule);

            foreach (var rule in eventRules)
            {
                this.AppendMediaEntitlementsToGmsEntitlementForNSSEntitled(gmsEntity, gmsEntitlement, new List<string> { rule.Entitlement }, medias, rule.OverrideDefaultPackages);
            }
        }

        /// <summary>
        /// Applies the game MediaEntitlements.
        /// </summary>
        /// <param name="game">The GmsGame.</param>
        /// <param name="gmsEntitlement">The GmsEntitlement to apply MediaEntitlements to.</param>
        /// <param name="medias">The NSS medias related to the game .</param>
        /// <param name="allRules">The GmsEntitlementRules.</param>
        private void ApplyGameMediaEntitlements(GmsGame game, GmsEntitlement gmsEntitlement, List<MediaInfo> medias, IEnumerable<GmsEntitlementRules> allRules)
        {
            this.ApplyGameLevelEntitlements(game, gmsEntitlement, medias, allRules);
            this.ApplyMediaLevelEntitlements(game, gmsEntitlement, medias, allRules);
            this.ApplyDefaultGameEntitlements(game, gmsEntitlement, medias);
            this.ApplyPreAndPostGameEntitlements(game, gmsEntitlement, medias);
        }

        /// <summary>
        /// Applies the media level entitlements.
        /// </summary>
        /// <param name="gmsEntity">The GmsEntity.</param>
        /// <param name="gmsEntitlement">The existing gmsEntitlement.</param>
        /// <param name="medias">The NSS medias related to the game or event.</param>
        /// <param name="allRules">The GmsEntitlementRules.</param>
        private void ApplyMediaLevelEntitlements(GmsEntity gmsEntity, GmsEntitlement gmsEntitlement, List<MediaInfo> medias, IEnumerable<GmsEntitlementRules> allRules)
        {
            var mediaLevelRules = allRules.Where(rule => rule.IsMediaRule);

            foreach (var rule in mediaLevelRules)
            {
                var applicableMedias = this.GetApplicableMedias(medias, rule).ToList();

                if (applicableMedias == null || applicableMedias.Count == 0)
                {
                    continue;
                }

                if (rule.ApplyToAllMedia)
                {
                    this.AppendMediaEntitlementsToGmsEntitlement(gmsEntity, gmsEntitlement, new List<string> { rule.Entitlement }, medias, rule.OverrideDefaultPackages);
                }
                else
                {
                    this.AppendMediaEntitlementsToGmsEntitlement(gmsEntity, gmsEntitlement, new List<string> { rule.Entitlement }, applicableMedias, rule.OverrideDefaultPackages);
                }
            }
        }

        /// <summary>
        /// Applies the game level entitlements.
        /// </summary>
        /// <param name="gmsGame">The GmsGame.</param>
        /// <param name="gmsEntitlement">The existing GmsEntitlement.</param>
        /// <param name="medias">The NSSS medias related to the game.</param>
        /// <param name="allRules">The GmsEntitlementRules.</param>
        private void ApplyGameLevelEntitlements(GmsGame gmsGame, GmsEntitlement gmsEntitlement, List<MediaInfo> medias, IEnumerable<GmsEntitlementRules> allRules)
        {
            var gameLevelRules = allRules.Where(rule => rule.IsGameRule && this.IsGameRuleApplicable(rule, gmsGame));

            foreach (var rule in gameLevelRules)
            {
                this.AppendMediaEntitlementsToGmsEntitlement(gmsGame, gmsEntitlement, new List<string> { rule.Entitlement }, medias, rule.OverrideDefaultPackages);
            }
        }

        /// <summary>
        /// Applies the default game entitlements.
        /// </summary>
        /// <param name="game">The GmsGame.</param>
        /// <param name="gmsEntitlement">The existing GmsEntitlement.</param>
        /// <param name="medias">The NSS medias related to the game or event.</param>
        private void ApplyDefaultGameEntitlements(GmsGame game, GmsEntitlement gmsEntitlement, List<MediaInfo> medias)
        {
            if (gmsEntitlement.MediaEntitlements == null)
            {
                var teamEntitlements = GetDefaultTeamEntitlements(game);
                this.AppendMediaEntitlementsToGmsEntitlement(game, gmsEntitlement, teamEntitlements, medias, false);
            }
            else
            {
                var mediaNamesWithOverride = gmsEntitlement.MediaEntitlements.Where(x => x.OverrideDefaultPackages).Select(x => x.MediaId);
                var applicableMedias = medias.Where(media => !mediaNamesWithOverride.Any(mediaName => mediaName == game.GetMediaName(game, media))).ToList();

                if (applicableMedias.Any())
                {
                    var teamEntitlements = GetDefaultTeamEntitlements(game);
                    this.AppendMediaEntitlementsToGmsEntitlement(game, gmsEntitlement, teamEntitlements, applicableMedias, false);
                }
            }
        }

        /// <summary>
        /// Applies the pre-game and post-game entitlements if necessary.
        /// </summary>
        /// <param name="gmsGame">The <see cref="GmsGame"/>.</param>
        /// <param name="gmsEntitlement">The existing <see cref="GmsEntitlement"/>.</param>
        /// <param name="medias">The NSS medias related to the game or event.</param>
        private void ApplyPreAndPostGameEntitlements(GmsGame gmsGame, GmsEntitlement gmsEntitlement, List<MediaInfo> medias)
        {
            var preGamePackage = this.tvpEventCreationOptions.Value.PreGamePackage;
            var postGamePackage = this.tvpEventCreationOptions.Value.PostGamePackage;

            foreach (var media in medias)
            {
                var mediaId = gmsGame.GetMediaName(gmsGame, media);
                var mediaEntitlement = gmsEntitlement.MediaEntitlements.First(m => m.MediaId == mediaId);

                var preferredSchedule = media.GetPreferredSchedule();
                var hasPreGameExperience = preferredSchedule.HasPreGameExperience(media, this.esniResourcesCreationOptions.Value);
                if (hasPreGameExperience)
                {
                    mediaEntitlement.Entitlements.Add(preGamePackage);
                }

                var hasPostGameExperience = preferredSchedule.HasPostGameExperience(media, this.esniResourcesCreationOptions.Value);
                if (hasPostGameExperience)
                {
                    mediaEntitlement.Entitlements.Add(postGamePackage);
                }
            }
        }

        /// <summary>
        /// Gets the video platform schedule identifier.
        /// </summary>
        /// <param name="requesterLiveEventId">The game identifier.</param>
        /// <param name="requesterLiveEventScheduleId">The media schedule identifier.</param>
        /// <returns>
        /// The schedule id.
        /// </returns>
        private async Task<string> GetVideoPlatformScheduleIdAsync(string requesterLiveEventId, string requesterLiveEventScheduleId = null)
        {
            var videoPlatformSchedule = await this.GetVideoPlatformScheduleAsync(requesterLiveEventId, requesterLiveEventScheduleId).ConfigureAwait(false);
            var videoPlatformScheduleId = videoPlatformSchedule?.Id;
            return videoPlatformScheduleId;
        }

        /// <summary>
        /// Gets the video platform schedule.
        /// </summary>
        /// <param name="requesterLiveEventId">The game identifier.</param>
        /// <param name="requesterLiveEventScheduleId">The media schedule identifier.</param>
        /// <returns>
        /// The schedule.
        /// </returns>
        private async Task<VideoPlatformSchedule> GetVideoPlatformScheduleAsync(string requesterLiveEventId, string requesterLiveEventScheduleId = null)
        {
            var videoPlatformScheduleRepository = this.repositoryFactory.Resolve<VideoPlatformSchedule>();

            // Get the VideoPlatformSchedules for this game, without schedule id
            var videoPlatformScheduleList = await videoPlatformScheduleRepository.GetItemsAsync(x => x.RequestorLiveEventId == requesterLiveEventId && x.RequestorLiveEventScheduleId == requesterLiveEventScheduleId).ConfigureAwait(false);
            var videoPlatformScheduleId = videoPlatformScheduleList.SingleOrDefault()?.Id;

            if (string.IsNullOrEmpty(videoPlatformScheduleId))
            {
                this.logger.LogInformation("No matching entry in VideoPlatformSchedule container for {GameId}. A new VideoPlatformSchedule will be created.", requesterLiveEventId);
            }
            else
            {
                this.logger.LogInformation("Found VideoPlatformSchedule with Id {ScheduleId} for game {GameId}.", videoPlatformScheduleId, requesterLiveEventId);
            }

            return videoPlatformScheduleList.SingleOrDefault();
        }

        /// <summary>
        /// Gets a VideoPlatformWorkflow by id.
        /// </summary>
        /// <param name="workflowId">The workflow id.</param>
        /// <returns>
        /// The VideoPlatformWorkflow.
        /// </returns>
        private async Task<VideoPlatformWorkflow> GetVideoPlatformWorkflowAsync(string workflowId)
        {
            var videoPlatformWorkflowRepository = this.repositoryFactory.Resolve<VideoPlatformWorkflow>();

            var videoPlatformWorkflow = await videoPlatformWorkflowRepository.GetItemAsync(workflowId).ConfigureAwait(false);
            return videoPlatformWorkflow;
        }

        /// <summary>
        /// Appends MediaEntitlements to the GmsEntitlement.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="gmsEntitlement">The GMS entitlement.</param>
        /// <param name="entitlementList">The entitlements strings.</param>
        /// <param name="medias">The NSS medias.</param>
        /// <param name="overrideDefaultPackages">Whether to overrideDefaultPackages creation.</param>
        private void AppendMediaEntitlementsToGmsEntitlement([NotNull] GmsEntity gmsEntity, GmsEntitlement gmsEntitlement, List<string> entitlementList, List<MediaInfo> medias, bool overrideDefaultPackages)
        {
            gmsEntity.Required(nameof(gmsEntity));
            gmsEntitlement.MediaEntitlements ??= new List<MediaEntitlement>();

            foreach (var media in medias)
            {
                var mediaId = gmsEntity.GetMediaName(gmsEntity, media);
                var mediaEntitlement = gmsEntitlement.MediaEntitlements.FirstOrDefault(x => x.MediaId == mediaId);

                if (mediaEntitlement == null)
                {
                    mediaEntitlement = new MediaEntitlement
                    {
                        MediaId = mediaId,
                        Entitlements = new List<string>(),
                        OverrideDefaultPackages = false,
                    };

                    gmsEntitlement.MediaEntitlements.Add(mediaEntitlement);
                }

                mediaEntitlement.Entitlements = mediaEntitlement.Entitlements.Union(entitlementList).ToList();
                mediaEntitlement.OverrideDefaultPackages = mediaEntitlement.OverrideDefaultPackages || overrideDefaultPackages;
            }
        }

        /// <summary>
        /// Appends MediaEntitlements to the GmsEntitlement for events that have NSS-Entitled in KVP.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="gmsEntitlement">The GMS entitlement.</param>
        /// <param name="entitlementList">The entitlements strings.</param>
        /// <param name="medias">The NSS medias.</param>
        /// <param name="overrideDefaultPackages">Whether to overrideDefaultPackages creation.</param>
        private void AppendMediaEntitlementsToGmsEntitlementForNSSEntitled([NotNull] GmsEntity gmsEntity, GmsEntitlement gmsEntitlement, List<string> entitlementList, List<MediaInfo> medias, bool overrideDefaultPackages)
        {
            gmsEntity.Required(nameof(gmsEntity));
            gmsEntitlement.MediaEntitlements ??= new List<MediaEntitlement>();

            foreach (var media in medias)
            {
                var mediaId = gmsEntity.GetMediaName(gmsEntity, media);
                var mediaEntitlement = gmsEntitlement.MediaEntitlements.FirstOrDefault(x => x.MediaId == mediaId);

                if (mediaEntitlement == null)
                {
                    mediaEntitlement = new MediaEntitlement
                    {
                        MediaId = mediaId,
                        Entitlements = new List<string>(),
                        OverrideDefaultPackages = false,
                    };

                    gmsEntitlement.MediaEntitlements.Add(mediaEntitlement);
                }

                mediaEntitlement.OverrideDefaultPackages = mediaEntitlement.OverrideDefaultPackages || overrideDefaultPackages;

                var nssEntitledKVPValue = (media.Schedules?.FirstOrDefault()?.Operations?.KeyValuePairs?.FirstOrDefault(x => x.Key.EqualsIgnoreCase("NSS-Entitled"))?.Value
                    ?? media.KeyValuePairs?.FirstOrDefault(x => x.Key.EqualsIgnoreCase("NSS-Entitled"))?.Value)
                    ?? gmsEntity.KeyValuePairs?.FirstOrDefault(x => x.Key.EqualsIgnoreCase("NSS-Entitled"))?.Value;

                if (nssEntitledKVPValue.IsNullOrEmpty())
                {
                    mediaEntitlement.Entitlements = mediaEntitlement.Entitlements.Union(entitlementList).ToList();
                }
                else
                {
                    var nssEntitledCommaSeparatedValues = nssEntitledKVPValue.SplitAndTrim(",");
                    mediaEntitlement.Entitlements = nssEntitledCommaSeparatedValues.ToList();
                }
            }
        }

        /// <summary>
        /// Gets the medias for which the rule applies.
        /// </summary>
        /// <param name="nssMedias">The nssMedias.</param>
        /// <param name="rule">The rule.</param>
        /// <returns>
        ///  Medias for which the rule applies.
        /// </returns>
        private IEnumerable<MediaInfo> GetApplicableMedias(List<MediaInfo> nssMedias, GmsEntitlementRules rule)
        {
            ParameterExpression argParam = Expression.Parameter(typeof(MediaInfo), "m");
            var validRule = rule.Parameter.Replace(GmsGameTransformationConstants.MediaRule, string.Empty, StringComparison.Ordinal);

            var properties = validRule.Split('.');

            Expression member = argParam;

            foreach (var property in properties)
            {
                member = Expression.PropertyOrField(member, property);
            }

            var typedValue = ConvertToType(rule.Value, member.Type.FullName);
            var val1 = Expression.Constant(typedValue);

            Expression e1 = Expression.Equal(member, val1);

            Expression<Func<MediaInfo, bool>> lambda = null;
            if (member.Type.Name == "String")
            {
                BinaryExpression nullCheck = Expression.NotEqual(member, Expression.Constant(null, typeof(object)));
                MethodInfo mi = typeof(string).GetMethod("Equals", new[] { typeof(string) });
                Expression call = Expression.Call(member, mi, val1);
                lambda = Expression.Lambda<Func<MediaInfo, bool>>(Expression.AndAlso(nullCheck, call), argParam);
            }
            else
            {
                lambda = Expression.Lambda<Func<MediaInfo, bool>>(e1, argParam);
            }

            var response = nssMedias.Where(lambda.Compile());

            return response;
        }

        /// <summary>
        /// Checks if the game rule is applicable.
        /// </summary>
        /// <param name="rule">The rule.</param>
        /// <param name="game">The game.</param>
        /// <returns>bool.</returns>
        private bool IsGameRuleApplicable(GmsEntitlementRules rule, GmsGame game)
        {
            ParameterExpression argParam = Expression.Parameter(typeof(GmsGame), "g");
            var validRule = rule.Parameter.Replace(GmsGameTransformationConstants.GameRule, string.Empty, StringComparison.Ordinal);

            var properties = validRule.Split('.');

            Expression member = argParam;

            foreach (var property in properties)
            {
                member = Expression.PropertyOrField(member, property);
            }

            var typedValue = ConvertToType(rule.Value, member.Type.FullName);
            var val1 = Expression.Constant(typedValue);

            Expression e1 = Expression.Equal(member, val1);

            var newListOfGame = new List<GmsGame>
                {
                    game,
                };

            Expression<Func<GmsGame, bool>> lambda = null;
            if (member.Type.Name == "String")
            {
                MethodInfo mi = typeof(string).GetMethod("Equals", new[] { typeof(string) });
                Expression call = Expression.Call(member, mi, val1);
                lambda = Expression.Lambda<Func<GmsGame, bool>>(call, argParam);
            }
            else
            {
                lambda = Expression.Lambda<Func<GmsGame, bool>>(e1, argParam);
            }

            var response = newListOfGame.Any(lambda.Compile());
            return response;
        }

        /// <summary>
        /// Gets the metadata start workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The GMS game.</param>
        /// <returns>
        /// A WorkflowIntent for EventMetadataStart.
        /// </returns>
        private WorkflowIntent GetMetadataStartWorkflowIntent(GmsEntity gmsEntity)
        {
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventMetadataStart,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpUpdateEventStatus
                            {
                                ExternalId = gmsEntity.Id,
                                EventStatus = TvpEventStatus.Started,
                            }),
                    },
            };

            return workflowIntent;
        }

        /// <summary>
        /// Gets the reached tipoff time workflow intent asynchronous.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="intentMetaData">The intent meta data.</param>
        /// <returns>
        /// A WorkflowIntent for EventReachedTipoffTime.
        /// </returns>
        private WorkflowIntent GetReachedTipoffTimeWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventReachedTipoffTime,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpProductionUpdateStatusInfo
                        {
                            ProductionId = intentMetaData.ChannelId,
                            ValidPreviousState = TvpProductionStatus.Verified,
                            State = TvpProductionStatus.Broadcast,
                            EventId = gmsEntity.Id,
                        }),
                    },
                WorkflowOffset = intentMetaData.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventReachedTipoffTime),
            };

            return workflowIntent;
        }

        /// <summary>
        /// Gets the metadata end workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <returns>
        /// A WorkflowIntent for EventMetadataEnd.
        /// </returns>
        private WorkflowIntent GetMetadataEndWorkflowIntent(GmsEntity gmsEntity)
        {
            var gmsEvent = gmsEntity as GmsEvent;
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventMetadataEnd,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpUpdateEventStatus
                            {
                                ExternalId = gmsEntity.Id,
                                EventStatus = TvpEventStatus.Final,
                            }),
                    },
                WorkflowRequestTime = gmsEvent?.EndDateTime,
            };

            return workflowIntent;
        }

        /// <summary>
        /// Gets the metadata setup workflow intent asynchronous.
        /// </summary>
        /// <param name="gmsEntity">The GMS game.</param>
        /// <param name="existingVideoPlatformSchedule">The existing schedule.</param>
        /// <returns>
        /// A workflowIntent object.
        /// </returns>
        private async Task<WorkflowIntent> GetMetadataSetupWorkflowIntentAsync(GmsEntity gmsEntity, VideoPlatformSchedule existingVideoPlatformSchedule = null)
        {
            var gameDuration = (await this.GetVideoPlatformWorkflowAsync(NbaWorkflowIds.EventMetadataEnd).ConfigureAwait(false)).BusinessDefaultOffset;

            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventMetadataSetup,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(gmsEntity.GetTvpEventCreationInfo(gameDuration, this.tvpEventCreationOptions.Value, this.dateTimeService)),
                    },
            };

            var prismaInfo = gmsEntity.GetPrismaInfo(this.esniResourcesCreationOptions.Value, this.aquilaChannelCreationOptions.Value.NSSPrimaryFeedKey, gameDuration);
            prismaInfo = prismaInfo.UpdateEsniIdsForGameUpdate(existingVideoPlatformSchedule);

            if (prismaInfo != null)
            {
                prismaInfo.EventId = gmsEntity.Id;
                workflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPrismaActor(prismaInfo));
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the metadata reassing workflow intent asynchronous.
        /// </summary>
        /// <param name="gmsEntity"> The Gms Game.</param>
        /// <param name="intentMetaData">The intentmetadata.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        private WorkflowIntent GetMetadataReassignmentWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            var gmsEvent = gmsEntity as GmsEvent;
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventProductionRemovePackages,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(
                        new TvpProductionPackage
                            {
                                EventId = gmsEntity.Id,
                                ProductionId = intentMetaData.ChannelId,
                                PackageId = null,
                            }),
                    },
                WorkflowOffset = intentMetaData.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventProductionRemovePackages),
            };

            if (gmsEvent?.EndDateTime != null)
            {
                workflowIntent.WorkflowRequestTime = gmsEvent.EndDateTime.Value.AddHours(this.workflowOffsetSettings.MetadataReassignmentIntentEndtimeOffsetInHours);
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the metadata cleanup workflow intent asynchronous.
        /// </summary>
        /// <param name="gmsEntity">The GMS game.</param>
        /// <returns>Workflow intent.</returns>
        private async Task<WorkflowIntent> GetMetadataCleanupWorkflowIntentAsync(GmsEntity gmsEntity)
        {
            var gmsEvent = gmsEntity as GmsEvent;
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventMetadataCleanup,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(gmsEntity.GetTvpEventCleanupInfo()),
                    },
            };

            var gameDuration = (await this.GetVideoPlatformWorkflowAsync(NbaWorkflowIds.EventMetadataEnd).ConfigureAwait(false)).BusinessDefaultOffset;
            var prismaMediasCreationInfo = gmsEntity.GetPrismaInfo(this.esniResourcesCreationOptions.Value, this.aquilaChannelCreationOptions.Value.NSSPrimaryFeedKey, gameDuration);

            if (prismaMediasCreationInfo != null)
            {
                var esniResourcesIds = prismaMediasCreationInfo.GetEsniResourcesIdsForCleanup();
                esniResourcesIds.EventId = gmsEntity.Id;
                workflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPrismaActor(esniResourcesIds));
            }

            if (gmsEvent?.EndDateTime != null)
            {
                workflowIntent.WorkflowRequestTime = gmsEvent.EndDateTime.Value.AddHours(this.workflowOffsetSettings.MetadataCleanupIntentEndtimeOffsetInHours);
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the metadata delete workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="existingVideoPlatformSchedule">The existing Schedule.</param>
        /// <returns>The metadata delete workflow intent.</returns>
        private WorkflowIntent GetMetadataDeleteWorkflowIntent(GmsEntity gmsEntity, VideoPlatformSchedule existingVideoPlatformSchedule)
        {
            var existingPrismaInfo = existingVideoPlatformSchedule.GetActorSpecificDetailsFromSchedule<PrismaMediaInfo>(NbaWorkflowIds.EventMetadataSetup, ActorIds.PrismaMedias);
            var existingCleanupIds = existingPrismaInfo?.GetEsniResourcesIdsForCleanup()?.EsniResourceIds.ToList() ?? new List<string>();
            var existingtoDeleteIds = existingPrismaInfo?.EsniResourceIdsToDelete?.ToList() ?? new List<string>();
            existingtoDeleteIds.AddRange(existingCleanupIds);
            existingtoDeleteIds = existingtoDeleteIds.Where(x => x != "/NBA/policy/blackout" || x != "/NBA/policy/event").ToList();

            existingtoDeleteIds = existingtoDeleteIds.Where(x => !string.Equals(x, "/NBA/policy/blackout", StringComparison.OrdinalIgnoreCase) && !string.Equals(x, "/NBA/policy/event", StringComparison.OrdinalIgnoreCase)).ToList();

            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventMetadataDelete,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpEventDeleteInfo
                            {
                                EventExternalId = gmsEntity.Id,
                            }),
                    },
            };

            if (existingtoDeleteIds.Any())
            {
                workflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPrismaActor(new PrismaDeleteResourceInfo
                {
                    EsniResourceIds = existingtoDeleteIds,
                    EventId = gmsEntity.Id,
                }));
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the infrastructure setup workflow intent.
        /// </summary>
        /// <param name="gmsEntity"><see cref="GmsEntity"/>.</param>
        /// <param name="intentMetaData"><see cref="IntentMetaData"/>.</param>
        /// <returns>Infra setup.</returns>
        private WorkflowIntent GetInfrastructureSetupWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            IEnumerable<MediaInfo> mediaInfos;

            if (intentMetaData != null)
            {
                mediaInfos = gmsEntity.Media.Where(x => x.Id == intentMetaData.GmsMediaId);
            }
            else
            {
                mediaInfos = gmsEntity.Media.Where(media =>
                    media.IsActiveNssMediaWithActiveSchedules
                    && !media.GetPreferredSchedule().HasCustomOffset(media, this.customOffsetOptions, NbaWorkflowIds.EventInfrastructureStart));
            }

            if (!mediaInfos.Any())
            {
                return default;
            }

            var aquilaChannelsCreationInfo = gmsEntity.GetAquilaChannelsCreationInfo(this.aquilaChannelCreationOptions.Value, this.esniResourcesCreationOptions.Value, mediaInfos);
            var quortexEndpointCreationInfo =
                gmsEntity.GetQuortexEndpointsCreationInfo(this.thirdPartyEndpointCreationOptions.Value, mediaInfos);

            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>(),
                WorkflowOffset = intentMetaData?.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventInfrastructureSetup),
            };

            IEnumerable<TvpProductionUpdateStatusInfo> tvpCreationInfo = new Collection<TvpProductionUpdateStatusInfo>();
            tvpCreationInfo = tvpCreationInfo.Concat(aquilaChannelsCreationInfo?.Select(channel => new TvpProductionUpdateStatusInfo
            {
                ProductionId = channel.ChannelId,
                State = TvpProductionStatus.Created,
                EventId = gmsEntity.Id,
            }).ToList());
            tvpCreationInfo = tvpCreationInfo.Concat(quortexEndpointCreationInfo?.Select(channel => new TvpProductionUpdateStatusInfo
            {
                ProductionId = channel.ChannelId,
                State = TvpProductionStatus.Created,
                EventId = gmsEntity.Id,
            }).ToList());

            if (tvpCreationInfo.Any())
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForTvpActor(tvpCreationInfo));
            }

            if (aquilaChannelsCreationInfo.Any())
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForAquilaActor(aquilaChannelsCreationInfo));
            }

            if (quortexEndpointCreationInfo.Any())
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForThirdPartyActor(quortexEndpointCreationInfo));
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the infrastructure start workflow intent.
        /// </summary>
        /// <param name="gmsGame">The GMS game.</param>
        /// <param name="intentMetaData">The intent data about the media.</param>
        /// <returns>Infra start.</returns>
        private WorkflowIntent GetInfrastructureStartWorkflowIntent(GmsEntity gmsGame, IntentMetaData intentMetaData)
        {
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                LiveEventTime = gmsGame.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpProductionUpdateStatusInfo
                            {
                                ProductionId = intentMetaData.ChannelId,
                                State = TvpProductionStatus.Started,
                                EventId = gmsGame.Id,
                            }),
                        ActorSpecificDetail.CreateForPrismaActor(new PrismaUpdateMatchTime
                            {
                                ProductionId = intentMetaData.ChannelId,
                                EventId = gmsGame.Id,
                            }),
                    },
                WorkflowOffset = intentMetaData.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventInfrastructureStart),
            };

            if (intentMetaData.IsThirdPartyProduction == null)
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForAquilaActor(new ChannelStateChangeInfo
                    {
                        ChannelId = intentMetaData.ChannelId,
                        EventId = gmsGame.Id,
                        PrimaryFeed = gmsGame.IsChannelPrimary(this.aquilaChannelCreationOptions.Value.NSSPrimaryFeedKey, intentMetaData.ChannelId),
                        LiveToOnDemand = gmsGame.IsMediaLiveToOnDemand(this.tvpEventCreationOptions.Value, intentMetaData.GmsMediaId),
                        HasInBandScte35 = gmsGame.MediaHasScte35Available(this.tvpEventCreationOptions.Value, intentMetaData.GmsMediaId),
                    }));
            }

            this.SetPlayoutActorSpecificDetail(gmsGame.Id, intentMetaData, workflowIntent);

            return workflowIntent;
        }

        /// <summary>
        /// Gets the infrastructure end workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The GMS game.</param>
        /// <param name="intentMetaData">The intent meta data.</param>
        /// <returns>End infra.</returns>
        private WorkflowIntent GetInfrastructureEndWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            var gmsEvent = gmsEntity as GmsEvent;
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureEnd,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = gmsEntity.Id,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpProductionUpdateStatusInfo
                            {
                                ProductionId = intentMetaData.ChannelId,
                                State = TvpProductionStatus.Over,
                                EventId = gmsEntity.Id,
                            }),
                    },
                WorkflowOffset = intentMetaData.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventInfrastructureEnd),
                WorkflowRequestTime = gmsEvent?.EndDateTime,
            };

            this.SetPlayoutActorSpecificDetail(gmsEntity.Id, intentMetaData, workflowIntent);

            return workflowIntent;
        }

        /// <summary>
        /// Insert a TVP ActorSpecificDetail if the encoder is greater than DefaultEncoderId value.
        /// </summary>
        /// <param name="gmsGameId">The GMS game identifier.</param>
        /// <param name="intentMetaData">The intent metadata.</param>
        /// <param name="workflowIntent">The workflow intent.</param>
        private void SetPlayoutActorSpecificDetail(string gmsGameId, IntentMetaData intentMetaData, WorkflowIntent workflowIntent)
        {
            if (int.TryParse(intentMetaData.EncoderId, out int encoderId) && encoderId > DefaultEncoderId && intentMetaData.PlayoutId != null)
            {
                workflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPlayoutActor(new PlayoutInfo
                {
                    AssetId = null,
                    PlayoutId = intentMetaData.PlayoutId,
                    ChannelId = intentMetaData.ChannelId,
                    IsLoop = true,
                    EventId = gmsGameId,
                    EncoderId = Convert.ToString(encoderId, CultureInfo.InvariantCulture),
                }));
            }
        }

        /// <summary>
        /// Gets the infrastructure cleanup workflow intent.
        /// </summary>
        /// <param name="gmsGame">The GMS game.</param>
        /// <param name="intentMetaData">The intent data about the media.</param>
        /// <returns>Infra cleanup.</returns>
        private WorkflowIntent GetInfrastructureCleanupWorkflowIntent(GmsEntity gmsGame, IntentMetaData intentMetaData)
        {
            var gmsEvent = gmsGame as GmsEvent;
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                LiveEventTime = gmsGame.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>(),
                WorkflowOffset = intentMetaData.CustomOffsets.GetValueOrDefault(NbaWorkflowIds.EventInfrastructureCleanup),
            };

            if (intentMetaData.IsThirdPartyProduction != null)
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForThirdPartyActor(new OttEndpointStateChangeInfo()
                    {
                        CustomPath = intentMetaData.ThirdPartyUrl,
                        PoolUuid = intentMetaData.ThirdPartyPoolUuid,
                        EventId = gmsGame.Id,
                        EndpointUuid = null,
                        ChannelId = intentMetaData.ChannelId,
                    }));
            }
            else
            {
                workflowIntent.ActorSpecificDetails.Add(
                    ActorSpecificDetail.CreateForAquilaActor(new ChannelStateChangeInfo
                    {
                        ChannelId = intentMetaData.ChannelId,
                        EventId = gmsGame.Id,
                    }));
            }

            if (gmsEvent?.EndDateTime != null)
            {
                workflowIntent.WorkflowRequestTime = gmsEvent.EndDateTime.Value.AddHours(this.workflowOffsetSettings.InfrastructureCleanupIntentEndtimeOffsetInHours);
            }

            return workflowIntent;
        }

        /// <summary>
        /// Gets the production remove pre-game package workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="intentMetaData">The <see cref="IntentMetaData"/>.</param>
        /// <returns>The <see cref="WorkflowIntent"/>.</returns>
        private WorkflowIntent GetProductionRemovePreGamePackageWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.ProductionRemovePreGamePackage,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpProductionPackage
                            {
                                EventId = gmsEntity.Id,
                                ProductionId = intentMetaData.ChannelId,
                                PackageId = this.tvpEventCreationOptions.Value.PreGamePackage,
                            }),
                    },
            };

            return workflowIntent;
        }

        /// <summary>
        /// Gets the production remove post-game package workflow intent.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="intentMetaData">The <see cref="IntentMetaData"/>.</param>
        /// <returns>The <see cref="WorkflowIntent"/>.</returns>
        private WorkflowIntent GetProductionRemovePostGamePackageWorkflowIntent(GmsEntity gmsEntity, IntentMetaData intentMetaData)
        {
            var workflowIntent = new WorkflowIntent
            {
                WorkflowId = NbaWorkflowIds.ProductionRemovePostGamePackage,
                LiveEventTime = gmsEntity.DateTime.Value.UtcDateTime,
                ChannelId = intentMetaData.ChannelId,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                    {
                        ActorSpecificDetail.CreateForTvpActor(new TvpProductionPackage
                            {
                                EventId = gmsEntity.Id,
                                ProductionId = intentMetaData.ChannelId,
                                PackageId = this.tvpEventCreationOptions.Value.PostGamePackage,
                            }),
                    },
                WorkflowOffset = this.tvpEventCreationOptions.Value.TimeSpanForPostGamePackageDeletion,
            };

            return workflowIntent;
        }

        /// <summary>
        /// Gets all the video platform schedules for live event.
        /// </summary>
        /// <param name="requestorLiveEventId">The requestor live event identifier.</param>
        /// <param name="requestorEventType">Type of the requestor event.</param>
        /// <returns>All the VideoPlatformSchedules for the live event.</returns>
        private async Task<IEnumerable<VideoPlatformSchedule>> GetVideoPlatformSchedulesForLiveEventAsync(string requestorLiveEventId, string requestorEventType)
        {
            var videoPlatformScheduleRepository = this.repositoryFactory.Resolve<VideoPlatformSchedule>();
            var existingVideoPlatformSchedules = await videoPlatformScheduleRepository.GetItemsAsync(videoPlatformSchedule =>
                videoPlatformSchedule.RequestorEventType == requestorEventType
                && videoPlatformSchedule.RequestorLiveEventId == requestorLiveEventId)
                .ConfigureAwait(false);

            return existingVideoPlatformSchedules;
        }

        /// <summary>
        /// Gets the media level video platform schedules for live event.
        /// </summary>
        /// <param name="requestorLiveEventId">The requestor live event identifier.</param>
        /// <param name="requestorEventType">Type of the requestor event.</param>
        /// <returns>List of Media Level VideoPlatformSchedules for the live event.</returns>
        private async Task<IEnumerable<VideoPlatformSchedule>> GetMediaLevelVideoPlatformSchedulesForLiveEventAsync(string requestorLiveEventId, string requestorEventType)
        {
            var videoPlatformScheduleRepository = this.repositoryFactory.Resolve<VideoPlatformSchedule>();
            var existingVideoPlatformSchedules = await videoPlatformScheduleRepository.GetItemsAsync(videoPlatformSchedule =>
                videoPlatformSchedule.RequestorEventType == requestorEventType
                && videoPlatformSchedule.RequestorLiveEventId == requestorLiveEventId
                && videoPlatformSchedule.RequestorLiveEventScheduleId != null)
                .ConfigureAwait(false);

            return existingVideoPlatformSchedules;
        }

        /// <summary>
        /// Adds the <see cref="WorkflowIntent"/> to the <see cref="ScheduleChangeRequest"/>.
        /// </summary>
        /// <param name="workflowIntent">The <see cref="WorkflowIntent"/>.</param>
        /// <param name="scheduleChangeRequest">The <see cref="ScheduleChangeRequest"/>.</param>
        private void AddWorkflowIntentToScheduleChangeRequest(WorkflowIntent workflowIntent, ScheduleChangeRequest scheduleChangeRequest)
        {
            var isValidWorkflowIntent = workflowIntent?.ActorSpecificDetails?.Any() ?? false;
            if (isValidWorkflowIntent)
            {
                scheduleChangeRequest.WorkflowIntents.Add(workflowIntent);
            }
        }

        /// <summary>
        /// Adds the <see cref="WorkflowIntent"/> to the <see cref="ScheduleChangeRequest"/>.
        /// </summary>
        /// <param name="workflowIntentList">The <see cref="WorkflowIntent"/>.</param>
        /// <param name="scheduleChangeRequest">The <see cref="ScheduleChangeRequest"/>.</param>
        private void AddMultipleWorkflowIntentToScheduleChangeRequest(ICollection<WorkflowIntent> workflowIntentList, ScheduleChangeRequest scheduleChangeRequest)
        {
            foreach (var workflowIntent in workflowIntentList)
            {
                var isValidWorkflowIntent = workflowIntent?.ActorSpecificDetails?.Any() ?? false;
                if (isValidWorkflowIntent)
                {
                    scheduleChangeRequest.WorkflowIntents.Add(workflowIntent);
                }
            }
        }
        private TimeSpan? SetWorkflowOffset(IEnumerable<IntentMetaData> offsets, string key)
        {
            return offsets.Select(x => SetWorkflowOffset(x, key)).FirstOrDefault();
        }

        private TimeSpan? SetWorkflowOffset(IntentMetaData offset, string key)
        {
            return offset.CustomOffsets?.Where(x => x.Key == key)
            .Select(x => x.Value)
            .FirstOrDefault();
        }
    }
}
#pragma warning restore CA1506
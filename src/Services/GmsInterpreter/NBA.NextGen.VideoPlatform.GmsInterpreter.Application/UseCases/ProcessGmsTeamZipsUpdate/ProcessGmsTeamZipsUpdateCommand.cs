// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsTeamZipsUpdateCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsTeamZipsUpdate
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// The publish event command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class ProcessGmsTeamZipsUpdateCommand : IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the team identifier.
        /// </summary>
        /// <value>
        /// The game identifier.
        /// </value>
        public string Id { get; set; }
    }
}

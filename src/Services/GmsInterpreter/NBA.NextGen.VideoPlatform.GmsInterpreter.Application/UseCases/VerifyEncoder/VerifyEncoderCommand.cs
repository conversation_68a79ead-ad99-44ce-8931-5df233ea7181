// "//-----------------------------------------------------------------------".
// <copyright file="VerifyEncoderCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.VerifyEncoder
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// The verify Encoder command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class VerifyEncoderCommand : IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the current evaluation time.
        /// </summary>
        /// <value>
        /// The Current evaluation time.
        /// </value>
        public DateTime CurrentEvaluation { get; set; }

        /// <summary>
        /// Gets or sets the verification period.
        /// </summary>
        /// <value>
        /// The period for evaluation.
        /// </value>
        public int VerificationPeriodInDays { get; set; }
    }
}

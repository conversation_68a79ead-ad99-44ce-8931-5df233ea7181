// "//-----------------------------------------------------------------------".
// <copyright file="GameEventUpdateIgnoreSettings.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Models
{
    /// <summary>
    /// GameEventUpdateIgnoreSettings.
    /// </summary>
    public class GameEventUpdateIgnoreSettings
    {
        /// <summary>
        /// Gets or sets the minutes until updates not accepted.
        /// </summary>
        /// <value>
        /// The minutes until updates not accepted.
        /// </value>
        public double MinutesUntilUpdatesNotAccepted { get; set; }
    }
}

include .env

aquilaHealthReporter:
	docker compose -f AquilaActor/docker-compose.yml -f AquilaActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) aquila-health-reporter && docker compose -f AquilaActor/docker-compose.yml -f AquilaActor/local-compose.yml up aquila-health-reporter

aquilaComposer:
	docker compose -f AquilaActor/docker-compose.yml -f AquilaActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) aquila-processor && docker compose -f AquilaActor/docker-compose.yml -f AquilaActor/local-compose.yml up aquila-processor

aquilaWDComposer:
	docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) aquila-watchdog-processor && docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml up aquila-watchdog-processor

aquilaWDSourceJob:
	docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) aquila-source-job && docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml up aquila-source-job

aquilaWDChannelJob:
	docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) aquila-channel-job && docker compose -f AquilaWatchdog/docker-compose.yml -f AquilaWatchdog/local-compose.yml up aquila-channel-job

prismaProcessor:
	docker compose -f PrismaActor/docker-compose.yml -f PrismaActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) prisma-actor-processor && docker compose -f PrismaActor/docker-compose.yml -f PrismaActor/local-compose.yml up prisma-actor-processor

makename:
	docker compose -f seervice/docker-compose.yml -f seervice/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) composename && docker compose -f seervice/docker-compose.yml -f seervice/local-compose.yml up composename
	

dmmApi:
	docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) dmmactor-api && docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml up dmmactor-api

dmmProcessor:
	docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) dmmactor-processor && docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml up dmmactor-processor

dmmHealth:
	docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) dmmactor-healthcheckcron && docker compose -f DmmActor/docker-compose.yml -f DmmActor/local-compose.yml up dmmactor-healthcheckcron

orchestratorProcessor:
	docker compose -f Orchestrator/docker-compose.yml -f Orchestrator/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) orchestrator-processor && docker compose -f Orchestrator/docker-compose.yml -f Orchestrator/local-compose.yml up orchestrator-processor

schedulerCron:
	docker compose -f Scheduler/docker-compose.yml -f Scheduler/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) scheduler-synchronization-cron && docker compose -f Scheduler/docker-compose.yml -f Scheduler/local-compose.yml up scheduler-synchronization-cron

schedulerApi:
	docker compose -f Scheduler/docker-compose.yml -f Scheduler/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) scheduler-api && docker compose -f Scheduler/docker-compose.yml -f Scheduler/local-compose.yml up scheduler-api

gmsWatchdogApi:
	docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) gmswatchdog-api && docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml up gmswatchdog-api

gmsWatchdogProcessor:
	docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) gms-watchdog-processor && docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml up gms-watchdog-processor

gmswdGmsEventPollingJob:
	docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) gms-event-polling-job && docker compose -f gmswatchdog/docker-compose.yml -f gmswatchdog/local-compose.yml up gms-event-polling-job

tvpConsumers:
	docker compose -f tvpactor/docker-compose.yml -f tvpactor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) tvp-consumers && docker compose -f tvpactor/docker-compose.yml -f tvpactor/local-compose.yml up tvp-consumers

tvpApi:
	docker compose -f tvpactor/docker-compose.yml -f tvpactor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) tvp-api && docker compose -f tvpactor/docker-compose.yml -f tvpactor/local-compose.yml up tvp-api

thirdpartyActorProcessor:
	docker compose -f ThirdPartyActor/docker-compose.yml -f ThirdPartyActor/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) third-party-processor && docker compose -f ThirdPartyActor/docker-compose.yml -f ThirdPartyActor/local-compose.yml up third-party-processor

gmsinterpreterProcessor:
	docker compose -f GmsInterpreter/docker-compose.yml -f GmsInterpreter/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) Gms-Interpreter-Processor && docker compose -f GmsInterpreter/docker-compose.yml -f GmsInterpreter/local-compose.yml up Gms-Interpreter-Processor

StreamMarkerProcessor:
	docker compose -f StreamMarker/docker-compose.yml -f StreamMarker/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) streamMarker-processor && docker compose -f StreamMarker/docker-compose.yml -f StreamMarker/local-compose.yml up streamMarker-processor	

StreamMarkerApi:
	docker compose -f StreamMarker/docker-compose.yml -f StreamMarker/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) streamMarker-api && docker compose -f StreamMarker/docker-compose.yml -f StreamMarker/local-compose.yml up streamMarker-api	

healthCron:
	docker compose -f GlobalHealthIndicator/docker-compose.yml -f GlobalHealthIndicator/local-compose.yml build --build-arg FEED_ACCESSTOKEN=$(NUGET_PAT) global-health-cron && docker compose -f GlobalHealthIndicator/docker-compose.yml -f GlobalHealthIndicator/local-compose.yml up global-health-cron

.PHONY: dmmApi dmmProcessor dmmHealth orchestratorProcessor schedulerCron schedulerApi gmsWatchdogApi tvpConsumers tvpApi healthCron

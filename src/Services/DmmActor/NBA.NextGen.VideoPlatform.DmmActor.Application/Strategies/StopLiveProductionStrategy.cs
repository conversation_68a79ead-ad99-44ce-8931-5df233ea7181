using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Interfaces;
using NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StopLiveProduction;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Newtonsoft.Json;

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies;

public class StopLiveProductionStrategy : IDmmStrategy
{
    public bool CanProcess(string workflowId) => workflowId == NbaWorkflowIds.ProcessGameEndMarker || workflowId == NbaWorkflowIds.EventLiveProductionServicesStop;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<StopLiveProductionStrategy> _logger;   

    public StopLiveProductionStrategy(IMediator mediator, IMapper mapper, ILogger<StopLiveProductionStrategy> logger){
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task HandleAsync(string message)
    {
        var request = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<LiveProductionServicesDetails>>(message);
        _logger.LogInformation($"StopLiveProductionStrategy started for {request.CorrelationId}");
        var cmd = _mapper.Map<StopLiveProductionCommand>(request);
        _logger.LogInformation($"Converted InfrastructureStateChangeRequest to StopLiveProductionCommand successfully for {cmd.CorrelationId}");
        
        await _mediator.Send(cmd);
        _logger.LogInformation($"StopLiveProductionStrategy ending for {cmd.CorrelationId}");
    }
}
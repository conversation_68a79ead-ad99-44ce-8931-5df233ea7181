// "//-----------------------------------------------------------------------".
// <copyright file="StopLiveProductionCommandValidator.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StopLiveProduction
{
    using FluentValidation;

    /// <summary>
    /// StartLiveProductionCommand Validator.
    /// </summary>
    public class StopLiveProductionCommandValidator : AbstractValidator<StopLiveProductionCommand>
    {
        /// <summary>
        /// The maximum character length.
        /// </summary>
        public static readonly int MaxCharLength = 30;

        /// <summary>
        /// Initializes a new instance of the <see cref="StopLiveProductionCommandValidator" /> class.
        /// </summary>
        public StopLiveProductionCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.MediaId).NotNull().WithMessage("ProductionId cannot be null.");
            this.RuleFor(x => x.MediaId).NotEmpty().WithMessage("ProductionId cannot be empty.");
        }
    }
}

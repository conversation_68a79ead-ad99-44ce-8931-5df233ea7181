// "//-----------------------------------------------------------------------".
// <copyright file="GetOrchestratorQueryHandler.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Queries
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using NBA.NextGen.VideoPlatform.DmmActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Returns the name of the Orchestration based on the workflow.
    /// </summary>
    public class GetOrchestratorQueryHandler : IRequestHandler<GetOrchestratorQuery, string>
    {
        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public Task<string> Handle([NotNull] GetOrchestratorQuery request, CancellationToken cancellationToken) => Task.FromResult(request.WorkflowId switch
        {
            NbaWorkflowIds.ProcessGameStartMarker => OrchestrationNames.StartContentProtectionAndLiveProductionRequest,
            NbaWorkflowIds.ProcessGameEndMarker => OrchestrationNames.StopLiveProductionRequest,
            NbaWorkflowIds.EventContentProtectionStart => OrchestrationNames.StartContentProtectionRequest,
            NbaWorkflowIds.EventContentProtectionStop => OrchestrationNames.StopContentProtectionRequest,
            NbaWorkflowIds.EventLiveProductionServicesStart => OrchestrationNames.StartLiveProductionRequest,
            NbaWorkflowIds.EventLiveProductionServicesStop => OrchestrationNames.StopLiveProductionRequest,
            _ => throw new NotSupportedException()
        });
    }
}

using System.Diagnostics;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using NBA.NextGen.VideoPlatform.DmmActor.Api;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", false, true)
    .AddJsonFile("local.settings.json", true, true)
    .AddAmazonSecretsManager(builder.Configuration)
    .AddParameterStore(builder.Configuration)
    .AddEnvironmentVariables();

if (Debugger.IsAttached)
    builder.Configuration.AddJsonFile("appsettings.debug.json", true, true);

builder.Services.AddApplicationInsightsTelemetryWorkerService(builder.Configuration);
builder.Services.AddApplicationDependencies(builder.Configuration);

builder.Services.AddHealthChecks();

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.MapHealthChecks("/healthz", new HealthCheckOptions()
{
    Predicate = _ => true, ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
app.MapHealthChecks("/readyz", new HealthCheckOptions()
{
    Predicate = _ => true, ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

app.Run();
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using NBA.NextGen.VideoPlatform.DmmActor.Processor;
using NBA.NextGen.VideoPlatform.DmmActor.Processor.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using System.Diagnostics;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", false, true)
    .AddJsonFile("local.settings.json", true, true)
    .AddAmazonSecretsManager(builder.Configuration)
    .AddParameterStore(builder.Configuration)
    .AddEnvironmentVariables();

if (Debugger.IsAttached)
    builder.Configuration.AddJsonFile("appsettings.debug.json", true, true);

builder.Services.AddApplicationInsightsTelemetryWorkerService(builder.Configuration);
builder.Services.AddApplicationDependencies(builder.Configuration);

builder.Services.AddHealthChecks()
                .AddCheck<MemoryHealthCheck>("Memory Check", HealthStatus.Unhealthy, ["DmmActor.Consumer"]);

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();


// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();


app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/readyz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.Run();
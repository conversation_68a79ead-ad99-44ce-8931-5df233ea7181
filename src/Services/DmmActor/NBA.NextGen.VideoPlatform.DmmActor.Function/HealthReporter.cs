namespace NBA.NextGen.VideoPlatform.DmmActor.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.DmmActor.Application.UserCases.Health.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;

    /// <seealso cref="NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup.FunctionBase" />
    public class HealthReporter : FunctionBase
    {
        public HealthReporter(IMediator mediator, ILogger<HealthReporter> logger, IMapper mapper)
            : base(mediator, logger, mapper)
        {
        }
        
        [FunctionName("ReportHealth")]
        public Task ReportHealthAsync(
                [TimerTrigger("%HealthTriggerCron%")][NotNull] TimerInfo timer)
        {
            timer.Required(nameof(timer));
            return this.ProcessAsync<PublishDmmActorHealthCommand>();
        }
    }
}

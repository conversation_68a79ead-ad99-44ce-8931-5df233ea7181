using Azure;
using Azure.Messaging.EventGrid;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using MST.Common.Azure.Extensions;
using MST.Common.Extensions;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UserCases.Health.Commands;
using NBA.NextGen.VideoPlatform.DmmActor.Application.UserCases.Health.Commands;
using NBA.NextGen.VideoPlatform.PrismaActor.Application.UserCases.Health.Commands;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Health.Commands.PublishHealth;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UserCases.Health.Commands;

var builder = Host.CreateApplicationBuilder(args);
var commonKey = "VideoPlatformCommon";

// Add shared config sources
builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", false, true)
                    .AddParameterStore(builder.Configuration)
                    .AddParameterStore(builder.Configuration, commonKey, "AquilaActor")
                    .AddParameterStore(builder.Configuration, commonKey, "DmmActor")
                    .AddParameterStore(builder.Configuration, commonKey, "PrismaActor")
                    .AddParameterStore(builder.Configuration, commonKey, "TvpActor")
                    .AddAmazonSecretsManager(builder.Configuration)
                    .AddEnvironmentVariables();

// Register services
builder.Services.AddSingleton<Microsoft.ApplicationInsights.TelemetryClient>();
builder.Services.AddSharedInfrastructure();
builder.Services.AddVideoPlatformSharedInfrastructure(builder.Configuration);
builder.Services.AddAquilaSharedInfrastructure(builder.Configuration);
builder.Services.AddDmmSharedInfrastructure();
builder.Services.AddTvpSharedInfrastructure(builder.Configuration);
builder.Services.AddMSTInfrastructure();

NBA.NextGen.VideoPlatform.AquilaActor.Application.RegisterServices.AddApplicationV2(builder.Services);
NBA.NextGen.VideoPlatform.DmmActor.Application.RegisterServices.AddApplicationV2(builder.Services);
NBA.NextGen.VideoPlatform.PrismaActor.Application.RegisterServices.AddApplication(builder.Services, builder.Configuration);
PrismaActor.Processor.ApplicationExtensions.AddApplicationDependencies(builder.Services, builder.Configuration);
NBA.NextGen.VideoPlatform.TvpActor.Application.RegisterServices.AddApplication(builder.Services, builder.Configuration);

builder.Services.AddEventGrid(builder.Configuration);
builder.Services.AddRefitClients(builder.Configuration);
builder.Services.Configure<NotifierSettings>(builder.Configuration.GetSection("NotifierSettings"));

var provider = builder.Services.BuildServiceProvider();

// Add Event Grid Notifier for VideoPlatform Topic
var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();
var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
if (vpNotifierSettings is null)
{
    throw new NullReferenceException("VideoPlatform topic not found in NotifierSettings.");
}

var eventKey = new AzureKeyCredential(builder.Configuration["event_grid_key"]);
var egClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey);
builder.Services.AddKeyedSingleton(TopicNames.VideoPlatform, egClient);
builder.Services.RegisterEventGridSender<ServiceHealthChangedEvent>(
    nameof(ServiceHealthChangedEvent),
    x => nameof(ServiceHealthChangedEvent),
    keyedServiceName: TopicNames.VideoPlatform
);

using IHost host = builder.Build();

var tasks = new List<Task>();
var mediator = host.Services.GetService<IMediator>() ?? throw new ArgumentNullException(nameof(IMediator));

// Launch health check commands
tasks.Add(mediator.Send(new PublishAquilaActorHealthCommand()));
tasks.Add(mediator.Send(new PublishDmmActorHealthCommand()));
tasks.Add(mediator.Send(new PublishPrismaActorHealthCommand()));
tasks.Add(mediator.Send(new PublishTvpActorHealthCommand()));
tasks.Add(mediator.Send(new PublishTvpActorHealthCommandV2()));

await Task.WhenAll(tasks);
// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckFunctionBase.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Function
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading.Tasks;
    using MediatR;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;

    /// <summary>
    /// The <see cref="HealthCheckFunctionBase"/>.
    /// </summary>
    public abstract class HealthCheckFunctionBase
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The date time.
        /// </summary>
        private readonly IDateTime dateTime;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckFunctionBase"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="dateTime">The date time.</param>
        protected HealthCheckFunctionBase(
            IMediator mediator,
            IDateTime dateTime)
        {
            this.mediator = mediator;
            this.dateTime = dateTime;
        }

        /// <summary>
        /// Send the commands to their handlers and returns a summary of the results.
        /// </summary>
        /// <param name="healthCheckCommands">List of health check command.</param>
        /// <returns>Returns a task with the <see cref="HealthCheckResponse"/>.</returns>
        protected async Task<HealthCheckResponse> ExecuteHealthChecksAsync([NotNull] IEnumerable<HealthCheckCommand> healthCheckCommands)
        {
            var response = new HealthCheckResponse();
            var tasks = new List<Task<HealthCheckDetail>>();
            foreach (var command in healthCheckCommands)
            {
                tasks.Add(this.mediator.Send(command));
            }

            var healthCheckDetails = await Task.WhenAll(tasks).ConfigureAwait(false);
            if (healthCheckDetails.Any(x => x.Status == HealthStatus.Unhealthy))
            {
                response.Status = HealthStatus.Unhealthy;
            }

            response.Detail = healthCheckDetails;
            response.Date = this.dateTime.Now;
            return response;
        }
    }
}

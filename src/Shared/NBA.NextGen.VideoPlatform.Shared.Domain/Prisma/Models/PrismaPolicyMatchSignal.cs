// "//-----------------------------------------------------------------------".
// <copyright file="PrismaPolicyMatchSignal.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Enums;

    /// <summary>
    /// Match Signal of a policy.
    /// </summary>
    public class PrismaPolicyMatchSignal
    {
        /// <summary>
        /// Gets or sets the Match.
        /// </summary>
        /// <value>
        /// The Match.
        /// </value>
        public PrismaMatchSignalMatch Match { get; set; }

        /// <summary>
        /// Gets or sets the Match.
        /// </summary>
        /// <value>
        /// The Match.
        /// </value>
        public IEnumerable<string> Assert { get; set; }
    }
}

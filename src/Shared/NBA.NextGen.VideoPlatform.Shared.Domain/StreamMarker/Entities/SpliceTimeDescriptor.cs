// "//-----------------------------------------------------------------------".
// <copyright file="SpliceTimeDescriptor.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The splice time descriptor.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SpliceTimeDescriptor
    {
        /// <summary>
        /// Gets or sets the Tai seconds.
        /// </summary>
        public long TaiSeconds { get; set; }

        /// <summary>
        /// Gets or sets the Tai NS.
        /// </summary>
        public long TaiNs { get; set; }

        /// <summary>
        /// Gets or sets the UTC offset.
        /// </summary>
        public long UtcOffset { get; set; }
    }
}

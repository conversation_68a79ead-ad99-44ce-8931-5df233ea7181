// "//-----------------------------------------------------------------------".
// <copyright file="SpliceScheduleComponent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The splice schedule component.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SpliceScheduleComponent : Component
    {
        /// <summary>
        /// Gets or sets the splice time in ISO8601 UTC time.
        /// </summary>
        public string UtcSpliceTime { get; set; }
    }
}

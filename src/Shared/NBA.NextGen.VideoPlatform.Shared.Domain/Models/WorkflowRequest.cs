// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The model for the <see cref="WorkflowRequest"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class WorkflowRequest : RequestBase
    {
        /// <summary>
        /// Gets or sets the requestor live event identifier.
        /// </summary>
        /// <value>
        /// The requestor live event identifier.
        /// </value>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the schedule which prompts this change.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the WorkflowIntent needed at this time.
        /// </summary>
        public WorkflowIntent WorkflowIntent { get; set; }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="AquilaChannelState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Aquila channel states.
    /// </summary>
    public enum AquilaChannelState
    {
        /// <summary>
        /// Stopped state.
        /// </summary>
        [EnumMember(Value = "creation_error")]
        CreationError,

        /// <summary>
        /// Stopped state.
        /// </summary>
        [EnumMember(Value = "stopped")]
        Stopped,

        /// <summary>
        /// StartRequested state.
        /// </summary>
        [EnumMember(Value = "start_request_error")]
        StartRequestError,

        /// <summary>
        /// StartRequested state.
        /// </summary>
        [EnumMember(Value = "start_requested")]
        StartRequested,

        /// <summary>
        /// InfraCreating state.
        /// </summary>
        [EnumMember(Value = "infra_creating")]
        InfraCreating,

        /// <summary>
        /// Stopped state.
        /// </summary>
        [EnumMember(Value = "starting")]
        Starting,

        /// <summary>
        /// Stopped state.
        /// </summary>
        [EnumMember(Value = "started")]
        Started,

        /// <summary>
        /// StopRequested state.
        /// </summary>
        [EnumMember(Value = "stop_requested")]
        StopRequested,

        /// <summary>
        /// Stopping state.
        /// </summary>
        [EnumMember(Value = "stopping")]
        Stopping,

        /// <summary>
        /// StartError state.
        /// </summary>
        [EnumMember(Value = "start_error")]
        StartError,

        /// <summary>
        /// Deletion state.
        /// </summary>
        [EnumMember(Value = "deleted")]
        Deleted,

        /// <summary>
        /// Configured state.
        /// </summary>
        [EnumMember(Value = "configured")]
        Configured,
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckDetail.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Health
{
    using System.Diagnostics.CodeAnalysis;
    using System.Text.Json.Serialization;
    using NBA.NextGen.Shared.Domain.Enums;

    /// <summary>
    /// The health check detail.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class HealthCheckDetail
    {
        /// <summary>
        /// Gets or sets the resource.
        /// </summary>
        public string Resource { get; set; }

        /// <summary>
        /// Gets or sets the detail.
        /// </summary>
        public string Detail { get; set; }

        /// <summary>
        /// Gets or sets the status of the resource.
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public HealthStatus Status { get; set; }
    }
}

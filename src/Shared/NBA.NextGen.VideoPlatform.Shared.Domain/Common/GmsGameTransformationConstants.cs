// "//-----------------------------------------------------------------------".
// <copyright file="GmsGameTransformationConstants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    /// <summary>
    /// The Standard Entitlement Names.
    /// </summary>
    public static class GmsGameTransformationConstants
    {
        /// <summary>
        /// The standard entitlement.
        /// </summary>
        public static readonly string DefaultEntitlement = "League Pass";

        /// <summary>
        /// The supported media type.
        /// </summary>
        public static readonly string SupportedMediaType = "nss";

        /// <summary>
        /// The media rule.
        /// </summary>
        public static readonly string MediaRule = "Media.";

        /// <summary>
        /// The game rule.
        /// </summary>
        public static readonly string GameRule = "Game.";

        /// <summary>
        /// The game rule.
        /// </summary>
        public static readonly string EventRule = "Event";

        /// <summary>
        /// The Domestic Team Choice Prefix.
        /// </summary>
        public static readonly string DomesticTeamChoicePrefix = "NBATP-";

        /// <summary>
        /// The service collection prefix.
        /// </summary>
        public static readonly string ServiceCollectionPrefix = "G";

        /// <summary>
        /// The service collection prefix.
        /// </summary>
        public static readonly string EventCollectionPrefix = "E";

        /// <summary>
        /// The Schedule change request session service bus.
        /// </summary>
        public static readonly string SCRSessionServiceBusMessage = "SCRSessionServiceBusMessage";

        /// <summary>
        /// The geo restriction.
        /// </summary>
        public static readonly string GeoRestriction = "ContentRestriction-";

        /// <summary>
        /// The geo restriction allowed.
        /// </summary>
        public static readonly string GeoRestrictionAllowed = "-Allow";

        /// <summary>
        /// The geo restriction denied.
        /// </summary>
        public static readonly string GeoRestrictionDenied = "-Block";

        /// <summary>
        /// The dummy production package name.
        /// </summary>
        public static readonly string DummyProductionPackageName = "LGFP";

        /// <summary>
        /// The dummy production label key.
        /// </summary>
        public static readonly string DummyProductionNameSuffix = "placeholder";

        /// <summary>
        /// The GMS entity identifier length, more info in the ADO Wiki: https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/767/GMS-How-Does-It-Work-.
        /// </summary>
        public static readonly int GmsEntityIdLength = 10;

        /// <summary>
        /// The GMS media identifier length, more info in the ADO Wiki: https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/1766/Production-IDs.
        /// </summary>
        public static readonly int GmsMediaIdLength = 7;

        /// <summary>
        /// The GMS team abbreviation length, more info in the ADO Wiki: https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/1766/Production-IDs.
        /// </summary>
        public static readonly int GmsTeamAbbrLength = 3;
    }
}

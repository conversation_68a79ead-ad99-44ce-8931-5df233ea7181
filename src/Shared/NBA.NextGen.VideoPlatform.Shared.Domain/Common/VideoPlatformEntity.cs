// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformEntity.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    using NBA.NextGen.Shared.Domain.Common;
    using Newtonsoft.Json;

    /// <summary>
    /// The video paltform repositories entities.
    /// </summary>
    /// <typeparam name="T">The primary key type.</typeparam>
    /// <seealso cref="NBA.NextGen.Shared.Domain.Common.Entity{T}" />
    public abstract class VideoPlatformEntity<T> : Entity<T>
    {
        /// <summary>
        /// Gets the type this will be the partition key in CosmosDB following https://dev.azure.com/nbadev/DTC/_workitems/edit/11302.
        /// </summary>
        /// <value>
        /// The type.
        /// </value>
        [JsonProperty(nameof(Type))]
        public string Type
        {
            get { return this.GetType().Name; }
#pragma warning disable S108 // Nested blocks of code should not be left empty
#pragma warning disable S3237 // "value" parameters should be used, cosmos requires a setter property for it to serialize values
            private set { }
#pragma warning restore S3237 // "value" parameters should be used
#pragma warning restore S108 // Nested blocks of code should not be left empty
        }
    }
}

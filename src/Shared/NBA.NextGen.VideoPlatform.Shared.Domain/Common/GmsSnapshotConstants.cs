// "//-----------------------------------------------------------------------".
// <copyright file="GmsSnapshotConstants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    /// <summary>
    /// Gms Snapshot Constants.
    /// </summary>
    public static class GmsSnapshotConstants
    {
        /// <summary>
        /// The Gms Snapshot Hub Name.
        /// </summary>
        public const string GmsSnapshotHub = "GmsSnapshotHub";

        /// <summary>
        /// The Task Hub Connection Name.
        /// </summary>
        public const string TaskHubConnectionName = "Storage";

        /// <summary>
        /// The name of the orchestration function that is responsible of running Gms Snapshot orchestration.
        /// </summary>
        public const string GmsSnapshotOrchestrationFunctionName = "GmsSnapshotOrchestration";

        /// <summary>
        /// The Capture Snapshot Activity.
        /// </summary>
        public const string CaptureSnapshotActivity = nameof(CaptureSnapshotActivity);

        /// <summary>
        /// The Get Season League Activity.
        /// </summary>
        public const string GetSeasonLeagueActivity = nameof(GetSeasonLeagueActivity);

        /// <summary>
        /// Gets or Sets Snapshot BContainer.
        /// </summary>
        public const string SnapshotContainer = "gamesnapshot";
    }
}

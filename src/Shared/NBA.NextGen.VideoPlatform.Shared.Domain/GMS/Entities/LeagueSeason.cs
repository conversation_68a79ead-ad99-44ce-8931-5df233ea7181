// "//-----------------------------------------------------------------------".
// <copyright file="LeagueSeason.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

    /// <summary>
    /// League Season.
    /// </summary>
    /// <seealso cref="Entity{TKey}" />
    public sealed class LeagueSeason : Entity<int>
    {
        /// <summary>
        /// Gets or sets the league identifier.
        /// </summary>
        /// <value>
        /// The league identifier.
        /// </value>
        public League LeagueId { get; set; }

        /// <summary>
        /// Gets or sets the season.
        /// </summary>
        /// <value>
        /// The season.
        /// </value>
        public string Season { get; set; }

        /// <summary>
        /// Gets or sets the start date.
        /// </summary>
        /// <value>
        /// The start date.
        /// </value>
        public DateTimeOffset StartDate { get; set; }

        /// <summary>
        /// Gets or sets the end date.
        /// </summary>
        /// <value>
        /// The end date.
        /// </value>
        public DateTimeOffset EndDate { get; set; }
    }
}

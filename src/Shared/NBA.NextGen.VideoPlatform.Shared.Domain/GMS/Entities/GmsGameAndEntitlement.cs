// "//-----------------------------------------------------------------------".
// <copyright file="GmsGameAndEntitlement.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    /// <summary>
    /// GameEntitlement Class.
    /// </summary>
    public class GmsGameAndEntitlement
    {
        /// <summary>
        /// Gets or sets the Entitlements of the game.
        /// </summary>
        /// <value>
        /// The Entitlements.
        /// </value>
        public GmsEntitlement Entitlement { get; set; }

        /// <summary>
        /// Gets or sets the GmsGame.
        /// </summary>
        /// <value>
        /// The GmsGame.
        /// </value>
        public GmsGame GmsGame { get; set; }
    }
}
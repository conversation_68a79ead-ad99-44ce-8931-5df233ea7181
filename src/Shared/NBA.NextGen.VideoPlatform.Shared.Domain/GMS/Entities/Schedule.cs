// "//-----------------------------------------------------------------------".
// <copyright file="Schedule.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System;

    /// <summary>
    /// Schedule.
    /// </summary>
    public sealed class Schedule
    {
        /// <summary>
        /// Gets or sets the Id. NBA chose to use long for schedule.id in GMS data. To be consistent and to avoid confusion, the decision is to use long for ID too.
        /// </summary>
        /// <value>
        /// The ID.
        /// </value>
        public long Id { get; set; }

        /// <summary>
        /// Gets or sets the language.
        /// </summary>
        /// <value>
        /// The language.
        /// </value>
        public string Language { get; set; }

        /// <summary>
        /// Gets or sets the URL.
        /// </summary>
        /// <value>
        /// The URL.
        /// </value>
        public string Endpoint { get; set; }

        /// <summary>
        /// Gets or sets the team context.
        /// </summary>
        /// <value>
        /// The team context.
        /// </value>
        public string TeamContext { get; set; }

        /// <summary>
        /// Gets or sets the channel.
        /// </summary>
        /// <value>
        /// The channel.
        /// </value>
        public string Channel { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [tape delay].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [tape delay]; otherwise, <c>false</c>.
        /// </value>
        public bool TapeDelay { get; set; }

        /// <summary>
        /// Gets or sets the operations.
        /// </summary>
        /// <value>
        /// The operations.
        /// </value>
        public Operation Operations { get; set; }

        /// <summary>
        /// Gets or sets the last updated.
        /// </summary>
        /// <value>
        /// The last updated.
        /// </value>
        public DateTimeOffset LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this <see cref="Schedule"/> is active.
        /// </summary>
        /// <value>
        ///   <c>true</c> if active; otherwise, <c>false</c>.
        /// </value>
        public bool Active { get; set; }

        /// <summary>
        /// Gets or sets the resolution.
        /// </summary>
        /// <value>
        /// The resolution.
        /// </value>
        public string Resolution { get; set; }
    }
}

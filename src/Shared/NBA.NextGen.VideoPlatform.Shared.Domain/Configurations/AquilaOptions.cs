namespace NBA.NextGen.VideoPlatform.Shared.Domain.Configuration
{
    using System.Diagnostics.CodeAnalysis;

    [ExcludeFromCodeCoverage]
    public class AquilaOptions : MediaKindOptions
    {
        public string AccountId { get; set; } // now pointing at AWS
    }

    public class MediaKindOptions
    {
        public string UserEmail { get; set; }
        public string AccountName { get; set; }
        public string Password { get ; set; }
        public string AuthEndpoint { get; set; }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="TvpEventSchedule.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities
{
    using System;
    using System.Collections.Generic;

    /// <summary>
    /// Schedule for a TV platform event.
    /// </summary>
    public class TvpEventSchedule
    {
        /// <summary>
        /// Gets or sets the external identifier.
        /// </summary>
        /// <value>
        /// The external identifier.
        /// </value>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the actual start UTC.
        /// </summary>
        /// <value>
        /// The actual start UTC.
        /// </value>
        public DateTimeOffset? ActualStartUtc { get; set; }

        /// <summary>
        /// Gets or sets the actual end UTC.
        /// </summary>
        /// <value>
        /// The actual end UTC.
        /// </value>
        public DateTimeOffset? ActualEndUtc { get; set; }

        /// <summary>
        /// Gets or sets the start UTC.
        /// </summary>
        /// <value>
        /// The start UTC.
        /// </value>
        public DateTimeOffset? StartUtc { get; set; }

        /// <summary>
        /// Gets or sets the end UTC.
        /// </summary>
        /// <value>
        /// The end UTC.
        /// </value>
        public DateTimeOffset? EndUtc { get; set; }

        /// <summary>
        /// Gets or sets the upid.
        /// </summary>
        /// <value>
        /// The upid.
        /// </value>
        public string Upid { get; set; }

        /// <summary>
        /// Gets or sets the productions.
        /// </summary>
        /// <value>
        /// The productions.
        /// </value>
        public IList<TvpProduction> Productions { get; set; }
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="ClientsStatus.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    using System.Collections.Generic;

    /// <summary>
    /// Content Protection clients.
    /// </summary>
    public class ClientsStatus
    {
        /// <summary>
        /// Gets or sets or set the Name.
        /// </summary>
        public string ClientName { get; set; }

        /// <summary>
        /// Gets or sets or set the Angle.
        /// </summary>
        public string Angle { get; set; }

        /// <summary>
        /// Gets or sets or set the Message.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets or set the Status.
        /// </summary>
        public string Status { get; set; }
    }
}
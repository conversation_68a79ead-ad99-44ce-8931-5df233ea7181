// "//-----------------------------------------------------------------------".
// <copyright file="IAquilaClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using NBA.NextGen.Shared.Application.HealthDetails;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;

    /// <summary>
    /// Methods for Aquila APIs.
    /// </summary>
    public interface IAquilaClientService : IHealthStatus
    {
        /// <summary>
        /// Gets the templates asynchronous.
        /// </summary>
        /// <returns>The templates.</returns>
        Task<IEnumerable<Template>> GetTemplatesAsync();

        /// <summary>
        /// Gets the sources asynchronous.
        /// </summary>
        /// <returns>The sources.</returns>
        Task<IEnumerable<Source>> GetSourcesAsync();

        /// <summary>
        /// Gets the source by its id asynchronous.
        /// </summary>
        /// <returns>The sources.</returns>
        /// <param name="sourceId">The source id.</param>
        Task<Source> GetSourceByIdAsync(string sourceId);

        /// <summary>
        /// Gets the sources asynchronous.
        /// </summary>
        /// <returns>The sources.</returns>
        Task<IEnumerable<Channel>> GetChannelsAsync();

        /// <summary>
        /// Gets the sources asynchronous.
        /// </summary>
        /// <returns>The sources.</returns>
        Task<IEnumerable<Whitelist>> GetWhitelistsAsync();

        /// <summary>
        /// Updates the whitelist.
        /// </summary>
        /// <param name="whitelist">The whitelist.</param>
        /// <param name="bookId">The bookid.</param>
        /// <returns>A task.</returns>
        Task UpdateWhitelistAsync(Whitelist whitelist, string bookId);

        /// <summary>
        /// Creates the whitelist.
        /// </summary>
        /// <param name="whitelist">The whitelist.</param>
        /// <returns>A task.</returns>
        Task CreateWhitelistAsync(Whitelist whitelist);

        /// <summary>
        /// Gets the channel by identifier asynchronous.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>Task of channel.</returns>
        Task<Channel> GetChannelByIdAsync(string channelId);

        /// <summary>
        /// Gets the channel by identifier asynchronous if Channel is available.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>Task of channel.</returns>
        Task<Channel> TryGetChannelByIdAsync(string channelId);

        /// <summary>
        /// Starts the channel instance asynchronous.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="instanceId">The channel instance identifier.</param>
        /// <returns>A task.</returns>
        Task StartChannelInstanceAsync(string channelId, string instanceId);

        /// <summary>
        /// Stops the channel instance.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="instanceId">The channel instance identifier.</param>
        /// <returns>A task.</returns>
        Task StopChannelInstanceAsync(string channelId, string instanceId);

        /// <summary>
        /// Creates the channel asynchronous.
        /// </summary>
        /// <param name="channel">The channel.</param>
        /// <returns>A Task.</returns>
        Task CreateChannelAsync(ChannelCreationInfo channel);

        /// <summary>
        /// Updates the channel asynchronous.
        /// </summary>
        /// <param name="channel">The channel.</param>
        /// <returns>The task.</returns>
        Task UpdateChannelAsync(ChannelCreationInfo channel);

        /// <summary>
        /// Deletes the channel asynchronous.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>A task.</returns>
        Task DeleteChannelAsync(string channelId);
    }
}

// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckAquilaCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;

    /// <summary>
    /// The aquila API health check command handler.
    /// </summary>
    public class HealthCheckAquilaCommandHandler : HealthCheckCommandHandler, IRequestHandler<HealthCheckAquilaCommand, HealthCheckDetail>
    {
        /// <summary>
        /// The resource.
        /// </summary>
        private const string Resource = "Aquila API";

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckAquilaCommandHandler" /> class.
        /// </summary>
        /// <param name="aquilaClientService">The aquila client service.</param>
        /// <param name="logger">The logger.</param>
        public HealthCheckAquilaCommandHandler(
            IAquilaClientService aquilaClientService,
            ILogger<HealthCheckAquilaCommandHandler> logger)
            : base(logger)
        {
            this.aquilaClientService = aquilaClientService;
        }

        /// <inheritdoc/>
        public async Task<HealthCheckDetail> Handle([NotNull] HealthCheckAquilaCommand request, CancellationToken cancellationToken)
        {
            var healthStatus = await this.aquilaClientService.GetHealthStatusAsync(cancellationToken).ConfigureAwait(false);

            if (healthStatus.Status == HealthStatus.Healthy)
            {
                this.Logger.LogInformation("The Aquila client service is healthy");
            }
            else
            {
                this.Logger.LogError(healthStatus.Exception, "The Aquila client service has the status {Status}", healthStatus.Status);
            }

            return GetHealthCheckDetail(Resource, string.Empty, healthStatus.Status);
        }
    }
}

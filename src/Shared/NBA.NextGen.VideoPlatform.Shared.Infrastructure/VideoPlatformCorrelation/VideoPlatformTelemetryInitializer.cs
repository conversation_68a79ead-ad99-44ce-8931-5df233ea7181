// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformTelemetryInitializer.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.VideoPlatformCorrelation
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using Microsoft.ApplicationInsights.Channel;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Infrastructure.Logging;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;

    /// <summary>
    /// The Video platform telemetry initializer.
    /// </summary>
    public class VideoPlatformTelemetryInitializer : ITelemetryInitializer
    {
        /// <summary>
        /// The cloud role name key.
        /// </summary>
        private const string CloudRoleNameKey = "CloudRoleName";

        /// <summary>
        /// The correlation identifier key.
        /// </summary>
        private const string CorrelationIdKey = "VideoPlatformCorrelationId";

        /// <summary>
        /// The settings.
        /// </summary>
        private readonly LoggingSettings settings;

        /// <summary>
        /// video Platform Correlation Provider.
        /// </summary>
        private readonly IVideoPlatformCorrelationProviderFactory factory;

        /// <summary>
        /// Initializes a new instance of the <see cref="VideoPlatformTelemetryInitializer"/> class.
        /// </summary>
        /// <param name="factory">The factory.</param>
        /// <param name="options">Options.</param>
        public VideoPlatformTelemetryInitializer(IVideoPlatformCorrelationProviderFactory factory, [NotNull] IOptionsMonitor<LoggingSettings> options)
        {
            this.factory = factory;
            options.Required(nameof(options));
            this.settings = options.CurrentValue;
        }

        /// <summary>
        /// Gets the Correlation Id.
        /// </summary>
        public string CorrelationId { get; private set; }

        /// <summary>
        /// Initialize.
        /// </summary>
        /// <param name="correlationId">correlation id.</param>
        public void Initialize(string correlationId)
        {
            this.CorrelationId = correlationId;
        }

        /// <inheritdoc/>
        public void Initialize([NotNull] ITelemetry telemetry)
        {
            telemetry.Required(nameof(telemetry));

            var properties = telemetry.Context.GlobalProperties;

            // Add other custom properties
            foreach (var pair in this.settings.Where(s => !s.Key.EqualsIgnoreCase(CloudRoleNameKey)))
            {
                if (properties.ContainsKey(pair.Key))
                {
                    properties[pair.Key] = pair.Value;
                }
                else
                {
                    properties.Add(pair.Key, pair.Value);
                }
            }

            var provider = this.factory.GetProvider();

            if (provider == null || string.IsNullOrWhiteSpace(provider.CorrelationId))
            {
                return;
            }

            var correlationId = provider.CorrelationId;

            if (properties.ContainsKey(CorrelationIdKey))
            {
               properties[CorrelationIdKey] = correlationId;
            }
            else
            {
               properties.Add(CorrelationIdKey, correlationId);
            }
        }
    }
}

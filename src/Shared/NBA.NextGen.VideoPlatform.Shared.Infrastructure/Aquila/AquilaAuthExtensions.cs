
using System;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NBA.NextGen.Vendor.Api.MkAquila;
using Polly;
using Polly.Extensions.Http;
using Polly.Retry;

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Aquila
{
    public static class AquilaAuthExtensions
    {
        public static IServiceCollection AddDirectAquilaClient(this IServiceCollection services, [NotNull] IConfiguration configuration)
        {
            AquilaSettings aquilaSettings = new AquilaSettings();
            configuration.Bind("AquilaSettings", aquilaSettings);

            services.AddSingleton<AquilaTokenResponse>();
            services.AddTransient<AquilaAuthTokenHandler>();
            services.AddSingleton(aquilaSettings).AddSingleton<IClientFactory, ClientFactory>()
                .AddHttpClient("AquilaClient")
                .AddHttpMessageHandler<AquilaAuthTokenHandler>()
                .AddRetryPolicy(aquilaSettings);
            return services;
        }

        private static IHttpClientBuilder AddRetryPolicy(this IHttpClientBuilder builder, AquilaSettings settings)
        {
            AsyncRetryPolicy<HttpResponseMessage> policy = HttpPolicyExtensions.HandleTransientHttpError()
                .WaitAndRetryAsync(3, (int i) => TimeSpan.FromSeconds(Math.Pow(2.0, i)));
            builder.AddPolicyHandler(policy);
            return builder;
        }
    }
}
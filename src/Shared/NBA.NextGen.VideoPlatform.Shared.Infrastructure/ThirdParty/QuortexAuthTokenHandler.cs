using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using NBA.NextGen.Shared.Application.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty;

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Quortex
{
    public class QuortexAuthTokenHandler : DelegatingHandler
    {
        private readonly HttpClient _authClient;
        private QuortexTokenResponse _authInformation;
        private readonly IOptions<QuortexSettings> _quortexOptions;
        private readonly ILogger<QuortexAuthTokenHandler> _logger;

        public QuortexAuthTokenHandler(IOptions<QuortexSettings> options, ILogger<QuortexAuthTokenHandler> logger, QuortexTokenResponse cachedResponse)
        {
            _quortexOptions = options;
            _logger = logger;
            _authInformation = cachedResponse;
            _authClient = new HttpClient();
            _authClient.BaseAddress = new Uri(_quortexOptions.Value.TokenEndpoint);
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if(!IsValid(_authInformation?.access_token))
                await GetTokenAsync();
                
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _authInformation.access_token);
            var response = await base.SendAsync(request, cancellationToken);

            if (response.StatusCode == HttpStatusCode.Unauthorized || response.StatusCode == HttpStatusCode.Forbidden )
            {
                _logger.LogInformation("Quortex Authentication Handler: Request returned Unauthorized, attempting with a fresh token...");
                //Refresh token
                await GetTokenAsync(); 

                //Retry
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _authInformation.access_token);
                response = await base.SendAsync(request, cancellationToken);
            }
            _logger.LogInformation($"Base Request's Status Code: {response.StatusCode.ToString()}");
            return response;
        }

        // Retrieve a fresh token form Quortex
        private async Task<QuortexTokenResponse> GetTokenAsync() 
        {
            QuortexTokenRequest authRequest = new QuortexTokenRequest()
            {
                api_key_secret = _quortexOptions.Value.ApiKey,
            };

            var serializeOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            var jsonString = System.Text.Json.JsonSerializer.Serialize(authRequest, serializeOptions);
            var content = new StringContent(jsonString, Encoding.UTF8, "application/json");

            var response = await _authClient.PostAsync("", content);
            response.EnsureSuccessStatusCode();
            var token = await response.Content.ReadAsStringAsync(); 
            _authInformation = JsonConvert.DeserializeObject<QuortexTokenResponse>(token);
            return _authInformation;
            
        }

        private bool IsValid(string access_token)
        {
            if(!access_token.IsNullOrEmpty())
            {
                //Split and convert to base 64 encoded
                string base64 = access_token.Split('.')[1].Replace("-", "+").Replace("_", "/");
                int mod4 = base64.Length % 4;
                if (mod4 > 0)
                {
                    base64 += new String('=', 4 - mod4);
                }
                string base64_encoded = System.Text.Encoding.ASCII.GetString(Convert.FromBase64String(base64));

                //pull out each key value pair in the Jwt
                Dictionary<string, object> jsonDictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(base64_encoded);

                //Search for expiration field and check if its later than current time.
                if(jsonDictionary.ContainsKey("exp"))
                {
                    var exp_num = Convert.ToDouble(jsonDictionary["exp"].ToString());
                    DateTime exp = (new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)).AddSeconds(exp_num);
                    return exp.TimeOfDay > DateTime.Now.TimeOfDay;
                }

                //Missing Expiration date field
                _logger.LogInformation("Quortex Authentication Handler: Token lacks expiration date. Fetching new one...");
                return false;
            }
            
            //Jwt not currently assigned
            _logger.LogInformation("Quortex Authentication Handler: Token unassigned, fetching new one...");
            return false;
        }
    }
}
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NBA.NextGen.Vendor.Api.MKTvp;
using NBA.NextGen.VideoPlatform.Shared.Domain;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Tvp;

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure;

public static class TvpAuthExtensions
{
    public static IServiceCollection AddDirectMKTvpClient(this IServiceCollection services, [NotNull] IConfiguration configuration)
    {
        MKTvpSettings MKTvpSettings = new MKTvpSettings();
        configuration.Bind("MKTvpSettings", MKTvpSettings);
        services.Configure<TvpTokenOptions>(configuration.GetSection("MKTvpTokenOptions"));
        services.PostConfigure<TvpTokenOptions>(x => x.Certificate = configuration["tvp_mk_cert"]);
        services.PostConfigure<TvpTokenOptions>(x => x.CertificatePassword = configuration["tvp_mk_cert_pw"]);

        services.AddSingleton<TvpAuthResponse>();
        services.AddTransient<TvpCertificateAuthHandler>();
        services.AddSingleton(MKTvpSettings).AddSingleton<IClientFactory, ClientFactory>()
            .AddHttpClient("MKTvpClient")
            .AddHttpMessageHandler<TvpCertificateAuthHandler>();
        return services;
    }

}

using Microsoft.AspNetCore.Mvc;
using MediatR;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;
using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
using FluentValidation;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NBA.NextGen.VideoPlatform.TvpActor.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TeamsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<TeamsController> _logger;

        public TeamsController(IMediator mediator, ILogger<TeamsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new team
        /// </summary>
        /// <param name="request">Team creation request</param>
        /// <returns>Created team information</returns>
        [HttpPost]
        [ProducesResponseType(typeof(TeamCreationResponse), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(409)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateTeam([FromBody] CreateTeamRequest request)
        {
            try
            {
                _logger.LogInformation("Creating team with external ID: {ExternalId}", request.ExternalId);

                var teamInfo = new TvpEventTeamCreationInfo
                {
                    ExternalId = request.ExternalId,
                    Name = request.Name,
                    CallLetters = request.CallLetters,
                    Description = request.Description,
                    League = request.League,
                    LocationExternalId = request.LocationExternalId,
                    Conference = request.Conference,
                    Division = request.Division
                };

                var command = new UpsertTeamsCommand
                {
                    TvpEventTeamCreationInfos = new List<TvpEventTeamCreationInfo> { teamInfo },
                    EnableUpdate = false // Only create, don't update existing
                };

                await _mediator.Send(command);

                var response = new TeamCreationResponse
                {
                    ExternalId = teamInfo.ExternalId,
                    Status = "Created",
                    Message = $"Team '{request.Name}' created successfully"
                };

                _logger.LogInformation("Successfully created team: {ExternalId}", request.ExternalId);
                return CreatedAtAction(nameof(GetTeam), new { externalId = teamInfo.ExternalId }, response);
            }
            catch (ValidationException ex)
            {
                _logger.LogWarning("Validation error creating team: {Error}", ex.Message);
                return BadRequest(new { Error = ex.Message });
            }
            catch (ConflictException ex)
            {
                _logger.LogWarning("Team already exists: {ExternalId}", request.ExternalId);
                return Conflict(new { Error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating team: {ExternalId}", request.ExternalId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        /// <summary>
        /// Creates or updates multiple teams
        /// </summary>
        /// <param name="request">Bulk team creation request</param>
        /// <returns>Bulk operation result</returns>
        [HttpPost("bulk")]
        [ProducesResponseType(typeof(BulkTeamCreationResponse), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateTeamsBulk([FromBody] BulkCreateTeamsRequest request)
        {
            try
            {
                _logger.LogInformation("Creating {Count} teams in bulk", request.Teams.Count);

                var teamInfos = request.Teams.Select(team => new TvpEventTeamCreationInfo
                {
                    ExternalId = team.ExternalId,
                    Name = team.Name,
                    CallLetters = team.CallLetters,
                    Description = team.Description,
                    League = team.League,
                    LocationExternalId = team.LocationExternalId,
                    Conference = team.Conference,
                    Division = team.Division
                }).ToList();

                var command = new UpsertTeamsCommand
                {
                    TvpEventTeamCreationInfos = teamInfos,
                    EnableUpdate = request.EnableUpdate
                };

                await _mediator.Send(command);

                var response = new BulkTeamCreationResponse
                {
                    ProcessedCount = teamInfos.Count,
                    Status = "Completed",
                    Teams = teamInfos.Select(t => t.ExternalId).ToList()
                };

                _logger.LogInformation("Successfully processed {Count} teams", teamInfos.Count);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk team creation");
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        /// <summary>
        /// Placeholder for getting team (would need to implement the query side)
        /// </summary>
        [HttpGet("{externalId}")]
        public async Task<IActionResult> GetTeam(string externalId)
        {
            // This would require implementing a GetTeamQuery
            return Ok(new { ExternalId = externalId, Message = "Team retrieval not implemented yet" });
        }
    }

    // Request/Response models
    public class CreateTeamRequest
    {
        public string ExternalId { get; set; }
        public string Name { get; set; }
        public string CallLetters { get; set; }
        public string Description { get; set; }
        public string League { get; set; }
        public string LocationExternalId { get; set; }
        public string Conference { get; set; }
        public string Division { get; set; }
    }

    public class BulkCreateTeamsRequest
    {
        public List<CreateTeamRequest> Teams { get; set; }
        public bool EnableUpdate { get; set; }
    }

    public class TeamCreationResponse
    {
        public string ExternalId { get; set; }
        public string Status { get; set; }
        public string Message { get; set; }
    }

    public class BulkTeamCreationResponse
    {
        public int ProcessedCount { get; set; }
        public string Status { get; set; }
        public List<string> Teams { get; set; }
    }
}
